import React from 'react';
import CombinationScroller from './CombinationScroller';
import CombinationPopUpCard from './CombinationPopUpCard'; // Import the pop-up card
import useCombinationPopUp from './CombinationPopUpCard/hooks/useCombinationPopUp'; // Import the pop-up hook
import '../styles/CombinationList.css';

const CombinationList = ({
  loading,
  combinations,
  onSaveCombination,
  onDeleteCombination
}) => {
  const {
    selectedCombination,
    openPopUp,
    closePopUp,
  } = useCombinationPopUp();

  // Simple loading state
  if (loading) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <p>Loading combinations...</p>
      </div>
    );
  }

  if (combinations.length === 0) {
    return (
      <div className="no-combinations">
        <p>No combinations found. Try generating some combinations with the controls above or adjust your filters. You need at least 3 recipes to create combinations.</p>
      </div>
    );
  }

  return (
    <>
      <CombinationScroller
        combinations={combinations}
        onCardClick={openPopUp} // Pass the openPopUp function
      />
      {selectedCombination && (
        <CombinationPopUpCard
          combination={selectedCombination}
          onClose={closePopUp}
          onSaveCombination={onSaveCombination}
          onDeleteCombination={onDeleteCombination}
        />
      )}
    </>
  );
};

export default CombinationList;
