import json
from decimal import Decimal
from typing import List, Dict, Any, Union, Optional
from poireaux_app.models import Recipe, RecipeIngredient, Ingredient

# List of ingredients that can't be divided (like bay leaves)
# These will be rounded to the nearest whole number, but never less than 1 if quantity is > 0
INDIVISIBLE_INGREDIENTS = [
    'bay leaves',
    'Sardines'
]

def round_ingredient_quantity(
    recipe_ingredient: Union[RecipeIngredient, Dict[str, Any]]
) -> Union[float, None]:
    """
    Round an ingredient quantity based on its measurement type and allowed divisors.
    
    Args:
        recipe_ingredient: Either a RecipeIngredient instance or a dictionary with ingredient data
        
    Returns:
        Rounded quantity or None if no rounding is needed or possible
    """
    # Extract ingredient data from either object type
    if isinstance(recipe_ingredient, RecipeIngredient):
        ingredient = recipe_ingredient.ingredient
        current_quantity = float(recipe_ingredient.quantity)
    else:
        # For dictionaries (API input)
        current_quantity = float(recipe_ingredient.get('quantity', 0))
        ingredient_data = recipe_ingredient.get('ingredient_data', {})
        
        # If we just have the ID, fetch the ingredient from database
        if isinstance(ingredient_data, dict) and 'id' in ingredient_data:
            try:
                ingredient_id = ingredient_data['id']
                ingredient = Ingredient.objects.get(id=ingredient_id)
            except Ingredient.DoesNotExist:
                print(f"Ingredient with ID {ingredient_id} not found")
                return None
        else:
            # If we have a full ingredient object in the dictionary
            ingredient = ingredient_data
    
    # Get the ingredient name for checking against indivisible list
    ingredient_name = getattr(ingredient, 'name', '').lower()
    
    # Special handling for indivisible ingredients like bay leaves
    if ingredient_name in [name.lower() for name in INDIVISIBLE_INGREDIENTS]:
        if current_quantity > 0:
            # For bay leaves and similar, always round to the nearest whole number, but never less than 1
            rounded_quantity = max(1, round(current_quantity))
            
            # Only consider it a change if it's different from the current value
            if abs(rounded_quantity - current_quantity) > 0.01:
                print(f"Indivisible ingredient: Rounding {getattr(ingredient, 'name', 'Unknown')} from {current_quantity:.2f} to {rounded_quantity}")
                return rounded_quantity
            
            # If no rounding needed
            return None
    
    # Parse measurement type
    measurement_type = getattr(ingredient, 'divisible_by_measurement', 'unit')
    
    # Parse allowed divisors
    allowed_divisors = [1]  # Default
    try:
        divisible_by_int = getattr(ingredient, 'divisible_by_int', '[1]')
        if isinstance(divisible_by_int, str):
            allowed_divisors = json.loads(divisible_by_int)
            if not isinstance(allowed_divisors, list) or not all(isinstance(n, (int, float)) for n in allowed_divisors):
                print(f"Invalid format for divisible_by_int: {divisible_by_int}")
                allowed_divisors = [1]
    except Exception as e:
        print(f"Error parsing divisible_by_int: {e}")
    
    # Initialize tracking variables
    should_round = False
    rounded_quantity = current_quantity
    
    # Apply rounding logic based on measurement type
    if measurement_type in ['g', 'ml']:
        # For grams/ml, apply smarter rounding logic
        if allowed_divisors and allowed_divisors[0] > 0:
            base_unit = allowed_divisors[0]
            # Find the nearest multiple of base_unit
            rounded_quantity = round(current_quantity / base_unit) * base_unit
            
            # Only consider it a change if it's different from the current value
            if abs(rounded_quantity - current_quantity) > 0.01:
                should_round = True
                print(f"Rounding {getattr(ingredient, 'name', 'Unknown')} from {current_quantity:.3f} {measurement_type} to "
                      f"{rounded_quantity:.3f} {measurement_type}")
            
            # Never round to zero
            if rounded_quantity < 0.01:
                rounded_quantity = base_unit
                should_round = True
                print(f"Preventing zero value: Setting {getattr(ingredient, 'name', 'Unknown')} to minimum {base_unit} {measurement_type}")
    
    # Apply rounding for unit-type ingredients that are close to allowed divisions
    elif measurement_type in ['unit', 'tsp', 'tbsp', 'handful', 'sprig', 'pinch']:
        # Check if it's exactly divisible first
        is_exactly_divisible = False
        for divisor in allowed_divisors:
            if abs((current_quantity * divisor) % 1) < 1e-9:
                is_exactly_divisible = True
                break
        
        # If not exactly divisible, apply rounding if within margin
        if not is_exactly_divisible:
            # Use a margin and find the closest valid value
            rounding_margin = 0.3
            
            # Keep track of the closest valid rounded quantity
            closest_rounded_quantity = None
            smallest_difference = float('inf')
            
            for divisor in allowed_divisors:
                # Check all possible valid fractions with this divisor
                for i in range(int(current_quantity * divisor) + 2):
                    candidate_quantity = i / divisor
                    difference = abs(current_quantity - candidate_quantity)
                    
                    # If this candidate is within our margin and closer than previous closest
                    if difference <= rounding_margin and difference < smallest_difference:
                        # Skip zero values for unit-type measurements
                        if candidate_quantity < 0.001:
                            continue
                        
                        smallest_difference = difference
                        closest_rounded_quantity = candidate_quantity
            
            # If we found a valid rounded quantity within the margin
            if closest_rounded_quantity is not None:
                # Never round unit-type measurements to zero
                if closest_rounded_quantity < 0.001:
                    if allowed_divisors and allowed_divisors[0] > 0:
                        closest_rounded_quantity = 1 / allowed_divisors[0]
                    else:
                        closest_rounded_quantity = 1
                
                # Only consider significant changes
                if abs(closest_rounded_quantity - current_quantity) > 0.004:
                    rounded_quantity = closest_rounded_quantity
                    should_round = True
                    print(f"Rounding {getattr(ingredient, 'name', 'Unknown')} from {current_quantity:.3f} {measurement_type} to "
                          f"{rounded_quantity:.3f} {measurement_type} (within margin of {rounding_margin}, difference: {smallest_difference:.3f})")
    
    # Return the rounded value if rounding should be applied
    if should_round:
        return rounded_quantity
    else:
        return None

def round_recipe_ingredients(recipe_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Round all ingredients in a recipe based on their measurement types and allowed divisors.
    
    Args:
        recipe_data: Recipe data dictionary with recipe_ingredients
        
    Returns:
        List of dictionaries with ingredient IDs and their rounded quantities
    """
    updates = []
    recipe_ingredients = recipe_data.get('recipe_ingredients', [])
    
    if not recipe_ingredients:
        return updates
    
    for ingredient in recipe_ingredients:
        # print(f"Rounding ingredient: {ingredient}")
        rounded_quantity = round_ingredient_quantity(ingredient)
        
        if rounded_quantity is not None:
            # Add to the update list
            updates.append({
                'id': ingredient.get('id'),
                'quantity': rounded_quantity
            })
    
    return updates

def round_all_recipe_ingredients(recipes: List[Dict[str, Any]], include_originals: bool = False) -> Dict[int, List[Dict[str, Any]]]:
    """
    Process and round all ingredients across multiple recipes.
    Optionally includes original recipes in the rounding process.

    Args:
        recipes: List of recipe dictionaries.
        include_originals: If True, original recipes will also be processed.
                           Defaults to False (originals are skipped).

    Returns:
        Dictionary mapping recipe IDs to lists of ingredient updates.
        The structure is {recipe_id: [{'id': recipe_ingredient_id, 'quantity': new_quantity}, ...]}.
    """
    all_updates = {}
    
    for recipe in recipes:
        recipe_id = recipe.get('id')
        recipe_name = recipe.get('name', '')
        
        # Conditionally skip original recipes
        if '(original)' in recipe_name:
            print(f"Skipping rounding for original recipe (include_originals=False): {recipe_name}")
            continue
            
        if recipe_id:
            updates = round_recipe_ingredients(recipe)
            if updates:
                all_updates[recipe_id] = updates
    
    return all_updates 
