import { useState } from 'react';

export const useCombinationState = () => {
  const [combinations, setCombinations] = useState([]);
  const [loading, setLoading] = useState(false); // Initialize loading to false
  const [error, setError] = useState(null);
  const [totalCount, setTotalCount] = useState(0);

  return {
    combinations,
    setCombinations,
    loading,
    setLoading,
    error,
    setError,
    totalCount,
    setTotalCount,
  };
};