/* Styles for the wrapper inside CardData.jsx to place pantry and shopping lists side-by-side */
.card-data-lists-wrapper {
  display: flex;
  justify-content: space-between; /* Adjust as needed, e.g., space-around or remove for default packing */
  align-items: flex-start; /* Aligns the tops of the lists */
  gap: 20px; /* Adds space between the two lists */
  margin-top: 15px; /* Maintain or adjust top margin as needed */
}

/* Optional: If you want each list to take up a certain amount of space */
.card-data-lists-wrapper > .ingredients-section {
  flex: 1; /* Each list will take up equal space */
  /* Alternatively, set specific flex-basis or width percentages if one should be wider */
}