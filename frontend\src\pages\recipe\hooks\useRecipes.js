import { useState, useEffect, useCallback } from 'react';
import { ENDPOINTS } from '../../../config/api';

/**
 * Custom hook for managing recipe data fetching and state with caching
 * @returns {Object} Recipe state and operations
 */
export const useRecipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedRecipes, setExpandedRecipes] = useState({});
  const [lastFetchTime, setLastFetchTime] = useState(null);
  
  // Cache duration in milliseconds (5 minutes)
  const CACHE_DURATION = 5 * 60 * 1000;
  
  // Check if cached data is still valid
  const isCacheValid = useCallback(() => {
    if (!lastFetchTime) return false;
    const now = new Date().getTime();
    return (now - lastFetchTime) < CACHE_DURATION;
  }, [lastFetchTime, CACHE_DURATION]);
  
  // Get cached recipes from localStorage
  const getCachedRecipes = useCallback((cacheKey = 'recipesCache') => {
    try {
      const cachedData = localStorage.getItem(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
    } catch (err) {
      console.error('Failed to parse cached recipes:', err);
    }
    return null;
  }, []);
  
  // Save recipes to localStorage cache
  const cacheRecipes = useCallback((data, cacheKey = 'recipesCache') => {
    try {
      localStorage.setItem(cacheKey, JSON.stringify(data));
      setLastFetchTime(new Date().getTime());
    } catch (err) {
      console.error('Failed to cache recipes:', err);
    }
  }, []);
  
  // Fetch recipes from the API
  const fetchRecipes = useCallback(async (forceRefresh = false) => {
    // Return cached data if it's valid and we're not forcing a refresh
    if (!forceRefresh && isCacheValid()) {
      const cachedData = getCachedRecipes();
      if (cachedData && cachedData.length > 0) {
        setRecipes(cachedData);
        return cachedData;
      }
    }
    
    setIsLoading(true);
    setError(null);
    
    // Log fetch start time
    // const startTime = performance.now(); // Removed as it's not used
    
    try {
      const response = await fetch(ENDPOINTS.RECIPES.GET);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch recipes: ${response.status} ${response.statusText}`);
      }
      
      // --- Modified: Get response as text and parse manually ---
      const responseText = await response.text();
      const data = JSON.parse(responseText);
      // --- End Modification ---

      // Debug: Log recipe image data
      console.log('Fetched recipes with image data:', data.map(recipe => ({
        id: recipe.id,
        name: recipe.name,
        image: recipe.image
      })));

      // Log fetch end time and calculate duration
      // const endTime = performance.now(); // Removed as it's not used

      setRecipes(data);
      cacheRecipes(data);
      return data;
    } catch (err) {
      console.error("Error fetching recipes:", err);
      setError(err.message);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [isCacheValid, getCachedRecipes, cacheRecipes]);
  
  // Fetch recipes on component mount
  useEffect(() => {
    // Try to load from cache first
    const cachedData = getCachedRecipes();
    setRecipes(cachedData);
    // Fetch fresh data if cache is invalid or empty
    fetchRecipes(false);
  }, [fetchRecipes, getCachedRecipes, isCacheValid]);
  
  const toggleIngredients = (recipeId) => {
    setExpandedRecipes(prev => ({
      ...prev,
      [recipeId]: !prev[recipeId]
    }));
  };
  
  const handleDeleteRecipe = async (recipeId) => {
    try {
      const response = await fetch(ENDPOINTS.RECIPES.DELETE(recipeId), {
        method: 'DELETE',
      }); 
      if (!response.ok) {
        throw new Error('Failed to delete recipe');
      }
      setRecipes(prev => prev.filter(recipe => recipe.id !== recipeId));
      
      // Update the cache after deletion
      const updatedRecipes = recipes.filter(recipe => recipe.id !== recipeId);
      cacheRecipes(updatedRecipes);
    } catch (err) {
      console.error("Error deleting recipe:", err);
      alert(`Failed to delete recipe: ${err.message}`);
    }
  };

  return {
    recipes,
    isLoading,
    error,
    fetchRecipes,
    toggleIngredients,
    expandedRecipes,
    handleDeleteRecipe,
    setRecipes // Add setRecipes to the return object
  };
};

export default useRecipes; 
