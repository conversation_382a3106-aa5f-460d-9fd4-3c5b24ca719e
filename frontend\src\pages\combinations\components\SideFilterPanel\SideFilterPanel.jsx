import React, { useState } from 'react';
import './styles/SideFilterPanel.css';
import ServingsInput from '../CombinationControlPanel/components/ServingsInput';
import MonthInput from '../CombinationControlPanel/components/MonthInput';
import FindCombinationsButton from '../CombinationControlPanel/components/FindCombinationsButton';

const SideFilterPanel = ({
  servings,
  month,
  diet,
  loading,
  onServingsChange,
  onMonthChange,
  onDietChange,
  onFindAndSaveCombinations,
  isProduction,
  isMobile,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const togglePanel = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {/* Filter Toggle Button */}
      <button 
        className="filter-toggle-btn"
        onClick={togglePanel}
        aria-label="Toggle filters"
      >
        <span className="filter-icon">⚙️</span>
        Filters
      </button>

      {/* Overlay */}
      {isOpen && <div className="filter-overlay" onClick={togglePanel} />}

      {/* Side Panel for Desktop, Center Modal for Mobile */}
      <div className={`side-filter-panel ${isOpen ? 'open' : ''} ${isMobile ? 'mobile-modal' : ''}`}>
        <div className="filter-panel-header">
          <h3>Extended Filters</h3>
          <button 
            className="close-btn"
            onClick={togglePanel}
            aria-label="Close filters"
          >
            ✕
          </button>
        </div>

        <div className="filter-panel-content">
          <div className="filter-section">
            <h4>Select Products</h4>
            
            <div className="filter-group">
              <label>Servings</label>
              <ServingsInput servings={servings} onServingsChange={onServingsChange} />
            </div>

            <div className="filter-group">
              <label>Month</label>
              <MonthInput month={month} onMonthChange={onMonthChange} />
            </div>

            <div className="filter-group">
              <label>Diet Type</label>
              <select
                value={diet}
                onChange={(e) => onDietChange(e.target.value)}
                className="filter-select"
              >
                <option value="vegan">Vegan</option>
                <option value="vegetarian">Vegetarian</option>
                <option value="fish">Fish</option>
                <option value="meat">Meat</option>
              </select>
            </div>
          </div>

          {!isProduction && (
            <div className="filter-actions">
              <FindCombinationsButton
                loading={loading}
                onFindAndSave={onFindAndSaveCombinations}
                servings={servings}
                month={month}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default SideFilterPanel;
