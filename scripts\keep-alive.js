#!/usr/bin/env node

/**
 * Simple keep-alive script for Cloud Run instance
 * Run this on any server with: node keep-alive.js
 * Or set up as a cron job: */5 * * * * node /path/to/keep-alive.js
 */

const https = require('https');

const HEALTH_URL = 'https://poireaux-749928798411.europe-west1.run.app/api/health/';
const INTERVAL_MINUTES = 5;

function pingHealthEndpoint() {
    const startTime = Date.now();
    
    https.get(HEALTH_URL, (res) => {
        const duration = Date.now() - startTime;
        const timestamp = new Date().toISOString();
        
        if (res.statusCode === 200) {
            console.log(`✅ [${timestamp}] Health check successful (${res.statusCode}) - ${duration}ms`);
        } else {
            console.log(`❌ [${timestamp}] Health check failed (${res.statusCode}) - ${duration}ms`);
        }
    }).on('error', (err) => {
        const timestamp = new Date().toISOString();
        console.log(`❌ [${timestamp}] Health check error: ${err.message}`);
    });
}

// Run immediately
pingHealthEndpoint();

// Set up interval (only if running continuously)
if (process.argv.includes('--continuous')) {
    console.log(`🚀 Starting keep-alive service (pinging every ${INTERVAL_MINUTES} minutes)`);
    setInterval(pingHealthEndpoint, INTERVAL_MINUTES * 60 * 1000);
} else {
    console.log('💡 Single ping completed. Use --continuous flag for continuous monitoring.');
    console.log('💡 Or set up a cron job: */5 * * * * node /path/to/keep-alive.js');
}
