The "process recipe" button in the frontend/src/pages/recipe/index.jsx page initiates a comprehensive backend process for recipes.

Frontend (frontend/src/pages/recipe/index.jsx and frontend/src/pages/recipe/hooks/useRecipeProcess.js):

User Interaction: Clicking the "process recipe" button within the ServingSizeSelector component triggers its handleProcessRecipe prop.
Hook Call: This prop calls the executeProcessAllRecipes(recipes) function, which is provided by the useRecipeProcess custom hook.
Original Recipe Identification: The executeProcessAllRecipes function identifies a recipe with (original) in its name from the current recipe list. This "original recipe" is crucial for the backend operation.
API Request: A POST request is sent to the /api/recipes/process/ endpoint (defined as ENDPOINTS.RECIPES.PROCESS.CHECK_ALL in frontend/src/config/api.js). The request body includes the original_recipe_id of the identified original recipe.
State Management: The frontend manages isProcessingRecipes and processingError states to provide user feedback.
Backend (poireaux_app/urls.py and poireaux_app/api/views/recipe_views.py):

URL Routing: The POST request to /api/recipes/process/ is routed by poireaux_app/urls.py to the processRecipeFully function in poireaux_app/api/views/recipe_views.py.
processRecipeFully Function: This function orchestrates the following steps:
Step 1: Generate Serving Variations (lines 144-170): It attempts to generate serving variations (e.g., for 1-4 servings) for the original_recipe_id using recipe_crud_utils.generate_serving_variations. It handles cases where variations might already exist.
Step 2: Fetch All Recipes (lines 172-185): It retrieves all recipes in the system using recipe_crud_utils.get_all_recipes() for subsequent global processing.
Step 3: Round Ingredients (lines 188-198): It calls round_all_recipe_ingredients(all_recipes_data) to round ingredient quantities for all fetched recipes, ensuring consistent measurements.
Step 4: Check Divisibility (lines 201-210): It calls check_recipes_divisibility(all_recipes_data) to perform divisibility checks on the ingredients of all recipes.
Response: The backend returns a Response indicating completion, including details about generated variations and the results of global rounding and divisibility checks.
