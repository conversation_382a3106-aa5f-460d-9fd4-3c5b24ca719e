import { useState } from 'react';
import { API_BASE_URL } from '../../../../config/api';

export const MONTHS = [
  'january', 'february', 'march', 'april', 'may', 'june',
  'july', 'august', 'september', 'october', 'november', 'december'
];

export const MEASUREMENT_UNITS = ['unit', 'g', 'ml', 'tbsp', 'tsp', 'cup', 'piece', 'bunch', 'loaf', 'jar', 'bottle', 'sprig'];
export const INGREDIENT_TYPES = ['vegetable', 'herb', 'meat', 'dairy', 'oil', 'spice', 'grain', 'preserve'];
export const BOUGHT_BY_UNITS = ['bag', 'block', 'bottle', 'box', 'bunch', 'can', 'carton', 'container', 'g', 'jar', 'liter', 'loaf', 'ml', 'package', 'packet', 'piece', 'sprig', 'tap', 'tube', 'unit'];

export const useIngredientAdder = () => {
  const [name, setName] = useState('');
  const [seasons, setSeasons] = useState(
    MONTHS.reduce((acc, month) => ({ ...acc, [month]: false }), {})
  );
  const [divisibleByInteger, setDivisibleByInteger] = useState('');
  const [divisibleByMeasurement, setDivisibleByMeasurement] = useState(MEASUREMENT_UNITS[0]);
  const [type, setType] = useState(INGREDIENT_TYPES[0]);
  const [needsCalculating, setNeedsCalculating] = useState(false);
  const [boughtByAmount, setBoughtByAmount] = useState('');
  const [boughtBy, setBoughtBy] = useState(BOUGHT_BY_UNITS[0]);
  const [feedbackMessage, setFeedbackMessage] = useState('');

  const handleSeasonChange = (month) => {
    setSeasons(prevSeasons => ({
      ...prevSeasons,
      [month]: !prevSeasons[month]
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setFeedbackMessage('');

    if (!name.trim()) {
      setFeedbackMessage('Ingredient name is required.');
      return;
    }

    let parsedDivisibleByInt = null;
    if (divisibleByInteger.trim()) {
      try {
        const numbers = divisibleByInteger.split(',').map(s => parseInt(s.trim(), 10)).filter(n => !isNaN(n));
        if (numbers.length > 0) {
          parsedDivisibleByInt = `[${numbers.join(',')}]`;
        }
      } catch (error) {
        setFeedbackMessage('Invalid format for "Divisible By Integer". Please use comma-separated numbers (e.g., 1,2,4).');
        return;
      }
    }

    let parsedBoughtByAmount = [];
    if (needsCalculating && boughtByAmount.trim()) {
      try {
        const amounts = boughtByAmount.split(',').map(s => parseInt(s.trim(), 10)).filter(n => !isNaN(n) && n > 0);
        if (amounts.length > 0) {
          parsedBoughtByAmount = amounts;
        } else {
          setFeedbackMessage('Invalid format for "Bought By Amount". Please use comma-separated positive numbers (e.g., 250,400).');
          return;
        }
      } catch (error) {
        setFeedbackMessage('Invalid format for "Bought By Amount". Please use comma-separated positive numbers (e.g., 250,400).');
        return;
      }
    }

    // Calculate seasonality logic
    const selectedMonths = Object.values(seasons).filter(Boolean).length;
    const totalMonths = MONTHS.length;

    let finalSeasons = { ...seasons };
    let hasSeasonality = false;

    if (selectedMonths === 0) {
      // No months selected → available year-round
      hasSeasonality = false;
      finalSeasons = MONTHS.reduce((acc, month) => ({ ...acc, [month]: true }), {});
    } else if (selectedMonths === totalMonths) {
      // All months selected → available year-round
      hasSeasonality = false;
      finalSeasons = MONTHS.reduce((acc, month) => ({ ...acc, [month]: true }), {});
    } else {
      // Some months selected → seasonal
      hasSeasonality = true;
      finalSeasons = seasons;
    }

    const ingredientData = {
      name: name.trim(),
      ...finalSeasons,
      has_seasonality: hasSeasonality,
      divisible_by_int: parsedDivisibleByInt,
      divisible_by_measurement: divisibleByMeasurement,
      type: type,
      needs_calculating: needsCalculating,
      bought_by_amount: needsCalculating ? parsedBoughtByAmount : [],
      bought_by: needsCalculating ? boughtBy : 'na',
    };



    try {
      const response = await fetch(`${API_BASE_URL}/ingredients/create/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ingredientData),
      });

      if (response.ok) {
        setFeedbackMessage('Ingredient added successfully!');
        // Clear form
        setName('');
        setSeasons(MONTHS.reduce((acc, month) => ({ ...acc, [month]: false }), {}));
        setDivisibleByInteger('');
        setDivisibleByMeasurement(MEASUREMENT_UNITS[0]);
        setType(INGREDIENT_TYPES[0]);
        setNeedsCalculating(false);
        setBoughtByAmount('');
        setBoughtBy(BOUGHT_BY_UNITS[0]);
      } else {
        const errorData = await response.json();
        setFeedbackMessage(`Error adding ingredient: ${errorData.detail || response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to submit ingredient:', error);
      setFeedbackMessage('Failed to submit ingredient. See console for details.');
    }
  };

  return {
    name,
    setName,
    seasons,
    handleSeasonChange,
    divisibleByInteger,
    setDivisibleByInteger,
    divisibleByMeasurement,
    setDivisibleByMeasurement,
    type,
    setType,
    needsCalculating,
    setNeedsCalculating,
    boughtByAmount,
    setBoughtByAmount,
    boughtBy,
    setBoughtBy,
    feedbackMessage,
    handleSubmit,
  };
};
