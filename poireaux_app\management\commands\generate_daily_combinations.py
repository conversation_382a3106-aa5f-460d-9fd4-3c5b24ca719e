from django.core.management.base import BaseCommand
from django.utils import timezone
from poireaux_app.services.combinations.combination_service import generate_unique_combinations

class Command(BaseCommand):
    help = 'Generates daily unique recipe combinations'

    def handle(self, *args, **options):
        self.stdout.write('Starting daily combination generation...')
        current_month = timezone.now().month
        generate_unique_combinations(servings=1, season_month=current_month)
        self.stdout.write(self.style.SUCCESS('Successfully generated daily combinations.'))
