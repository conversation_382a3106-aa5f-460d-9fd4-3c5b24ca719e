import React from 'react';
import '../styles/MonthSelection.css';

// Format seasonality display for ingredients
export const formatSeasonality = (ingredient) => {
  if (!ingredient.has_seasonality) {
    return 'No seasonality';
  }
  
  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  
  // Map DB month names to month numbers (0-11)
  const monthMap = {
    january: 0, february: 1, march: 2, april: 3, may: 4, june: 5,
    july: 6, august: 7, september: 8, october: 9, november: 10, december: 11
  };
  
  return (
    <div className="season-indicator">
      {monthNames.map((name, index) => {
        const dbMonthName = Object.keys(monthMap).find(key => monthMap[key] === index);
        const isActive = ingredient[dbMonthName];
        const monthNumber = index + 1; // 1-12
        
        return (
          <div className="month-indicator-container" key={name}>
            <span 
              className={`month-indicator ${isActive ? 'month-active-' + monthNumber : ''}`}
              title={`${name}: ${isActive ? 'In season' : 'Not in season'}`}
            ></span>
            <span className="month-tooltip">{name}</span>
          </div>
        );
      })}
    </div>
  );
};

const SeasonalityFormatter = ({ ingredient }) => {
  return formatSeasonality(ingredient);
};

export default SeasonalityFormatter; 