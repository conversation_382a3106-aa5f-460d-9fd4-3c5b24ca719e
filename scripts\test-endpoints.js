#!/usr/bin/env node

/**
 * Test script to compare performance between fast and full endpoints
 */

const https = require('https');

const BASE_URL = 'https://poireaux-749928798411.europe-west1.run.app/api';
const ENDPOINTS = {
  health: `${BASE_URL}/health/`,
  fast: `${BASE_URL}/combinations/fast/?servings=1&diet=vegan`,
  full: `${BASE_URL}/combinations/get/?servings=1&diet=vegan`
};

function testEndpoint(name, url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const duration = Date.now() - startTime;
        const dataSize = Buffer.byteLength(data, 'utf8');
        
        try {
          const parsed = JSON.parse(data);
          const resultCount = Array.isArray(parsed) ? parsed.length : 
                             (parsed.results ? parsed.results.length : 0);
          
          resolve({
            name,
            url,
            status: res.statusCode,
            duration,
            dataSize,
            resultCount,
            success: res.statusCode === 200
          });
        } catch (err) {
          resolve({
            name,
            url,
            status: res.statusCode,
            duration,
            dataSize,
            resultCount: 0,
            success: false,
            error: 'Invalid JSON'
          });
        }
      });
    }).on('error', (err) => {
      reject({
        name,
        url,
        error: err.message,
        success: false
      });
    });
  });
}

async function runTests() {
  console.log('🧪 Testing API endpoints performance...\n');
  
  const tests = [
    { name: 'Health Check', url: ENDPOINTS.health },
    { name: 'Fast Endpoint', url: ENDPOINTS.fast },
    { name: 'Full Endpoint', url: ENDPOINTS.full }
  ];
  
  for (const test of tests) {
    try {
      console.log(`⏳ Testing ${test.name}...`);
      const result = await testEndpoint(test.name, test.url);
      
      if (result.success) {
        console.log(`✅ ${result.name}:`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Duration: ${result.duration}ms`);
        console.log(`   Data Size: ${(result.dataSize / 1024).toFixed(2)}KB`);
        if (result.resultCount !== undefined) {
          console.log(`   Results: ${result.resultCount} items`);
        }
      } else {
        console.log(`❌ ${result.name}: ${result.error || 'Failed'}`);
      }
      console.log('');
    } catch (err) {
      console.log(`❌ ${test.name}: ${err.error || err.message}`);
      console.log('');
    }
  }
  
  console.log('🏁 Performance test completed!');
  console.log('\n💡 Tips:');
  console.log('   - Fast endpoint should be significantly faster');
  console.log('   - Fast endpoint should return smaller data size');
  console.log('   - Both should return the same number of results');
}

runTests().catch(console.error);
