.seasonality-display {
  margin-bottom: 20px;
}

.seasonality-label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.seasonality-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

/* Sizes */
.seasonality-display.small .seasonality-grid {
  gap: 2px;
}

.seasonality-display.large .seasonality-grid {
  gap: 6px;
}

.month-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 8px 0;
  font-size: 0.8rem;
  font-weight: 500;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: default;
  text-align: center;
}

.seasonality-display.small .month-box {
  padding: 4px 0;
  font-size: 0.7rem;
}

.seasonality-display.large .month-box {
  padding: 12px 0;
  font-size: 0.9rem;
}

.month-box:hover {
  transform: scale(1.05);
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Month styling based on season */
.month-box.winter {
  background-color: #e3f2fd;
  color: #1565c0;
}

.month-box.spring {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.month-box.summer {
  background-color: #fffde7;
  color: #f57f17;
}

.month-box.fall {
  background-color: #fff3e0;
  color: #e65100;
}

/* In season styling */
.month-box.in-season {
  opacity: 1;
  font-weight: 700;
}

/* Not in season styling */
.month-box.off-season {
  opacity: 0.5;
  text-decoration: line-through;
}

/* Current month styling */
.month-box.current {
  border: 2px solid #2196f3;
  box-shadow: 0 0 4px rgba(33, 150, 243, 0.5);
}

/* Season legend */
.season-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
  justify-content: center;
}

.season-legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
}

.season-color-box {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.season-color-box.winter {
  background-color: #e3f2fd;
  border: 1px solid #1565c0;
}

.season-color-box.spring {
  background-color: #e8f5e9;
  border: 1px solid #2e7d32;
}

.season-color-box.summer {
  background-color: #fffde7;
  border: 1px solid #f57f17;
}

.season-color-box.fall {
  background-color: #fff3e0;
  border: 1px solid #e65100;
}

/* Responsive styling */
@media (max-width: 768px) {
  .seasonality-grid {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }
  
  .season-legend {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .seasonality-grid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
} 