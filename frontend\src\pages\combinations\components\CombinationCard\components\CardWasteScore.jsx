import React, { useState, useEffect } from 'react';
import '../../../../../styles/WasteScoreIndicator.css'; // Adjusted path
import Tooltip from '../../../../../components/Tooltip'; // Adjusted path

/**
 * A component that visualizes waste scores with color-coded indicators and optional detailed breakdown
 * 
 * @param {Object} props
 * @param {number} props.score - The waste score to display
 * @param {boolean} props.showLabel - Whether to show the "Waste Score" label
 * @param {string} props.size - Size of the indicator ('small', 'medium', 'large')
 * @param {Object} props.combination - The combination containing waste information
 * @param {boolean} props.isCacheValid - Whether the combination's cache is valid
 */
function WasteScoreIndicator({ score, showLabel = true, size = 'medium', combination, isCacheValid = true }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [ingredientWaste, setIngredientWaste] = useState([]);
  
  // Determine the color based on the waste score
  const getColor = (score) => {
    if (score <= 25) return 'green'; // Excellent
    if (score <= 50) return 'lightgreen'; // Good
    if (score <= 100) return 'yellow'; // Moderate
    if (score <= 150) return 'orange'; // Poor
    return 'red'; // Bad
  };

  // Get a text description of the waste score
  const getDescription = (score) => {
    if (score <= 25) return 'Excellent';
    if (score <= 50) return 'Good';
    if (score <= 100) return 'Moderate';
    if (score <= 150) return 'Poor';
    return 'High Waste';
  };
  
  // Transform the ingredient_waste object from the API
  useEffect(() => {
    if (combination?.ingredient_waste && Object.keys(combination.ingredient_waste).length > 0) {
      console.log('Using real ingredient waste data');
      const formattedWaste = Object.values(combination.ingredient_waste).map(item => ({
        name: item.ingredient_name,
        amount: item.buy_amount,
        needed: item.needed_quantity,
        wastePercentage: item.waste_percentage,
        unit: item.unit
      }));
      
      setIngredientWaste(formattedWaste);
    } else if (combination) {
      console.log('Using mock ingredient waste data');
      // Mock data for ingredient breakdown for saved combinations that don't have waste data
      const mockData = [
        { name: 'Onions', amount: 100, needed: 85, wastePercentage: 15, unit: 'g' },
        { name: 'Tomatoes', amount: 500, needed: 450, wastePercentage: 10, unit: 'g' },
        { name: 'Potatoes', amount: 1000, needed: 800, wastePercentage: 20, unit: 'g' },
        { name: 'Carrots', amount: 300, needed: 270, wastePercentage: 10, unit: 'g' },
      ];
      setIngredientWaste(mockData);
    }
  }, [combination]);

  const color = getColor(score);
  const description = getDescription(score);
  
  // Tooltip content explaining what the score represents
  const tooltipContent = "Waste score is calculated only for ingredients that require purchase. Common items like salt and spices are excluded.";

  // Helper function to get a color based on waste percentage
  const getColorForPercentage = (percentage) => {
    if (percentage <= 10) return '#28a745'; // green
    if (percentage <= 20) return '#8bc34a'; // light green
    if (percentage <= 30) return '#ffc107'; // yellow
    if (percentage <= 40) return '#fd7e14'; // orange
    return '#dc3545'; // red
  };

  // Helper function to calculate total waste savings
  const calculateWasteSavings = (ingredients) => {
    return ingredients.reduce((total, ingredient) => {
      return total + (ingredient.amount - ingredient.needed);
    }, 0);
  };

  // Sort ingredients by waste percentage (highest first)
  const sortedIngredients = [...ingredientWaste].sort((a, b) => 
    b.wastePercentage - a.wastePercentage
  );

  return (
    <div className={`waste-score-indicator ${size}`}>
      <div className="waste-score-header">
        {showLabel && (
          <span className="waste-score-label">
            Waste Score:
            <Tooltip content={tooltipContent}>
              <span className="info-icon">ⓘ</span>
            </Tooltip>
          </span>
        )}
        
        <div className="waste-score-display">
          <div className={`waste-score-circle ${color} ${!isCacheValid ? 'outdated' : ''}`}>
            {!isCacheValid && <span className="outdated-indicator">!</span>}
          </div>
          <div className="waste-score-text">
            <span className="waste-score-value">{score.toFixed(1)}%</span>
            <span className="waste-score-description">
              {description}
              {!isCacheValid && <span className="outdated-label"> (Outdated)</span>}
            </span>
          </div>
        </div>
        
        {combination && (
          <button 
            className="toggle-btn"
            onClick={() => setIsExpanded(!isExpanded)}
            aria-expanded={isExpanded}
          >
            {isExpanded ? 'Hide Details' : 'Show Details'}
          </button>
        )}
      </div>
      
      {isExpanded && combination && (
        <div className="waste-breakdown-details">
          <div className="waste-explanation">
            <p>
              The waste score indicates how much of purchased ingredients may be wasted.
              Lower scores are better (less waste). Scores are calculated based on 
              packaging sizes versus recipe needs.
              <span className="calculation-note">Note: Common ingredients like salt and spices are excluded from calculations.</span>
            </p>
          </div>
          
          <div className="ingredient-waste-list">
            <h5>Ingredient Breakdown:</h5>
            {sortedIngredients.length > 0 ? (
              sortedIngredients.map((ingredient, index) => (
                <div key={index} className="ingredient-waste-item">
                  <div className="ingredient-info">
                    <span className="ingredient-name">{ingredient.name}</span>
                    <span className="ingredient-waste-percentage">
                      {ingredient.wastePercentage.toFixed(1)}% waste
                    </span>
                  </div>
                  <div className="waste-bar-container">
                    <div className="waste-bar-background">
                      <div 
                        className="waste-bar-fill"
                        style={{ 
                          width: `${100 - ingredient.wastePercentage}%`,
                          backgroundColor: getColorForPercentage(ingredient.wastePercentage) 
                        }}
                      ></div>
                    </div>
                    <div className="waste-bar-labels">
                      <span>Used: {ingredient.needed.toFixed(1)}{ingredient.unit}</span>
                      <span>Package: {ingredient.amount.toFixed(1)}{ingredient.unit}</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p>No ingredient waste data available</p>
            )}
          </div>
          
          <div className="total-waste-summary">
            <p>
              <strong>Potential savings:</strong> By optimizing these recipes together,
              you could reduce waste by approximately {calculateWasteSavings(sortedIngredients).toFixed(1)}g
              of ingredients compared to cooking these recipes separately.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

export default WasteScoreIndicator;