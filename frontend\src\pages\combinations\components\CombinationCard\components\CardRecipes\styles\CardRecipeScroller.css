/* Styles for CardRecipeScroller.jsx */

.recipe-navigation { /* Existing class, adding styles */
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.recipe-navigation-status {
  font-size: 0.9em;
  color: #333;
  text-align: center;
}

/* Styles for buttons can be shared or specific if needed */
.recipe-navigation button {
  /* Basic button styling, can be enhanced */
  padding: 5px 10px;
  border: 1px solid #ccc;
  background-color: #f8f8f8;
  cursor: pointer;
}

.recipe-navigation button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
