.recipe-header {
  margin-bottom: 10px; /* Or adjust as needed */
  padding-bottom: 10px;
  border-bottom: 1px solid #eee; /* Optional: adds a separator */
}

.recipe-header h3 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 1.4em; /* Slightly larger for emphasis */
  color: #333;
}

.recipe-meta {
  display: flex;
  flex-wrap: wrap; /* Allow items to wrap on smaller screens */
  gap: 10px; /* Spacing between meta items */
  font-size: 0.9em;
  color: #666;
}

.recipe-meta span {
  padding: 3px 6px;
  border-radius: 4px;
  background-color: #f0f0f0; /* Light background for tags */
}

.recipe-type {
  /* Specific styles if needed */
}

.recipe-servings {
  /* Specific styles if needed */
}

.recipe-divisibility {
  font-weight: bold;
}