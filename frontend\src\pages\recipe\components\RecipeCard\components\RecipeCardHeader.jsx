import React from 'react';
import '../styles/RecipeCardHeader.css';

const RecipeCardHeader = ({ recipe }) => {
  if (!recipe) {
    return null; // Or some loading/placeholder
  }

  return (
    <div className="recipe-header">
      <h3>{recipe.name}</h3>
      <div className="recipe-meta">
        {recipe.type && <span className="recipe-type">{recipe.type}</span>}
        {recipe.servings && <span className="recipe-servings">{recipe.servings} servings</span>}
        <span
          className="recipe-divisibility"
          style={{ color: recipe.divisible ? 'green' : 'red' }}
        >
          {recipe.divisible ? 'Divisible' : 'Not Divisible'}
        </span>
      </div>
    </div>
  );
};

export default RecipeCardHeader;