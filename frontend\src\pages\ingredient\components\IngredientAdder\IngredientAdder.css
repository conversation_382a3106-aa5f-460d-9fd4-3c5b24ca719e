.ingredient-adder-container {
  padding: 15px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ingredient-adder-container form {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
  display: flex;
  flex-direction: column;
}

.ingredient-adder-container h3 {
  margin-top: 0;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 15px;
  flex-shrink: 0;
  font-weight: 600;
  font-size: 1.1rem;
  background-color: #f0f7ff;
  padding: 8px 12px;
  border-radius: 6px;
  border-bottom: 2px solid #e0e0e0;
}

.ingredient-adder-container div:not(.month-checkbox-container):not(.month-checkbox-item):not(.needs-calculating-container) {
  margin-bottom: 10px;
}

.ingredient-adder-container label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.ingredient-adder-container input[type="text"],
.ingredient-adder-container input[type="number"],
.ingredient-adder-container select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.9rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.ingredient-adder-container input[type="text"]:focus,
.ingredient-adder-container input[type="number"]:focus,
.ingredient-adder-container select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.ingredient-adder-container input[type="checkbox"] {
  margin-right: 8px; /* Increased margin for checkbox */
  transform: scale(1.2); /* Slightly larger checkbox */
}

.ingredient-adder-container .month-checkbox-container {
  display: grid; /* Use grid for better alignment */
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* Responsive grid */
  gap: 10px;
  margin-bottom: 15px; /* Add margin below the container */
}

.ingredient-adder-container .month-checkbox-item {
  display: flex;
  align-items: center;
  background-color: #fff; /* Add background to items for clarity */
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.ingredient-adder-container .month-checkbox-item label {
  font-weight: normal;
  margin-left: 5px;
  margin-bottom: 0;
  color: #333;
  cursor: pointer; /* Add cursor pointer to label */
}

.ingredient-adder-container button[type="submit"] {
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  width: 100%;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
}

.ingredient-adder-container button[type="submit"]:hover {
  background-color: #388e3c;
}

.success-message {
  color: #155724; /* Darker green for success text */
  background-color: #d4edda; /* Lighter green background */
  border: 1px solid #c3e6cb; /* Green border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center; /* Center message */
}

.error-message {
  color: #721c24; /* Darker red for error text */
  background-color: #f8d7da; /* Lighter red background */
  border: 1px solid #f5c6cb; /* Red border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center; /* Center message */
}

/* Styles for new button-like checkboxes */
.season-toggle-button,
.needs-calculating-toggle-button {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  font-size: 0.75rem;
  text-align: center;
}

.season-toggle-button.selected,
.needs-calculating-toggle-button.selected {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.season-toggle-button:not(.selected):hover,
.needs-calculating-toggle-button:not(.selected):hover {
  background-color: #f0f0f0;
}

.month-buttons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.needs-calculating-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.needs-calculating-container label {
  margin-right: 8px;
  margin-bottom: 0;
  font-size: 0.9rem;
}
