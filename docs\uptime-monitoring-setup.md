# Setting Up External Monitoring for Cloud Run Keep-Alive

## Option 1: UptimeRobot (Free)

1. Go to [UptimeRobot](https://uptimerobot.com/)
2. Create a free account
3. Click "Add New Monitor"
4. Configure:
   - **Monitor Type**: HTTP(s)
   - **Friendly Name**: Poireaux Keep-Alive
   - **URL**: `https://poireaux-************.europe-west1.run.app/api/health/`
   - **Monitoring Interval**: 5 minutes
   - **Monitor Timeout**: 30 seconds
5. Save the monitor

## Option 2: Pingdom

1. Go to [Pingdom](https://www.pingdom.com/)
2. Create an account (has free tier)
3. Add new check:
   - **Name**: Poireaux Keep-Alive
   - **URL**: `https://poireaux-************.europe-west1.run.app/api/health/`
   - **Check interval**: 5 minutes

## Option 3: StatusCake

1. Go to [StatusCake](https://www.statuscake.com/)
2. Create free account
3. Add new test:
   - **Test Name**: Poireaux Keep-Alive
   - **Website URL**: `https://poireaux-************.europe-west1.run.app/api/health/`
   - **Check Rate**: 5 minutes

## Benefits of External Monitoring

- **Free options available**
- **No Google Cloud dependencies**
- **Additional monitoring features** (uptime alerts, performance metrics)
- **Easy setup** - no command line required
- **Email/SMS alerts** if your service goes down
