# Generated by Django 5.1.7 on 2025-04-11 09:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('poireaux_app', '0013_remove_recipeingredient_servings'),
    ]

    operations = [
        migrations.CreateModel(
            name='Combination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=200)),
                ('waste_score', models.FloatField(default=0.0)),
                ('servings', models.IntegerField(default=1)),
                ('is_divisible', models.BooleanField(default=True)),
                ('seasonality_months', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('recipes', models.ManyToManyField(related_name='combinations', to='poireaux_app.recipe')),
            ],
        ),
    ]
