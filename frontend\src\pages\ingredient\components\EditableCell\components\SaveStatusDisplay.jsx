import React from 'react';

const getSaveStatusInfo = (saveState) => {
  let statusClass = '';
  let statusText = '';
  if (saveState === 'saving') {
    statusClass = 'status-saving';
    statusText = 'Saving...';
  } else if (saveState === 'success') {
    statusClass = 'status-success';
    statusText = 'Saved!';
  } else if (saveState === 'error') {
    statusClass = 'status-error';
    statusText = 'Error!';
  }
  return { statusClass, statusText };
};

const SaveStatusDisplay = ({ saveState }) => {
  const { statusText } = getSaveStatusInfo(saveState);

  if (!statusText) {
    return null;
  }

  return (
    <div className="save-status">{statusText}</div>
  );
};

export { getSaveStatusInfo };
export default SaveStatusDisplay;