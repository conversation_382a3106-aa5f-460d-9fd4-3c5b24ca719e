import React, { useEffect } from 'react';
import { useSavedCombinations } from './hooks/useSavedCombinations';
import CombinationGrid from '../combinations/components/CombinationGrid';
import './styles/savedCombinations.css';

const SavedCombinationsPage = () => {
    const {
        savedCombinations,
        loading,
        error,
        fetchSavedCombinations,
        deleteSavedCombination,
    } = useSavedCombinations();

    useEffect(() => {
        fetchSavedCombinations();
    }, [fetchSavedCombinations]);

    const combinations = savedCombinations.map(sc => ({ ...sc.combination, savedCombinationId: sc.id }));

    return (
        <div className="saved-combinations-page">
            <h1>My Saved Combinations</h1>
            {error && <p className="error-message">Error: {error.message}</p>}
            <CombinationGrid
                loading={loading}
                combinations={combinations}
                onDeleteCombination={deleteSavedCombination}
            />
        </div>
    );
};

export default SavedCombinationsPage;
