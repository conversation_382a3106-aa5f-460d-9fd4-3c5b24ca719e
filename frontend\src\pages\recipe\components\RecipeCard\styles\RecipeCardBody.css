.recipe-body {
  padding: 10px 0; /* Add some vertical padding */
}

.recipe-seasonality {
  margin-bottom: 15px;
}

.recipe-seasonality .no-seasonality {
  font-style: italic;
  color: #888;
  font-size: 0.9em;
}

.recipe-ingredients {
  margin-bottom: 15px;
}

.recipe-ingredients h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1.1em;
  color: #444;
}

.recipe-ingredients ul {
  list-style-type: disc; /* Or 'none' if you prefer no bullets */
  padding-left: 20px; /* Indent list items */
  margin: 0;
}

.recipe-ingredients li {
  margin-bottom: 5px;
  font-size: 0.95em;
  color: #555;
}

.recipe-instructions {
  margin-bottom: 15px;
}

.recipe-instructions .instructions-header {
  display: flex; /* Align title and arrow */
  justify-content: space-between; /* Space out title and arrow */
  align-items: center;
  cursor: pointer;
  padding: 8px 0; /* Add some padding to the clickable area */
  user-select: none;
  border-bottom: 1px solid #f0f0f0; /* Optional separator */
  margin-bottom: 8px; /* Space before content if expanded */
}

.recipe-instructions .instructions-header h4 {
  margin: 0;
  font-size: 1.1em;
  color: #444;
}

.recipe-instructions .dropdown-arrow {
  font-size: 0.9em;
  color: #777;
  transition: transform 0.2s ease-in-out;
}

/* If you want the arrow to rotate, you might need to adjust this
   or handle it with a class change in JS if 'instructionsExpanded' is true */
/* .recipe-instructions .instructions-header.expanded .dropdown-arrow {
  transform: rotate(180deg);
} */

.recipe-instructions .instructions-content {
  padding-top: 8px;
  animation: fadeIn 0.3s ease;
  font-size: 0.95em;
  color: #555;
  line-height: 1.5;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}