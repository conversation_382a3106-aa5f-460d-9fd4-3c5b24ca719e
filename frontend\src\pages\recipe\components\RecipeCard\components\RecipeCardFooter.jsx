import React from 'react';
import '../styles/RecipeCardFooter.css';

const RecipeCardFooter = ({ recipe, handleEditClick, handleDeleteRecipe }) => {
  if (!recipe) {
    return null;
  }

  return (
    <div className="recipe-footer">
      <div className="recipe-diet">
        {recipe.diet && <span className="diet-tag">{recipe.diet}</span>}
      </div>
      
      <div className="recipe-actions">
        <button 
          className="edit-btn"
          onClick={() => handleEditClick(recipe)}
        >
          Edit
        </button>
        <button 
          className="delete-btn"
          onClick={() => handleDeleteRecipe(recipe.id)}
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export default RecipeCardFooter;