"""Service for generating and uploading recipe images using OpenAI and Supabase."""
import logging
import os
import uuid
import requests
from openai import OpenAI
from supabase import create_client, Client

logger = logging.getLogger(__name__)

def generate_and_upload_recipe_image(recipe_name, recipe_instructions=None):
    """
    Generates an image for a recipe using OpenAI's gpt-4.1-mini model
    and uploads it to Supabase Storage.

    Args:
        recipe_name (str): The name of the recipe
        recipe_instructions (str, optional): The cooking instructions for the recipe
    """
    logger.info("Starting image generation process for recipe: %s", recipe_name)

    try:
        # --- 1. Get credentials from environment ---
        # The OpenAI client implicitly uses the OPENAI_API_KEY env var.
        # Let's ensure our key is named correctly for the client to find it.
        # For clarity, we'll still get it here to check for its existence.
        openai_api_key = os.environ.get("IMAGE_GENERATION_KEY_OPENAI")
        supabase_url = os.environ.get("DJANGO_SUPABASE_URL")
        supabase_key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
        supabase_bucket = os.environ.get("SUPABASE_RECIPE_BUCKET", "recipe-images")

        if not all([openai_api_key, supabase_url, supabase_key]):
            logger.error("Missing required environment variables for image generation/upload.")
            raise ValueError(
                "Missing IMAGE_GENERATION_KEY_OPENAI, DJANGO_SUPABASE_URL, "
                "or SUPABASE_SERVICE_ROLE_KEY from environment variables."
            )

        # --- 2. Generate Image with GPT-Image-1 ---
        logger.info("Generating image with GPT-Image-1...")
        # The client will automatically use the key from the environment.
        client = OpenAI(api_key=openai_api_key)

        clean_recipe_name = recipe_name.replace(' (original)', '').strip()

        # Build the base prompt
        prompt = (
            f"Create a professional food photograph of {clean_recipe_name} in the distinctive style of Yotam Ottolenghi's cookbooks. "
        )

        # Add recipe instructions context if available
        if recipe_instructions:
            prompt += (
                f"Based on these cooking instructions, show the dish as it would appear when properly prepared: "
                f"{recipe_instructions[:500]}{'...' if len(recipe_instructions) > 500 else ''} "
            )

        # Add detailed styling requirements
        prompt += (
            f"The image should feature: "
            f"• A beautifully plated {clean_recipe_name} as the central focus, shot from directly above (bird's eye view) "
            f"• Rich, vibrant colors with excellent contrast - deep jewel tones, golden browns, fresh greens "
            f"• The dish should be presented in an elegant ceramic bowl, cast iron pan, or rustic earthenware vessel "
            f"• Soft, even lighting that creates gentle shadows and highlights the textures and colors of the ingredients "
            f"• A clean, neutral gray background (hex #E5E5E5) that doesn't compete with the food "
            f"• Visible texture details: glistening oils, fresh herb garnishes, varied ingredient sizes and shapes "
            f"• Composition that fills most of the frame while leaving some breathing room around the edges "
            f"• Professional food styling with ingredients artfully arranged, not perfectly uniform "
            f"• High-end cookbook photography quality with sharp focus and rich color saturation "
            f"• Aspect ratio of 9:16 (portrait orientation) "
            f"• The overall aesthetic should be warm, inviting, and sophisticated - capturing the Mediterranean/Middle Eastern influence typical of Ottolenghi's cuisine "
            f"• No text, watermarks, or branding visible in the image"
        )

        response = client.images.generate(
            model="gpt-image-1",
            prompt=prompt,
            size="1024x1536",  # 2:3 aspect ratio (closest to 9:16 that's supported)
            n=1
        )

        # Extract the image URL and download it
        if not response.data or len(response.data) == 0:
            raise ValueError("Image generation failed, no image data returned from API.")

        # Debug: Show only essential info about the response
        first_item = response.data[0]

        # Check for URL attribute
        image_url = getattr(first_item, 'url', None)
        print(f"--- IMAGE DEBUG: url attribute: {image_url} ---")

        if not image_url:
            # Check if it has b64_json instead (like DALL-E)
            b64_data = getattr(first_item, 'b64_json', None)
            print(f"--- IMAGE DEBUG: b64_json found: {b64_data is not None} ---")

            if b64_data:
                # Convert base64 to image data directly
                import base64
                image_data = base64.b64decode(b64_data)
                print(f"--- IMAGE DEBUG: Using b64_json data ---")
            else:
                print(f"--- IMAGE DEBUG: No url or b64_json found ---")
                raise ValueError("No image URL or data found in API response.")

        else:
            print(f"--- IMAGE DEBUG: Found URL: {image_url} ---")

            # Download the image from the URL
            image_response = requests.get(image_url)
            image_response.raise_for_status()
            image_data = image_response.content
        logger.info("Image generated and decoded successfully.")

        # --- 3. Upload to Supabase Storage ---
        logger.info("Uploading image to Supabase bucket: %s...", supabase_bucket)
        supabase: Client = create_client(supabase_url, supabase_key)

        file_name = f"recipe_image_{uuid.uuid4()}.png"

        supabase.storage.from_(supabase_bucket).upload(
            file=image_data,
            path=file_name,
            file_options={"content-type": "image/png"}
        )

        # --- 4. Get the public URL ---
        public_url = supabase.storage.from_(supabase_bucket).get_public_url(file_name)
        logger.info("Image uploaded successfully. Public URL: %s", public_url)

        # Return the full URL - the serializer will extract the filename for storage
        return public_url

    except ValueError as e:
        logger.error("A value error occurred: %s", e, exc_info=True)
        return "https://via.placeholder.com/150"
    except Exception as e: # pylint: disable=broad-except
        logger.error("An unexpected error occurred: %s", e, exc_info=True)
        return "https://via.placeholder.com/150"
