import React from 'react';
import CardRecipeImage from './components/CardRecipes/CardRecipeImage'; // Updated import path
import useRecipeManagement from './components/CardRecipes/hooks/useRecipeManagement'; // Adjusted import path for the hook
import CardRecipeData from './components/CardRecipes/CardRecipeData'; // Adjusted import path
// import CombinationPopUpCard from '../CombinationPopUpCard'; // No longer needed here
// import useCombinationPopUp from '../CombinationPopUpCard/hooks/useCombinationPopUp'; // No longer needed here
import './styles/CombinationCard.css';

// aggregateIngredients function and related logic moved to CardData.jsx
 
const CombinationCard = ({ combination, onSave, onRecalculate, onCardClick, onSaveCombination, onDeleteCombination }) => { // Added onCardClick prop
  // Pop-up state is now managed by CombinationList
  // const {
  //   selectedCombination: selectedPopUpCombination,
  //   openPopUp,
  //   closePopUp,
  // } = useCombinationPopUp();

  const {
    recipes,
    activeRecipe,
    goToNextRecipe,
    goToPreviousRecipe,
  } = useRecipeManagement(combination);

  // testImages array removed.
  // Image will now be determined by activeRecipe.image from useRecipeManagement.

  console.log("Active Recipe in CombinationCard:", activeRecipe); // DEBUGGING LOG

  if (!combination) {
    return <div className="combination-card-loading">Loading combination...</div>;
  }

  return (
    // Removed Fragment <> as pop-up is no longer rendered here
      <div
        className={`combination-card ${!combination.cache_valid ? 'outdated' : ''}`}
        onClick={() => onCardClick(combination)} // Call prop to open pop-up
      >
        
        <div className="card-body">
          {/* CardRecipeScroller removed */}
          
          <CardRecipeImage
            recipes={recipes} // Pass the recipes array
            // currentRecipeIndex prop removed as activeRecipe is now the source of truth for the image.
            imageUrl={activeRecipe?.image} // Use image from the activeRecipe object
            activeRecipe={activeRecipe} // Pass activeRecipe for other potential uses (e.g., alt text)
            goToPreviousRecipe={goToPreviousRecipe} // Pass navigation functions
            goToNextRecipe={goToNextRecipe}     // Pass navigation functions
          />

          {/* CardHeader removed - recipe name now displays as overlay on image */}

          {/* {activeRecipe && (
            <CardRecipeData
              activeRecipeName={activeRecipe.name}
              activeRecipeSpecificIngredients={activeRecipe.specific_ingredients || []}
              // activeRecipeImageUrl={activeRecipe.image_url} // Removed image URL prop
            />
          )} */}
          
          {/* Display overall pantry/shopping lists for the entire combination using the new CardData component */}
          {/* <CardData combination={combination} /> */}

          {/* <WasteScoreIndicator
            score={combination.waste_score}
            size="medium"
            combination={combination} // Keep full combination for tooltip or other details
            isCacheValid={combination.cache_valid}
          /> */}

          {/* <CardSeasonality combination={combination} /> Seasonality is likely for the whole combination */}
        </div>
        
        {/* <CardFooter combination={combination} onSave={onSave} /> */}
      </div>
    // Pop-up rendering moved to CombinationList
    // {selectedPopUpCombination && (
    //   <CombinationPopUpCard
    //     combination={selectedPopUpCombination}
    //     onClose={closePopUp}
    //   />
    // )}
  );
};

export default CombinationCard;
