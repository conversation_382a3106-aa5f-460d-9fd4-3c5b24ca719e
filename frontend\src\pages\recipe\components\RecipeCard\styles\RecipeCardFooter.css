.recipe-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #eee; /* Optional: adds a separator */
  margin-top: 10px; /* Space above the footer */
}

.recipe-diet .diet-tag {
  background-color: #e0e0e0; /* Example background */
  color: #555;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
}

.recipe-actions button {
  padding: 6px 12px;
  margin-left: 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.recipe-actions .edit-btn {
  background-color: #007bff; /* Blue */
  color: white;
}

.recipe-actions .edit-btn:hover {
  background-color: #0056b3;
}

.recipe-actions .delete-btn {
  background-color: #dc3545; /* Red */
  color: white;
}

.recipe-actions .delete-btn:hover {
  background-color: #c82333;
}