import { useState } from 'react';

const useCombinationPopUp = () => {
  const [selectedCombination, setSelectedCombination] = useState(null);

  const openPopUp = (combination) => {
    setSelectedCombination(combination);
  };

  const closePopUp = () => {
    setSelectedCombination(null);
  };

  return {
    selectedCombination,
    openPopUp,
    closePopUp,
  };
};

export default useCombinationPopUp;
