import React from 'react';
import '../styles/CardSeasonality.css';
import SeasonalityDisplay from '../../../../../components/SeasonalityDisplay'; // Adjusted path
import { useSeasonality } from '../../../hooks/useSeasonality'; // Adjusted path

const CardSeasonality = ({ combination }) => {
  const { safelyParseSeasonalMonths } = useSeasonality();

  return (
    <div className="seasonality">
      {(() => {
        // Enhanced error handling for seasonality data
        try {
          const seasonalMonths = safelyParseSeasonalMonths(
            combination.overlapping_months || combination.seasonality_months
          );
          
          return seasonalMonths.length > 0 ? (
            <SeasonalityDisplay
              seasonalMonths={seasonalMonths}
              highlightCurrent={true}
            />
          ) : (
            <p className="no-seasonality">No seasonality data available</p>
          );
        } catch (err) {
          console.error('Error rendering seasonality:', err);
          return <p className="no-seasonality">Error displaying seasonality data</p>;
        }
      })()}
    </div>
  );
};

export default CardSeasonality;