"""
Main settings file that routes to the appropriate environment-specific settings.
This file serves as a fallback for when DJANGO_SETTINGS_MODULE is set to 'poireaux.settings'
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Determine which settings file to use based on an environment variable
# Default to development settings if DJANGO_ENV is not set
DJANGO_ENV = os.environ.get('DJANGO_ENV', 'development')

if DJANGO_ENV == 'production':
    from .settings.production import *  # noqa: F403,F401
else:  # Default to development
    from .settings.development import *  # noqa: F403,F401
