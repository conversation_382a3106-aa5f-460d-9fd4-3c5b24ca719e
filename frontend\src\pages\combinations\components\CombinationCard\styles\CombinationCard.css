/* Combination Card Styles */
.combination-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px; /* Reduced from 20px */
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  /* Width and height will be set by parent container */
  display: flex; /* Add flexbox to the card itself */
  flex-direction: column; /* Stack children vertically */
  border: 2px solid #8FBC8F; /* Leek green border */
}

.combination-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Styling for outdated combinations */
.combination-card.outdated {
  border: 1px solid #ffc107;
}

.cache-status-banner {
  background-color: #fff3cd;
  color: #856404;
  padding: 8px 12px;
  font-size: 0.85em;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ffeeba;
}

.outdated-icon {
  margin-right: 6px;
}

.recalculate-btn-small {
  background-color: #ffc107;
  color: #212529;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8em;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recalculate-btn-small:hover {
  background-color: #e0a800;
}

.calculation-info {
  margin-top: 8px;
  font-size: 0.8em;
  color: #6c757d;
}

.calculation-timestamp {
  display: inline-block;
  padding: 2px 6px;
  background-color: #e9ecef;
  border-radius: 4px;
}

.card-body {
  padding: 12px; /* Reduced from 15px */
  display: flex; /* Make card-body a flex container */
  flex-direction: column; /* Stack its children vertically */
  flex-grow: 1; /* Allow card-body to take available space */
  overflow: hidden; /* Prevent content overflow */
}

.waste-score-container {
  margin-bottom: 12px; /* Reduced from 15px */
}

.recipes-list {
  margin-bottom: 12px; /* Reduced from 15px */
  flex-shrink: 1; /* Allow to shrink if needed */
  overflow: hidden; /* Prevent overflow */
}

.recipes-list h4 {
  margin-top: 0;
  margin-bottom: 8px; /* Reduced from 10px */
  font-size: 0.95em; /* Slightly smaller */
  color: #495057;
}

.recipes-list ul {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
  max-height: 200px; /* Limit height to prevent overflow */
  overflow-y: auto; /* Add scroll if needed */
}

.recipes-list li {
  padding: 5px 0; /* Reduced from 6px */
  border-bottom: 1px solid #f0f0f0;
  color: #495057;
  font-size: 0.9em; /* Slightly smaller text */
}

.recipes-list li:last-child {
  border-bottom: none;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .combination-card {
    width: 100%;
    max-width: 95%;
    max-height: 85vh; /* Slightly more height on mobile */
    margin-bottom: 10px;
  }

  .card-body {
    padding: 10px;
  }

  .recipes-list ul {
    max-height: 150px; /* Smaller on mobile */
  }
}
