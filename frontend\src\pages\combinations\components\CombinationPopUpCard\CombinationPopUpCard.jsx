import React from 'react';
import useRecipeManagement from '../CombinationCard/components/CardRecipes/hooks/useRecipeManagement';
import CardRecipeImage from '../CombinationCard/components/CardRecipes/CardRecipeImage';
import CardRecipeData from '../CombinationCard/components/CardRecipes/CardRecipeData';
import './CombinationPopUpCard.css';
import '../CombinationCard/components/CardRecipes/styles/CardRecipeData.css';

const CombinationPopUpCard = ({ combination, onClose, onSaveCombination, onDeleteCombination }) => {
  const {
    recipes,
    activeRecipe,
    goToNextRecipe,
    goToPreviousRecipe,
  } = useRecipeManagement(combination);

  if (!combination) {
    return null;
  }

  return (
    <div className="combination-pop-up-overlay" onClick={onClose}>
      <div className="combination-pop-up-card" onClick={(e) => e.stopPropagation()}>
        <button onClick={onClose} className="combination-pop-up-close-button">&times;</button>
        <div className="combination-pop-up-content">
          <div className="combination-pop-up-nav-button-container">
            <button onClick={goToPreviousRecipe} className="combination-pop-up-nav-button">{'<'}</button>
          </div>
          <div className="combination-pop-up-left">
            <CardRecipeImage
              recipes={recipes}
              imageUrl={activeRecipe?.image}
              activeRecipe={activeRecipe}
              goToPreviousRecipe={goToPreviousRecipe}
              goToNextRecipe={goToNextRecipe}
              showNavigation={false}
            />
            {/* Add mobile navigation controls */}
            <div className="mobile-nav-controls">
              <button onClick={goToPreviousRecipe} className="combination-pop-up-nav-button">{'<'}</button>
              <button onClick={goToNextRecipe} className="combination-pop-up-nav-button">{'>'}</button>
            </div>
          </div>
          <div className="combination-pop-up-middle">
            <CardRecipeData
              activeRecipeName={activeRecipe?.name}
              activeRecipeSpecificIngredients={activeRecipe?.specific_ingredients}
            />
          </div>
          <div className="combination-pop-up-right">
            <div className="instructions-container">
              {activeRecipe?.instructions ? (
                <p>{activeRecipe.instructions}</p>
              ) : (
                <p>No instructions available for this recipe.</p>
              )}
            </div>
          </div>
          <div className="combination-pop-up-nav-button-container">
            <button onClick={goToNextRecipe} className="combination-pop-up-nav-button">{'>'}</button>
          </div>
        </div>
        <div className="combination-pop-up-footer">
          {onSaveCombination && (
            <button onClick={(e) => { e.stopPropagation(); onSaveCombination(combination.id); }} className="popup-button save-button">
              Save Combination
            </button>
          )}
          {onDeleteCombination && (
            <button onClick={(e) => { e.stopPropagation(); onDeleteCombination(combination.savedCombinationId); }} className="popup-button delete-button">
              Delete
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CombinationPopUpCard;
