import React from 'react';

function TypeTabBar({ typeOptions, activeTypeTab, setActiveTypeTab }) {
  return (
    <div className="type-tabs">
      <div 
        className={`type-tab ${activeTypeTab === 'all' ? 'active' : ''}`}
        onClick={() => setActiveTypeTab('all')}
      >
        All
      </div>
      
      {typeOptions.filter(type => type !== 'all').map(type => (
        <div 
          key={type}
          className={`type-tab ${activeTypeTab === type ? 'active' : ''}`}
          onClick={() => setActiveTypeTab(type)}
        >
          {type.charAt(0).toUpperCase() + type.slice(1)}
        </div>
      ))}
    </div>
  );
}

export default TypeTabBar; 