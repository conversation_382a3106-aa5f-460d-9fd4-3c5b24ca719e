/* Combination Cards Grid - Original Layout */
.combination-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Responsive grid */
  gap: 20px; /* Space between cards */
  padding: 20px 0; /* Padding around the grid */
  justify-items: center; /* Center the cards within their grid cells */
  position: relative; /* For pop-up positioning */
}

/* Loading State Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #555; /* Medium grey text */
}

.loading-spinner {
  border: 4px solid #f3f3f3; /* Light grey border */
  border-top: 4px solid #3498db; /* Blue for spinner */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite; /* Spin animation */
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.1em;
}

/* No Combinations Message */
.no-combinations {
  text-align: center;
  padding: 40px;
  color: #777; /* Slightly lighter grey */
  background-color: #f9f9f9; /* Very light grey background */
  border-radius: 8px;
  margin-top: 20px;
}

.no-combinations p {
  font-size: 1.2em;
  margin-bottom: 10px;
}

/* Responsive adjustments for grid */
@media (max-width: 768px) {
  .combination-cards-grid {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 15px;
    padding: 15px 0;
  }
}

@media (max-width: 480px) {
  .combination-cards-grid {
    gap: 10px;
    padding: 10px 0;
  }
}
