# Generated by Django 5.1.7 on 2025-07-26 16:15

from django.db import migrations, models


def convert_bought_by_amount_to_json(apps, schema_editor):
    """Convert existing integer bought_by_amount values to JSON arrays"""
    Ingredient = apps.get_model('poireaux_app', 'Ingredient')

    for ingredient in Ingredient.objects.all():
        if (ingredient.bought_by_amount is not None and
            ingredient.bought_by_amount > 0):
            # Convert single integer to array with that value
            ingredient.bought_by_amount_temp = [ingredient.bought_by_amount]
        else:
            # Set empty array for zero or null values
            ingredient.bought_by_amount_temp = []
        ingredient.save()


def reverse_bought_by_amount_to_integer(apps, schema_editor):
    """Reverse conversion: take first value from JSON array"""
    Ingredient = apps.get_model('poireaux_app', 'Ingredient')

    for ingredient in Ingredient.objects.all():
        if (ingredient.bought_by_amount_temp and
            len(ingredient.bought_by_amount_temp) > 0):
            # Take the first value from the array
            ingredient.bought_by_amount = ingredient.bought_by_amount_temp[0]
        else:
            ingredient.bought_by_amount = 0
        ingredient.save()


class Migration(migrations.Migration):

    dependencies = [
        ('poireaux_app', '0027_combination_diet_and_more'),
    ]

    operations = [
        # First, add a temporary JSON field
        migrations.AddField(
            model_name='ingredient',
            name='bought_by_amount_temp',
            field=models.JSONField(
                blank=True,
                default=list,
                help_text='List of purchase amounts (e.g., [250, 400] for different can sizes)'
            ),
        ),
        # Convert data from integer to JSON
        migrations.RunPython(convert_bought_by_amount_to_json, reverse_bought_by_amount_to_integer),
        # Remove the old integer field
        migrations.RemoveField(
            model_name='ingredient',
            name='bought_by_amount',
        ),
        # Rename the temp field to the original name
        migrations.RenameField(
            model_name='ingredient',
            old_name='bought_by_amount_temp',
            new_name='bought_by_amount',
        ),
    ]
