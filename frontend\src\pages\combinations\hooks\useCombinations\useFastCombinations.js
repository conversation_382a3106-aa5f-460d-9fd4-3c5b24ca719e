import { useState, useEffect, useCallback } from 'react';
import { useCombinationAPI } from './useCombinationAPI';

/**
 * Hook for fast combination loading with progressive enhancement
 * 1. First loads minimal data from fast endpoint for immediate display
 * 2. Then loads full data in background for complete functionality
 */
export const useFastCombinations = () => {
  const { fetchCombinationsAPI } = useCombinationAPI();
  
  const [combinations, setCombinations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fastLoading, setFastLoading] = useState(false);
  const [fullLoading, setFullLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasFastData, setHasFastData] = useState(false);
  const [hasFullData, setHasFullData] = useState(false);

  const fetchCombinations = useCallback(async (servings = 1, month = null, diet = 'all') => {
    setLoading(true);
    setError(null);
    
    try {
      // Step 1: Load fast data first for immediate display
      setFastLoading(true);
      console.log('🚀 Loading fast combinations data...');
      
      const fastData = await fetchCombinationsAPI(servings, month, diet, true); // useFastEndpoint = true
      setCombinations(fastData);
      setHasFastData(true);
      setFastLoading(false);
      
      console.log('✅ Fast data loaded:', fastData.length, 'combinations');
      
      // Step 2: Load full data in background for complete functionality
      setFullLoading(true);
      console.log('🔄 Loading full combinations data in background...');
      
      const fullData = await fetchCombinationsAPI(servings, month, diet, false); // useFastEndpoint = false
      setCombinations(fullData);
      setHasFullData(true);
      setFullLoading(false);
      
      console.log('✅ Full data loaded:', fullData.length, 'combinations');
      
    } catch (err) {
      console.error('❌ Error loading combinations:', err);
      setError(err.message);
      setFastLoading(false);
      setFullLoading(false);
    } finally {
      setLoading(false);
    }
  }, [fetchCombinationsAPI]);

  const fetchFastOnly = useCallback(async (servings = 1, month = null, diet = 'all') => {
    setLoading(true);
    setFastLoading(true);
    setError(null);
    
    try {
      console.log('🚀 Loading fast-only combinations data...');
      const fastData = await fetchCombinationsAPI(servings, month, diet, true);
      setCombinations(fastData);
      setHasFastData(true);
      console.log('✅ Fast-only data loaded:', fastData.length, 'combinations');
    } catch (err) {
      console.error('❌ Error loading fast combinations:', err);
      setError(err.message);
    } finally {
      setLoading(false);
      setFastLoading(false);
    }
  }, [fetchCombinationsAPI]);

  const fetchFullOnly = useCallback(async (servings = 1, month = null, diet = 'all') => {
    setLoading(true);
    setFullLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Loading full combinations data...');
      const fullData = await fetchCombinationsAPI(servings, month, diet, false);
      setCombinations(fullData);
      setHasFullData(true);
      console.log('✅ Full data loaded:', fullData.length, 'combinations');
    } catch (err) {
      console.error('❌ Error loading full combinations:', err);
      setError(err.message);
    } finally {
      setLoading(false);
      setFullLoading(false);
    }
  }, [fetchCombinationsAPI]);

  return {
    combinations,
    loading,
    fastLoading,
    fullLoading,
    error,
    hasFastData,
    hasFullData,
    fetchCombinations,      // Progressive loading: fast first, then full
    fetchFastOnly,          // Only load fast data
    fetchFullOnly,          // Only load full data
    isLoadingAny: loading || fastLoading || fullLoading,
    loadingStatus: {
      initial: loading && !hasFastData && !hasFullData,
      fastComplete: hasFastData && !hasFullData,
      fullComplete: hasFullData,
      backgroundLoading: hasFastData && fullLoading
    }
  };
};
