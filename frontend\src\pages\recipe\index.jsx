import React, { useEffect } from 'react'; // Removed useState
// import { useIngredients } from '../../hooks/useIngredients'; // Removed as it's not used
import useRecipes from './hooks/useRecipes'; // Import useRecipes
import useRecipeProcess from './hooks/useRecipeProcess';
import useRecipeList from './hooks/useRecipeList'; // New
import useOverallLoadingState from './hooks/useOverallLoadingState'; // New

// Import global styles
import '../../styles/RecipeUi.css';
// Import page-specific styles
import './styles/index.css';

// Import page-specific hooks
import useServingSize from './hooks/useServingSize';
import useRecipeSeasonality from './hooks/useRecipeSeasonality';

// Import page-specific components
import ServingSizeSelector from './components/ServingSizeSelector';
import RecipeList from './components/RecipeList';
import RecipeAdder from './components/RecipeAdder'; // Import RecipeAdder

function Recipe() {
  // Get ingredient-related state and handlers
  // const { ingredients } = useIngredients(); // Removed as it's not used

  // Get recipe-related state and handlers
  const {
    recipes,
    setRecipes, // Or equivalent update mechanism
    handleDeleteRecipe, // Assuming this is still from useRecipes
    isLoading // from useRecipes
  } = useRecipes();

  // Call useRecipeList (passing setRecipes) to get handleRecipeUpdate
  const { handleRecipeUpdate } = useRecipeList(setRecipes);

  // Call useRecipeProcess to get executeProcessAllRecipes, isProcessingRecipes, and processingError
  const { executeProcessAllRecipes, isProcessingRecipes, processingError } = useRecipeProcess();

  // Call useRecipeSeasonality (passing recipes) to get recipeSeasonalityMap, isLoadingSeasonality
  // Assuming formatSeasonality is still provided for RecipeList.
  const {
    recipeSeasonalityMap,
    isLoadingSeasonality,
    formatSeasonality, // Keep for RecipeList, assuming it's still there
    // preloadSeasonality is now handled internally by useRecipeSeasonality
  } = useRecipeSeasonality(recipes);

  // Call useOverallLoadingState (passing isLoading from useRecipes and isProcessingRecipes from useRecipeProcess) to get isAnyRefreshing
  const { isAnyRefreshing } = useOverallLoadingState(isLoading, isProcessingRecipes);

  // Use custom hook for serving size
  const { activeServingSize, setActiveServingSize, filterRecipesBySize } = useServingSize(1);

  // Effect to handle processing errors (replicates old alert functionality)
  useEffect(() => {
    if (processingError) {
      alert(`Error processing recipes: ${processingError}`);
      // Consider if processingError needs to be reset in the hook after being handled
    }
  }, [processingError]);
  
  return (
    <div className="recipe-container">
      <div className="recipe-main-layout">
        <div className="recipe-adder-section">
          <RecipeAdder />
        </div>

        <div className="recipe-list-section">
          <div className="recipe-list">
            <ServingSizeSelector
              activeServingSize={activeServingSize}
              setActiveServingSize={setActiveServingSize}
              isRefreshing={isAnyRefreshing} // from useOverallLoadingState
              handleProcessRecipe={() => executeProcessAllRecipes(recipes)} // from useRecipeProcess, wrapped
            />

            <h2>Your Recipes</h2>

            <RecipeList
              recipes={recipes}
              activeServingSize={activeServingSize}
              formatSeasonality={formatSeasonality} // from useRecipeSeasonality
              recipeSeasonalityMap={recipeSeasonalityMap} // from useRecipeSeasonality
              isLoadingSeasonality={isLoadingSeasonality} // from useRecipeSeasonality
              handleDeleteRecipe={handleDeleteRecipe} // from useRecipes
              filterRecipesBySize={filterRecipesBySize} // from useServingSize
              onRecipeUpdate={handleRecipeUpdate} // from useRecipeList
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Recipe; 
