import { useState, useCallback, useEffect } from 'react';
import { API_BASE_URL } from '../../../../../config/api';

const useRecipeImageUpload = (initialImage, recipeId, onRecipeUpdateCallback) => {
  const [currentImage, setCurrentImage] = useState(initialImage);
  const [uploadError, setUploadError] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    console.log(`useRecipeImageUpload for recipe ${recipeId}:`, {
      initialImage,
      currentImage
    });
    setCurrentImage(initialImage);
  }, [initialImage, recipeId, currentImage]);

  const onDrop = useCallback(async (acceptedFiles) => {
    setUploadError(null);
    if (acceptedFiles.length === 0) {
      setUploadError('No file selected or file type is not supported.');
      return;
    }
    const file = acceptedFiles[0];
    if (!file.type.startsWith('image/')) {
      setUploadError('Invalid file type. Please upload an image.');
      return;
    }

    const formData = new FormData();
    formData.append('image', file);
    setIsUploading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/recipes/update/${recipeId}/`, {
        method: 'PATCH',
        body: formData,
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        let errorText = `Failed to upload image (status: ${response.status} ${response.statusText}). URL: ${response.url}`;
        let errorDetail = '';

        if (contentType && contentType.indexOf("application/json") !== -1) {
          const errorData = await response.json();
          errorDetail = errorData.detail || JSON.stringify(errorData);
        } else {
          errorDetail = await response.text();
          console.error('Server response was not JSON. Raw response text:', errorDetail);
        }
        throw new Error(`${errorText} - Details: ${errorDetail}`);
      }

      const updatedRecipeData = await response.json();
      setCurrentImage(updatedRecipeData.image);
      if (onRecipeUpdateCallback) {
        onRecipeUpdateCallback(updatedRecipeData);
      }
    } catch (error) {
      console.error('Upload error object:', error);
      setUploadError(error.message || 'An unexpected error occurred during upload.');
    } finally {
      setIsUploading(false);
    }
  }, [recipeId, onRecipeUpdateCallback]);

  return {
    currentImage,
    uploadError,
    isUploading,
    onDrop,
    setCurrentImage // Exposing this in case parent needs to reset it externally
  };
};

export default useRecipeImageUpload;
