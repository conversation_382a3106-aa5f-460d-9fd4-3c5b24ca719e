import { useState } from 'react';
import { ENDPOINTS } from '../../../config/api';

/**
 * Custom hook for processing all recipes.
 * Encapsulates the logic and state for the "Process All Recipes" functionality.
 */
export const useRecipeProcess = () => {
  const [isProcessingRecipes, setIsProcessingRecipes] = useState(false);
  const [processingError, setProcessingError] = useState(null);

  /**
   * Initiates the processing of all recipes.
   * Accepts the current list of recipes, handles API calls, and manages internal loading/error states.
   * @param {Array} recipes - The current list of recipes.
   */
  const executeProcessAllRecipes = async (recipes) => {
    setProcessingError(null); // Clear previous errors

    if (!recipes || recipes.length === 0) {
      console.warn("No recipes to process.");
      setProcessingError("No recipes to process.");
      return;
    }

    setIsProcessingRecipes(true);
    try {
      const originalRecipe = recipes.find(recipe => recipe.name && recipe.name.includes("(original)"));

      if (!originalRecipe) {
        console.error("No original recipe found to process. Cannot send request.");
        setProcessingError("No original recipe found to process.");
        // setIsProcessingRecipes(false); // This will be handled by finally
        return; // Exit early, finally will still run
      }

      const response = await fetch(ENDPOINTS.RECIPES.PROCESS.CHECK_ALL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ original_recipe_id: originalRecipe.id }),
      });

      if (!response.ok) {
        let errorDetail = `HTTP error ${response.status}`;
        try {
          const errorData = await response.json();
          errorDetail = errorData.error || JSON.stringify(errorData);
        } catch (e) {
          // If response is not JSON, use text
          errorDetail = await response.text();
        }
        throw new Error(`Failed to process recipes: ${errorDetail}`);
      }

      // const result = await response.json(); // result is not used further for state setting
      await response.json(); // Consume the response
      console.log("All recipes processed successfully.");
      // Success: isProcessingRecipes will be set to false in finally, error remains null
    } catch (error) {
      console.error("Error processing recipes:", error.message);
      setProcessingError(error.message);
    } finally {
      setIsProcessingRecipes(false);
    }
  };

  return { isProcessingRecipes, processingError, executeProcessAllRecipes };
};

export default useRecipeProcess;