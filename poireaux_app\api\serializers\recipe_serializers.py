import os # Added for environment variables
from rest_framework import serializers
from ...models import Recipe, RecipeIngredient, Ingredient
from .ingredient_serializers import IngredientSerializer

import json
import os
from rest_framework import serializers
from ...models import Recipe, RecipeIngredient
from .ingredient_serializers import IngredientSerializer

class RecipeIngredientSerializer(serializers.ModelSerializer):
    ingredient_name = serializers.CharField(source='ingredient.name', read_only=True)
    ingredient_data = serializers.SerializerMethodField()

    class Meta:
        model = RecipeIngredient
        fields = ['id', 'recipe', 'ingredient', 'quantity', 'unit', 'is_divisible', 'ingredient_name', 'ingredient_data']
    
    def get_ingredient_data(self, obj):
        return IngredientSerializer(obj.ingredient).data

class RecipeIngredientCreateSerializer(serializers.ModelSerializer):
    # This serializer is for creating/updating RecipeIngredient instances
    # It expects ingredient_id, quantity, unit, and is_divisible
    ingredient = serializers.PrimaryKeyRelatedField(queryset=Ingredient.objects.all())

    class Meta:
        model = RecipeIngredient
        fields = ['ingredient', 'quantity', 'unit', 'is_divisible']

class RecipeLiteSerializer(serializers.ModelSerializer):
    """
    A lightweight serializer for recipes, including only id, name, and image.
    """
    image = serializers.SerializerMethodField(method_name='get_direct_supabase_url')

    class Meta:
        model = Recipe
        fields = ['id', 'name', 'image']

    def get_direct_supabase_url(self, obj):
        # Check if the image exists and its name is a non-empty string
        if obj.image and hasattr(obj.image, 'name') and isinstance(obj.image.name, str) and obj.image.name:
            image_value = obj.image.name

            # If the image field already contains a full URL, return it directly
            if image_value.startswith('http'):
                return image_value

            # Otherwise, construct the URL from the filename
            image_filename = os.path.basename(image_value)

            supabase_base_url = os.getenv('DJANGO_SUPABASE_URL')
            bucket_name = os.getenv('SUPABASE_RECIPE_BUCKET')

            if supabase_base_url and bucket_name:
                supabase_base_url = supabase_base_url.rstrip('/')
                bucket_name = bucket_name.strip('/')
                return f"{supabase_base_url}/storage/v1/object/public/{bucket_name}/{image_filename}"

        # Return None if any check fails
        return None

class CombinationRecipeSerializer(serializers.ModelSerializer):
    """
    A serializer for recipes within a combination, including only essential fields.
    """
    recipe_ingredients = RecipeIngredientSerializer(many=True, read_only=True)
    image = serializers.SerializerMethodField(method_name='get_direct_supabase_url')

    class Meta:
        model = Recipe
        fields = ['id', 'name', 'image', 'instructions', 'recipe_ingredients']

    def get_direct_supabase_url(self, obj):
        # Check if the image exists and its name is a non-empty string
        if obj.image and hasattr(obj.image, 'name') and isinstance(obj.image.name, str) and obj.image.name:
            image_value = obj.image.name

            # If the image field already contains a full URL, return it directly
            if image_value.startswith('http'):
                return image_value

            # Otherwise, construct the URL from the filename
            image_filename = os.path.basename(image_value)

            supabase_base_url = os.getenv('DJANGO_SUPABASE_URL')
            bucket_name = os.getenv('SUPABASE_RECIPE_BUCKET')

            if supabase_base_url and bucket_name:
                supabase_base_url = supabase_base_url.rstrip('/')
                bucket_name = bucket_name.strip('/')
                return f"{supabase_base_url}/storage/v1/object/public/{bucket_name}/{image_filename}"

        # Return None if any check fails
        return None

class RecipeSerializer(serializers.ModelSerializer):
    # Use RecipeIngredientSerializer for reading, and RecipeIngredientCreateSerializer for writing
    recipe_ingredients = RecipeIngredientSerializer(many=True, read_only=True)
    
    # For writing, we'll use a separate field that expects a JSON string of ingredients
    ingredients = serializers.CharField(write_only=True) # Expects a JSON string from frontend
    
    instructions = serializers.CharField(required=False, allow_blank=True)
    diet = serializers.CharField(required=False)
    image = serializers.CharField(required=False, allow_blank=True)  # Allow both reading and writing
    months = serializers.CharField(required=False, allow_blank=True) # Expects JSON string

    class Meta:
        model = Recipe
        fields = ['id', 'name', 'instructions', 'type', 'diet', 'servings', 'months', 'recipe_ingredients', 'divisible', 'ingredients', 'image', 'category']
        extra_kwargs = {
            'category': {'required': False}, # Make category optional for now if not always sent
            'divisible': {'required': False}, # Make divisible optional
        }
    
    def to_representation(self, instance):
        """Custom representation to handle image URL construction"""
        data = super().to_representation(instance)

        # Handle image field - construct URL from stored filename
        if instance.image and hasattr(instance.image, 'name') and instance.image.name:
            image_value = instance.image.name

            # If the image field already contains a full URL, return it directly
            if image_value.startswith('http'):
                data['image'] = image_value
            else:
                # Otherwise, construct the URL from the filename
                image_filename = os.path.basename(image_value)

                supabase_base_url = os.getenv('DJANGO_SUPABASE_URL')
                bucket_name = os.getenv('SUPABASE_RECIPE_BUCKET')

                if supabase_base_url and bucket_name:
                    supabase_base_url = supabase_base_url.rstrip('/')
                    bucket_name = bucket_name.strip('/')
                    data['image'] = f"{supabase_base_url}/storage/v1/object/public/{bucket_name}/{image_filename}"
                else:
                    data['image'] = None
        else:
            data['image'] = None

        return data

    def create(self, validated_data):
        ingredients_data = []
        months_data = []

        # Handle 'ingredients' field (JSON string)
        if 'ingredients' in validated_data:
            try:
                ingredients_data = json.loads(validated_data.pop('ingredients'))
            except json.JSONDecodeError:
                raise serializers.ValidationError({'ingredients': 'Invalid JSON format for ingredients.'})

        # Handle 'months' field (JSON string)
        if 'months' in validated_data:
            # The Recipe model stores months as a TextField, which expects a string.
            # The frontend already sends it as a JSON string, so no further parsing needed here.
            # Just ensure it's popped from validated_data if it's handled separately.
            pass # The field is already handled by ModelSerializer if it's in fields

        # Handle image field - if it's a URL, store just the filename
        image_value = validated_data.get('image')
        print(f"--- SERIALIZER DEBUG: Processing image value: {image_value} ---")

        if image_value and isinstance(image_value, str):
            if image_value.startswith('http') and 'recipe_image_' in image_value:
                # Extract the filename from the Supabase URL and store it
                # Remove query parameters if present
                url_without_query = image_value.split('?')[0]
                filename = url_without_query.split('/')[-1]
                validated_data['image'] = f"recipe_images/{filename}"
                print(f"--- SERIALIZER DEBUG: Storing image as: {validated_data['image']} ---")
            elif image_value.startswith('http'):
                # For placeholder URLs, don't store anything
                validated_data.pop('image', None)
                print(f"--- SERIALIZER DEBUG: Removed placeholder URL ---")
            else:
                # It's already a filename, keep it as is
                print(f"--- SERIALIZER DEBUG: Keeping filename: {image_value} ---")
        else:
            print(f"--- SERIALIZER DEBUG: No image value or not a string ---")

        # Create the Recipe instance
        recipe = Recipe.objects.create(**validated_data)

        # Create RecipeIngredient instances
        for ingredient_data in ingredients_data:
            # Use the RecipeIngredientCreateSerializer to validate and create
            ingredient_serializer = RecipeIngredientCreateSerializer(data=ingredient_data)
            ingredient_serializer.is_valid(raise_exception=True)
            RecipeIngredient.objects.create(recipe=recipe, **ingredient_serializer.validated_data)
        
        return recipe

    def update(self, instance, validated_data):
        # Handle 'ingredients' field for updates if needed (similar to create)
        # For now, assuming ingredients are only added/updated via separate endpoints or full replacement
        
        # Handle 'months' field for updates
        if 'months' in validated_data:
            # The Recipe model stores months as a TextField, which expects a string.
            # The frontend already sends it as a JSON string, so no further parsing needed here.
            pass # The field is already handled by ModelSerializer if it's in fields

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

# Define MONTHS here or import it if it's a common constant
MONTHS = [
    'january', 'february', 'march', 'april', 'may', 'june',
    'july', 'august', 'september', 'october', 'november', 'december'
]
