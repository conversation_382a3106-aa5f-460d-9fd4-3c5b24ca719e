const webpack = require('webpack');
const { override, addWebpackResolve, addWebpackPlugin, overrideDevServer } = require('customize-cra');

module.exports = {
  webpack: override(
    addWebpackResolve({
      fallback: {
        "http": require.resolve("stream-http"),
        "https": require.resolve("https-browserify"),
        "util": require.resolve("util/"),
        "zlib": require.resolve("browserify-zlib"),
        "stream": require.resolve("stream-browserify"),
        "url": require.resolve("url/"),
        "assert": require.resolve("assert/"),
        "buffer": require.resolve("buffer/"),
        "process": require.resolve("process/browser"),
      }
    }),
    addWebpackPlugin(
      new webpack.ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer'],
      }),
    )
  ),
  devServer: (configFunction) => (proxy, allowedHost) => {
    const config = configFunction(proxy, allowedHost);
    config.allowedHosts = ['auto'];
    return config;
  },
};
