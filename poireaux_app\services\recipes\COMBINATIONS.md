# Recipe Combinations Implementation

This document explains the recipe combinations functionality, components, and implementation details.

## Overview

The recipe combinations system generates, calculates, and saves optimal combinations of recipes in a single, streamlined process to minimize food waste. It helps users find sets of recipes that work well together by ensuring compatible seasonality and divisibility for the desired servings. The system performs full calculations (waste score, ingredient waste details, dependent ingredients, seasonality, ingredient count) and saves the valid results automatically during generation.

## Data Structure

### Combination Model

- Each `Combination` model represents a set of recipes that work well together:
  - `name`: Name of the combination (generated from recipe names)
  - `recipes`: Many-to-many relationship with Recipe
  - `waste_score`: Float representing the calculated waste score (lower is better)
  - `servings`: Number of servings for each recipe in the combination
  - `is_divisible`: Boolean flag indicating if all recipes can be properly divided
  - `seasonality_months`: JSON array of months when all recipes in the combination are in season
  - `created_at`: Timestamp when the combination was created
  - `last_calculated`: Timestamp tracking when the combination was last calculated
  - `ingredient_waste`: JSON field to store detailed waste information for each ingredient
  - `is_top_performer`: Boolean flag to indicate combinations in the top 10% by waste score
  - `cache_valid`: Boolean flag indicating if the cached data is still valid
  - `dependent_ingredients`: JSON field storing which ingredients affect this combination
  - `ingredients_length`: Integer count of unique ingredients in the combination

### Ingredient Waste Calculation

- Waste is calculated for each ingredient in a combination:
  - Only ingredients with `needs_calculating = True` are considered
  - Waste is determined by comparing the needed quantity with the package size
  - The waste score is the percentage of unused ingredients across the combination

## Components

### Backend Components

1. **Combination Service** (`combination_service.py`):
   - Core functions for generating and filtering combinations
   - Waste score calculation algorithms
   - Combination saving and retrieval functions

2. **Combination Utilities** (`combination_crud_utils.py`):
   - Helper functions for combination operations
   - Seasonality data extraction and processing

3. **Recipe Combination Service** (`recipe_combination_service.py`):
   - Functions for checking if *previously generated* combinations exist for given parameters
   - Functions for determining if *existing* combinations need recalculation (due to updates)
   - Functions for the cascade recalculation system (triggered by updates)
   - Functions for marking combinations as invalid when dependencies change
   - Functions for updating top performers

4. **Signal Handlers** (`signals.py`):
   - Handlers for recipe updates and deletions
   - Handlers for ingredient updates and deletions
   - Handlers for recipe ingredient updates and deletions
   - Automatic marking of affected combinations as invalid

5. **API Endpoints** (`combination_views.py`):
   - REST endpoints for accessing and manipulating combinations
   - Supports generation and retrieval operations
   - Endpoint for triggering recalculation (for updates/backfilling) and checking calculation status

### Frontend Components

1. **Combinations Hook** (`useCombinations.js`):
   - Manages combination data with API integration
   - Provides functions for finding/saving optimal combinations (unified flow)
   - Handles loading states and error management
   - Supports filtering and sorting capabilities (e.g., by `waste_score`, `ingredients_length`)

2. **Seasonality Hook** (`useSeasonality.js`):
   - Provides utility functions for handling seasonality data in combinations
   - Formats data for display and handles month conversions

3. **Control Panel** (`ControlPanel.jsx`):
  - Includes UI elements for filtering and sorting combinations.
  - Features a single "Find & Save Optimal Combinations" button to trigger the unified generation process.

4. **Combination Card** (`CombinationCard.jsx`):
  - Displays individual combination details.
  - Updated to show the `ingredients_length` (unique ingredient count) next to the combination name.

## Core Functions

### Backend Functions

#### `generate_recipe_combinations(recipes, servings, season_month)`

Generates all possible combinations of exactly 3 recipes.

**Input**: 
- `recipes`: List of Recipe objects
- `servings`: Number of servings for each recipe
- `season_month`: Optional month to filter for seasonality

**Output**:
- List of recipe combinations (where each combination is a list of Recipe objects)

**Algorithm**:
1. Use Python's `itertools.combinations` to generate all possible combinations of 3 recipes
2. Return the list of combinations

#### `filter_by_divisibility(combinations, servings)`

Filters combinations based on whether all recipes can be properly divided.

**Input**: 
- `combinations`: List of recipe combinations
- `servings`: Number of servings

**Output**:
- Filtered list of recipe combinations

**Algorithm**:
1. For each combination, check if all recipes are divisible
2. Keep only combinations where all recipes are divisible

#### `filter_by_seasonality(combinations, month, min_overlapping_months)`

Filters combinations based on seasonality overlap.

**Input**: 
- `combinations`: List of recipe combinations
- `month`: Specific month to check for (1-12), or None for any month
- `min_overlapping_months`: Minimum number of months that must overlap

**Output**:
- List of tuples with (recipe_combination, overlapping_months)

**Algorithm**:
1. For each combination, check if the recipes have overlapping seasonality
2. If filtering for a specific month, check if that month is in the overlapping months
3. Keep only combinations with sufficient seasonality overlap

#### `filter_by_meat_limit(combinations)`

Filters combinations to ensure they contain at most one recipe of type 'Meat'.

**Input**:
- `combinations`: List of recipe combinations (where each combination is a list of Recipe objects)

**Output**:
- Filtered list of recipe combinations

**Algorithm**:
1. For each combination, count the number of recipes with `type == 'Meat'`.
2. Keep only combinations where the count is 0 or 1.

#### `calculate_waste_score(combo, servings)`

Calculates waste score for a combination of recipes.

**Input**: 
- `combo`: List of Recipe objects
- `servings`: Number of servings for each recipe

**Output**:
- Tuple of (waste_score, ingredient_waste_details)

**Algorithm**:
1. Calculate waste for each ingredient in the combination
2. Sum up the total waste and total quantity
3. Calculate the waste score as (total_waste / total_quantity * 100)
4. Return the waste score and detailed waste information

#### `generate_optimal_combinations(servings, season_month, limit, offset, recipe_filter)`

Generates, fully calculates, saves, and retrieves optimal recipe combinations in a single operation.

**Input**:
- `servings`: Number of servings for each recipe
- `season_month`: Month to filter for seasonality
- `limit`: Maximum number of combinations to return per page
- `offset`: Number of combinations to skip for pagination
- `recipe_filter`: Optional list of recipe IDs to filter by

**Output**:
- Paginated list of saved Combination objects (dictionaries) with full details (waste score, ingredient waste, dependencies, seasonality, ingredient count, etc.).

**Algorithm**:
1. Get all recipes or filter by specific IDs.
2. Generate all possible combinations of exactly 3 recipes.
3. Filter combinations by divisibility, seasonality, and meat limit (at most one 'Meat' recipe).
4. For each valid combination:
   a. Perform *full calculation*: waste score, detailed ingredient waste, seasonality months, unique ingredient count.
   b. Extract dependent ingredients from the waste calculation.
   c. Save the combination to the database with all calculated fields and `cache_valid=True`.
5. Retrieve the newly saved combinations matching the input criteria.
6. Sort the retrieved combinations by waste score (lower is better).
7. Apply pagination (limit/offset).
8. Return the paginated list of formatted results.

#### `check_combinations_exist(servings, month)`

Checks if combinations exist for the given parameters.

**Input**:
- `servings`: Number of servings for each recipe
- `month`: Optional month to filter for seasonality

**Output**:
- Boolean indicating if combinations exist

**Algorithm**:
1. Query the database for combinations matching the criteria
2. Check if the combinations are valid (cache_valid=True)
3. Return True if matching combinations exist, False otherwise

#### `needs_recalculation(combination)`

Determines if an *existing* combination needs recalculation due to changes in its dependencies (recipes, ingredients). This is used for updates, not initial generation.

**Input**:
- `combination`: Combination object to check

**Output**:
- Boolean indicating if recalculation is needed

**Algorithm**:
1. Check if the combination's `cache_valid` flag is `False`.
2. Check if any associated recipes have been updated since the combination's `last_calculated` timestamp.
3. Check if any dependent ingredients have changed (this check might be implicitly covered by `cache_valid=False` set by signals).
4. Return True if recalculation is needed based on these checks, False otherwise.

#### `recalculate_combination(combination_id)`

Recalculates an *existing* specific combination, typically triggered manually or after a dependency change (via signals marking it invalid). Not used during the initial generation flow.

**Input**:
- `combination_id`: ID of the combination to recalculate

**Output**:
- Boolean indicating if recalculation was successful

**Algorithm**:
1. Retrieve the combination from the database.
2. Get the associated recipes.
3. Recalculate waste score, detailed ingredient waste, seasonality months, and `ingredients_length`.
4. Extract dependent ingredients from the new waste calculation.
5. Update the combination record with the new values, set `cache_valid=True`, and update `last_calculated`.
6. Save the updated combination.
7. Return True if successful, False otherwise.

#### `update_top_performers()`

Updates the is_top_performer flag for combinations based on waste score.

**Input**: None

**Output**:
- Number of combinations marked as top performers

**Algorithm**:
1. For each serving size, get all combinations
2. Calculate how many to mark as top performers (10%)
3. Mark the top performers
4. Unmark the rest
5. Return the total number marked

### Frontend Functions

#### `useCombinations()`

Custom hook for managing recipe combinations.

**Returns**:
- `combinations`: Array of filtered combinations retrieved from the backend.
- `loading`: Boolean indicating if a request (fetch, generate, delete) is in progress.
- `error`: Any error that occurred during an operation.
- `servings`: Current servings setting.
- `month`: Current month setting.
- `sortConfig`: Current sort configuration.
- `filterConfig`: Current filter configuration.
- `pagination`: Object containing current page, total pages, etc.
- `setServings`: Function to update servings.
- `setMonth`: Function to update month.
- `fetchCombinations`: Function to fetch saved combinations (supports pagination, sorting, filtering).
- `findAndSaveCombinations`: Function to trigger the unified backend process to generate, calculate, save, and return optimal combinations.
- `retryOperation`: Function to retry a failed operation.
- `updateSort`: Function to update sort configuration.
- `updateFilter`: Function to update filter configuration.
- `setPage`: Function to change the current page for pagination.

## Implemented Optimization Strategy

The system uses a combination of immediate calculation during generation and signal-based invalidation for updates:

### 1. Initial Generation & Calculation

- **Unified Process**: When combinations are first generated via `POST /combinations/generate/`:
  - All necessary calculations (waste score, ingredient waste, dependencies, seasonality, ingredient count) are performed immediately.
  - The results are saved directly to the `Combination` model.
  - The `cache_valid` flag is set to `True`, and `last_calculated` is timestamped.
  - There is no separate recalculation step required *at generation time*.

### 2. Update Handling (Post-Generation)

- **Signal-Based Invalidation**:
  - Signal handlers (`signals.py`) monitor changes to `Recipe`, `Ingredient`, and `RecipeIngredient` models.
  - When a relevant model instance is saved or deleted, the handler identifies dependent `Combination` records.
  - Affected combinations have their `cache_valid` flag set to `False`.

- **Selective Recalculation (Triggered)**:
  - Invalid combinations (`cache_valid=False`) are ignored by default in fetches unless explicitly requested.
  - Recalculation of invalid combinations can be triggered manually or potentially by a background task (using `recalculate_combination` via `POST /combinations/recalculate/`).
  - Dependency tracking (`dependent_ingredients`) helps ensure only necessary calculations are redone during a recalculation cycle.
  - The `update_top_performers` function can be run periodically or after significant updates to refresh the `is_top_performer` flags based on the latest valid scores.

### 4. Signal-Based Invalidation

- Signal handlers automatically mark combinations as invalid when:
  - Recipes are updated or deleted
  - Ingredients are updated or deleted
  - Recipe ingredients are updated or deleted

## API Endpoints

### `GET /combinations/get/`

Get saved combinations from the database. Supports sorting via the `ordering` query parameter (e.g., `ordering=waste_score`, `ordering=-waste_score`, `ordering=ingredients_length`, `ordering=-ingredients_length`).

**Response**:
- Array of combination objects with all details

### `POST /combinations/generate/`

**Unified endpoint:** Generates, fully calculates, saves, and returns optimal recipe combinations.

**Request Body**:
- `servings`: Number of servings for each recipe (default: 1)
- `season_month`: Month to filter for seasonality (1-12, optional)
- `limit`: Maximum number of combinations to return per page (default: 10)
- `offset`: Number of combinations to skip for pagination (default: 0)
- `recipe_filter`: Optional array of recipe IDs to filter by

**Response**:
- Paginated list of newly created/saved combination objects with full details (including waste score, ingredient waste, dependencies, seasonality, ingredient count, `cache_valid=True`, etc.).

### `GET /combinations/get/{id}/`

Get a specific combination by ID.

**Response**:
- Combination object with all details

### `POST /waste-score/calculate/`
 
Calculate waste score for a given set of recipe IDs and servings.

**Request Body**:
- `recipe_ids`: Array of recipe IDs
- `servings`: Number of servings for each recipe

**Response**:
- Waste score and detailed waste information

### `POST /combinations/recalculate/`

Triggers recalculation of *existing* combinations. Used for manual recalculation, handling updates triggered by signals (revalidating `cache_valid=False` combinations), or backfilling specific fields if needed (e.g., if a calculation logic changes). **Not used for initial generation.**

**Request Body**:
- `combination_ids`: Array of specific combination IDs to recalculate (optional). If provided, only these are recalculated.
- `servings`: If provided (and `combination_ids` is not), recalculates *all invalid* (`cache_valid=False`) combinations for that serving size.
- `all_invalid`: Boolean flag (optional). If true, recalculates *all* invalid combinations across all serving sizes (use with caution).

**Response**:
- Message indicating how many combinations were successfully recalculated.

### `GET /combinations/status/`

Retrieves the status of combination calculations.

**Response**:
- Total number of combinations
- Number of valid combinations
- Number of invalid combinations
- Number of top performers
- Number of invalid top performers
- Statistics broken down by serving size

## Data Flow

1. **Combination Generation (Unified Flow)**:
   - User clicks "Find & Save Optimal Combinations" in the frontend (`ControlPanel.jsx`).
   - Frontend (`useCombinations.js`) calls `POST /combinations/generate/` with parameters (servings, month, pagination, filters).
   - Backend (`combination_service.py::generate_optimal_combinations`):
     a. Filters recipes based on criteria.
     b. Generates potential 3-recipe combinations.
     c. Filters combinations (divisibility, seasonality, meat limit).
     d. Performs *full calculation* (waste, dependencies, seasonality, count) for each valid combination.
     e. Saves each fully calculated combination to the database (`cache_valid=True`).
     f. Retrieves the newly saved combinations matching the request parameters.
     g. Sorts the retrieved combinations by waste score.
     h. Returns the paginated list of saved, calculated combinations to the frontend.

2. **Combination Storage**:
   - Storage is now an *automatic part* of the generation process (Step 1e above).
   - When `POST /combinations/generate/` is called, the backend automatically saves the fully calculated, valid combinations to the database.
   - There is no separate "save" action initiated by the user after generation.

3. **Dependency Change Handling (Updates)**:
   - When underlying `Recipe`, `Ingredient`, or `RecipeIngredient` data changes, signal handlers (`signals.py`) are triggered.
   - Signal handlers identify dependent `Combination` records.
   - Affected combinations are marked as invalid by setting `cache_valid=False`.
   - Recalculation of these invalid combinations can be triggered via `POST /combinations/recalculate/` if needed, to update their scores and re-validate them (`cache_valid=True`).

4. **Frontend Access**:
   - The frontend fetches combinations using the API endpoints
   - The `useCombinations` hook provides a clean interface for components
   - Components display combinations with their waste scores and details
   - Users can filter and sort combinations based on various criteria
   - Background recalculation happens without blocking the user interface