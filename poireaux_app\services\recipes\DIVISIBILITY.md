# Recipe Divisibility Checking

This document explains the recipe divisibility checking logic, functionality, and implementation details.

## Overview

The divisibility checking system determines if a recipe has ingredient quantities that are properly divisible based on various criteria. This is important to ensure that recipes can be scaled up or down without resulting in awkward or impractical measurements.

## Data Structure

### Ingredient Measurement Configuration

- Each `Ingredient` model has fields that control how it can be divided:
  - `divisible_by_measurement`: Type of measurement (e.g., 'unit', 'g', 'ml', 'tsp', 'tbsp')
  - `divisible_by_int`: JSON string array of divisors that the ingredient can be divided by (e.g., '[1, 2, 4]')

### Recipe Information

- Recipes are grouped by name to identify related variants
- Each recipe is evaluated individually against divisibility criteria
- The result is stored in the `divisible` field on the `Recipe` model

## Core Function

### `check_recipes_divisibility(recipes)`

The main function that determines if recipes have properly divisible ingredients.

**Input**: 
- `recipes`: List of recipe dictionaries with ingredients

**Output**:
- Dictionary mapping recipe IDs to their divisibility status (boolean)

**Algorithm**:
1. Group recipes by name
2. For each recipe:
   a. Check each ingredient's quantity against its measurement type and allowed divisors
   b. For 'g'/'ml' measurements: Verify the quantity is divisible by the base unit (e.g., 5g, 10g, 15g)
   c. For unit-type measurements: Verify the quantity is either exactly divisible by an allowed divisor or close to a valid fraction
   d. Mark very small quantities (< 0.25) for unit-type measurements as non-divisible
3. Aggregate results and set the divisible flag on each recipe

## Divisibility Criteria

### Weight/Volume Measurements ('g', 'ml')

For ingredients measured by weight or volume:
- A base unit is defined in the first element of `divisible_by_int` (e.g., `[5, 10]` means the base unit is 5)
- Quantities should be multiples of this base unit (e.g., 5g, 10g, 15g...)
- Small deviations are allowed (within 10% of the base unit)

### Count-Based Measurements ('unit', 'tsp', 'tbsp', 'handful', 'sprig', 'pinch')

For ingredients measured by count or standard measures:
- Quantities should be divisible by the values in `divisible_by_int` (e.g., `[1, 2, 4]` allows quantities like 0.5, 0.25, 0.75, 1.0)
- For quantities that aren't exactly divisible, they should be close to a valid fraction (within 0.3)
- Quantities below 0.25 (but > 0) are flagged as non-divisible for these types

## API Endpoint

### `POST /api/recipes/divisibility/check/`

Checks divisibility for all recipes or a specified list.

**Implementation**:
- Defined in `divisibility_views.py` as `check_all_recipes_divisibility`
- Fetches all recipes if none provided in request
- Updates the `divisible` field on each recipe in the database 