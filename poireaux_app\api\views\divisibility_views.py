from rest_framework.decorators import api_view
from rest_framework.response import Response
from ...models import Recipe, RecipeIngredient
from ...services.recipes.recipe_divisibility_service import check_recipes_divisibility
from ...services.recipes.recipe_rounding_service import round_all_recipe_ingredients
from rest_framework import serializers

@api_view(['POST'])
def check_all_recipes_divisibility(request):
    """
    Check divisibility for a group of recipes, using the recipe with the highest
    serving size as the reference for each recipe name group.
    
    POST data should include:
    - recipes: List of recipe objects with their ingredients
    
    Returns:
        Dictionary mapping recipe IDs to their divisibility status
    """
    try:
        data = request.data
        recipes = data.get('recipes', [])
        
        if not recipes:
            # If no recipes provided, fetch all recipes from database
            print("No recipes provided, fetching all recipes from database")
            all_recipes = Recipe.objects.all()
            from ..serializers import RecipeSerializer
            serializer = RecipeSerializer(all_recipes, many=True)
            recipes = serializer.data
        
        # Calculate divisibility for all recipes
        divisibility_results = check_recipes_divisibility(recipes)
        
        # Update the database with the divisibility results
        for recipe_id, is_divisible in divisibility_results.items():
            try:
                recipe = Recipe.objects.get(id=recipe_id)
                if recipe.divisible != is_divisible:
                    print(f"Updating recipe {recipe_id} divisibility from {recipe.divisible} to {is_divisible}")
                    recipe.divisible = is_divisible
                    recipe.save()
            except Recipe.DoesNotExist:
                print(f"Recipe with ID {recipe_id} not found in database")
        
        return Response(divisibility_results)
    
    except Exception as e:
        print(f"Error checking recipes divisibility: {e}")
        return Response({'error': str(e)}, status=500)

@api_view(['POST'])
def round_ingredient_quantities(request):
    """
    Round ingredient quantities based on their measurement types and allowed divisors.
    
    POST data should include:
    - recipes: List of recipe objects with their ingredients
    
    Returns:
        Dictionary mapping recipe IDs to lists of updated ingredient quantities
    """
    try:
        data = request.data
        recipes = data.get('recipes', [])
        
        if not recipes:
            # If no recipes provided, fetch all recipes from database
            print("No recipes provided, fetching all recipes from database")
            all_recipes = Recipe.objects.all()
            from ..serializers import RecipeSerializer
            serializer = RecipeSerializer(all_recipes, many=True)
            recipes = serializer.data
        
        # Calculate rounding updates for all recipes
        rounding_updates = round_all_recipe_ingredients(recipes)
        
        # Apply updates to the database
        for recipe_id, ingredient_updates in rounding_updates.items():
            for update in ingredient_updates:
                ingredient_id = update.get('id')
                rounded_quantity = update.get('quantity')
                
                try:
                    recipe_ingredient = RecipeIngredient.objects.get(id=ingredient_id)
                    
                    # Apply the update
                    recipe_ingredient.quantity = rounded_quantity
                    recipe_ingredient.save()
                    
                    print(f"Updated ingredient {ingredient_id} quantity to {rounded_quantity}")
                except RecipeIngredient.DoesNotExist:
                    print(f"Recipe ingredient with ID {ingredient_id} not found in database")
        
        return Response(rounding_updates)
    
    except Exception as e:
        print(f"Error rounding ingredient quantities: {e}")
        return Response({'error': str(e)}, status=500) 