# Frontend Page Structure Guide

This document outlines the standard folder structure for pages within the `frontend/src/pages/` directory. The goal is to maintain consistency, improve readability, promote modularity, and make it easier for developers to locate, understand, and modify different parts of a page's implementation.

We will use the `frontend/src/pages/combinations/` directory as an example.

## Core Philosophy

Each page or significant feature within the application should reside in its own directory under `frontend/src/pages/`. This directory will contain all the React components, custom hooks, styles, and the main page file specific to that feature. The overarching principle is to **keep individual files (components, hooks) small, focused, and easily understandable.**

This approach ensures that:

*   **Modularity**: Features and their constituent parts are self-contained.
*   **Colocation**: All code related to a specific feature (UI, logic, styles) is grouped together.
*   **Scalability**: New features can be added without cluttering existing codebases.
*   **Maintainability**: Smaller, focused files are easier to debug, test, and refactor.

## Directory Breakdown

A typical page directory, for example `frontend/src/pages/combinations/`, will have the following structure:

```
combinations/
├── components/         # Page-specific React components
├── hooks/              # Page-specific custom React hooks
├── index.jsx           # Main entry file for the page
└── styles/             # Page-specific styles (CSS, SASS, etc.)
```

Let's break down each part with the refined philosophy:

### 1. `index.jsx`

*   **Purpose**: This is the main React component file that defines the page itself. It acts as the entry point for the feature.
*   **Responsibilities**:
    *   Importing and assembling various UI components from the `components/` directory.
    *   Utilizing custom hooks from the `hooks/` directory to manage state and side effects.
    *   Importing page-specific styles from the `styles/` directory.
    *   Defining the overall layout and structure of the page. It should primarily focus on orchestration rather than complex logic or UI rendering itself.
*   **Example (`frontend/src/pages/combinations/index.jsx`):**
    ```javascript
    import React from 'react';
    import { useCombinations } from './hooks/useCombinations';
    import CombinationList from './components/CombinationList';
    import ControlPanel from './components/CombinationControlPanel/CombinationControlPanel'; // Note: ControlPanel itself might be a composite component
    import './styles/combinations.css';

    function CombinationsPage() {
      const { combinations, loading, error /* ...other state and functions */ } = useCombinations();

      return (
        <div className="combinations-container">
          <h1>Optimal Recipe Combinations</h1>
          <ControlPanel /* ...props... */ />
          <CombinationList combinations={combinations} loading={loading} /* ...props... */ />
          {/* ... other high-level page structure ... */}
        </div>
      );
    }

    export default CombinationsPage;
    ```

### 2. `components/`

*   **Purpose**: This directory houses all React components that are specifically created for and used by this page/feature.
*   **Guidelines**:
    *   **Granularity is Key**: Strive to break down UI elements into the smallest reasonable, reusable pieces.
    *   **Sub-directories for Composite Components**: If a component is composed of several smaller, dedicated sub-components, it **should** get its own sub-directory. This sub-directory will contain the main component file (e.g., `CombinationCard.jsx`) and a nested `components/` directory for its parts, and potentially its own `hooks/` or `styles/` if those are specific only to that composite component and its children.
        *   **Example**: The [`CombinationCard`](frontend/src/pages/combinations/components/CombinationCard/CombinationCard.jsx:1) component in [`frontend/src/pages/combinations/components/CombinationCard/`](frontend/src/pages/combinations/components/CombinationCard/) is a good example. It has its own directory because it's made up of smaller parts like `CardHeader.jsx`, `CardRecipeData.jsx`, etc., which reside in `frontend/src/pages/combinations/components/CombinationCard/components/`.
    *   **Single File for Simple Components**: If a component is simple, self-contained, and not composed of smaller, distinct parts, it can exist as a single `.jsx` file directly within the page's `components/` directory.
    *   **Shared Components**: If a component becomes general enough to be used across multiple pages, consider moving it to a shared components directory (e.g., `frontend/src/components/`).
*   **Example Structure within `components/`**:
    ```
    frontend/src/pages/combinations/components/
    ├── CombinationList.jsx         // A relatively simple component
    ├── ErrorMessageDisplay.jsx     // Another simple component
    └── CombinationCard/            // A composite component with its own structure
        ├── CombinationCard.jsx     // The main component file for CombinationCard
        ├── components/             // Sub-components specific to CombinationCard
        │   ├── CardHeader.jsx
        │   ├── CardData.jsx
        │   └── CardRecipes/
        │       ├── CardRecipeData.jsx
        │       └── hooks/
        │           └── useRecipeManagement.js // Hook specific to CardRecipes
        ├── hooks/                  // Hooks specific to CombinationCard (if any, not shown in user example but possible)
        └── styles/                 // Styles specific to CombinationCard
            └── CombinationCard.css
    ```

### 3. `hooks/`

*   **Purpose**: This directory contains custom React Hooks that encapsulate stateful logic and side effects specific to this page/feature.
*   **Guidelines**:
    *   **Single Responsibility**: Each hook should ideally manage a single piece of related logic or a specific concern (e.g., data fetching, form state, a specific UI interaction's state).
    *   **Keep Them Small and Focused**: Avoid creating monolithic hooks that do too many unrelated things. If a hook starts to grow large or handle diverse responsibilities, refactor it into smaller, more focused hooks.
    *   **Naming Convention**: Follow the `useMyHookName.js` convention.
    *   **Clarity and Editability**: The primary goal is to make the logic within hooks easy to understand, test, and modify. Small, focused hooks contribute significantly to this.
    *   **Sub-directories for Related Hooks (Optional but Recommended for Complexity)**: If you have a set of closely related hooks that work together to manage a larger piece of functionality for the page (e.g., different aspects of API interaction for combinations: filtering, pagination, data fetching), you might group them in a sub-directory within the page's `hooks/` folder for better organization.
        *   **Example**: The `frontend/src/pages/combinations/hooks/useCombinations/` directory containing `useCombinationAPI.js`, `useCombinationFilters.js`, etc., which are all parts of the overall `useCombinations` logic.
*   **Example (`frontend/src/pages/combinations/hooks/`):**
    ```
    frontend/src/pages/combinations/hooks/
    ├── useCombinations.js          // Main hook, might orchestrate smaller hooks
    ├── useSeasonality.js           // A separate, focused hook
    └── useCombinations/            // Sub-directory for breaking down complex useCombinations logic
        ├── useCombinationAPI.js
        ├── useCombinationFilters.js
        ├── useCombinationPagination.js
        └── useCombinationState.js
    ```

### 4. `styles/`

*   **Purpose**: This directory holds all styling files (e.g., CSS, SASS, LESS, CSS Modules) that are specific to this page/feature or its main components.
*   **Guidelines**:
    *   **Page-Level Styles**: Styles that apply broadly to the page layout or multiple components within the page reside here (e.g., `combinations.css`).
    *   **Component-Specific Styles**: For composite components that have their own sub-directory (as described in the `components/` section), their specific styles should be co-located within that component's directory (e.g., `frontend/src/pages/combinations/components/CombinationCard/styles/CombinationCard.css`). This keeps component-specific styling tightly coupled with the component itself.
    *   **Scoped Styles**: Use CSS Modules, BEM, or similar conventions to scope styles and avoid conflicts.

## Maintaining the Structure

When developing new features or modifying existing ones:

1.  **New Page/Feature**: Create a new directory under `frontend/src/pages/`.
2.  **Core Files**: Start with an `index.jsx`. Add `components/`, `hooks/`, and `styles/` subdirectories as needed.
3.  **Component Design**:
    *   Identify distinct UI pieces.
    *   If a UI piece is made of smaller parts, create a dedicated directory for it within `components/` and build it from those smaller parts (which go into its own nested `components/` directory).
    *   Simple, indivisible components can be single files.
4.  **Hook Design**:
    *   Identify distinct logical concerns or stateful behaviors.
    *   Create small, focused hooks for each.
    *   If several hooks contribute to a larger feature's logic (like `useCombinations`), consider grouping them in a sub-directory within `hooks/`.
5.  **Styling**: Place page-wide styles in the page's `styles/` directory. For composite components with their own directory, co-locate their styles within that component's `styles/` sub-directory.

By adhering to this refined structure, emphasizing small, focused files and logical grouping for composite elements, our frontend codebase will be significantly more organized, maintainable, and easier for all developers to contribute to effectively.