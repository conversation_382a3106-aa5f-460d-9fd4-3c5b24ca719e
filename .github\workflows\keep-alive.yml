name: Keep Cloud Run Instance Alive

on:
  schedule:
    # Run every 5 minutes
    - cron: '*/5 * * * *'
  workflow_dispatch: # Allow manual triggering

jobs:
  ping-health-endpoint:
    runs-on: ubuntu-latest
    
    steps:
    - name: Ping Health Endpoint
      run: |
        echo "Pinging health endpoint to keep Cloud Run instance warm..."
        response=$(curl -s -o /dev/null -w "%{http_code}" https://poireaux-749928798411.europe-west1.run.app/api/health/)
        
        if [ $response -eq 200 ]; then
          echo "✅ Health check successful (HTTP $response)"
        else
          echo "❌ Health check failed (HTTP $response)"
          exit 1
        fi
        
    - name: Log timestamp
      run: echo "Keep-alive ping completed at $(date)"
