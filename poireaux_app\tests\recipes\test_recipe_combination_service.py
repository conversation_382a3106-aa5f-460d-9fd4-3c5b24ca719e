import json
from datetime import timedelta
from django.test import TestCase
from django.utils import timezone
from unittest.mock import patch, MagicMock

from poireaux_app.models import Recipe, Ingredient, RecipeIngredient, Combination
from poireaux_app.services.recipes.recipe_combination_service import (
    check_combinations_exist,
    get_cached_combinations,
    needs_recalculation,
    mark_combination_invalid,
    mark_combinations_invalid_for_recipe,
    mark_combinations_invalid_for_ingredient,
    mark_top_performers_for_recalculation,
    identify_affected_combinations,
    recalculate_combination,
    recalculate_marked_combinations,
    update_top_performers
)

class RecipeCombinationServiceTestCase(TestCase):
    """Test case for the recipe combination service."""
    
    def setUp(self):
        """Set up test data."""
        # Create test ingredients
        self.ingredient1 = Ingredient.objects.create(
            name="Test Ingredient 1",
            bought_by="weight",
            bought_by_amount=500,  # 500g packaging
            unit="g"
        )
        self.ingredient2 = Ingredient.objects.create(
            name="Test Ingredient 2",
            bought_by="weight",
            bought_by_amount=250,  # 250g packaging
            unit="g"
        )
        self.ingredient3 = Ingredient.objects.create(
            name="Test Ingredient 3",
            bought_by="unit",
            bought_by_amount=6,  # Pack of 6
            unit="unit"
        )
        
        # Create test recipes
        self.recipe1 = Recipe.objects.create(
            name="Test Recipe 1",
            servings=2
        )
        self.recipe2 = Recipe.objects.create(
            name="Test Recipe 2",
            servings=4
        )
        self.recipe3 = Recipe.objects.create(
            name="Test Recipe 3",
            servings=2
        )
        
        # Create recipe ingredients
        RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient1,
            quantity=200,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient3,
            quantity=2,
            unit="unit"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient1,
            quantity=150,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient2,
            quantity=100,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe3,
            ingredient=self.ingredient2,
            quantity=75,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe3,
            ingredient=self.ingredient3,
            quantity=1,
            unit="unit"
        )
        
        # Create test combinations
        self.combination1 = Combination.objects.create(
            name="Test Combination 1",
            servings=2,
            waste_score=25.5,
            seasonality_months=json.dumps([1, 2, 3]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=True,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient3.id])
        )
        self.combination1.recipes.add(self.recipe1, self.recipe2)
        
        self.combination2 = Combination.objects.create(
            name="Test Combination 2",
            servings=2,
            waste_score=30.0,
            seasonality_months=json.dumps([4, 5, 6]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=False,
            dependent_ingredients=json.dumps([self.ingredient2.id, self.ingredient3.id])
        )
        self.combination2.recipes.add(self.recipe1, self.recipe3)
        
        self.combination3 = Combination.objects.create(
            name="Test Combination 3",
            servings=4,
            waste_score=15.0,
            seasonality_months=json.dumps([7, 8, 9]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=True,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient2.id])
        )
        self.combination3.recipes.add(self.recipe2, self.recipe3)
        
        # Create an invalid combination
        self.invalid_combination = Combination.objects.create(
            name="Invalid Combination",
            servings=2,
            waste_score=40.0,
            seasonality_months=json.dumps([10, 11, 12]),
            last_calculated=timezone.now(),
            cache_valid=False,
            is_top_performer=False,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient2.id])
        )
        self.invalid_combination.recipes.add(self.recipe1, self.recipe2)

    def test_check_combinations_exist(self):
        """Test checking if combinations exist."""
        # Test with existing combinations
        self.assertTrue(check_combinations_exist(servings=2))
        
        # Test with specific month
        self.assertTrue(check_combinations_exist(servings=2, month=1))
        self.assertTrue(check_combinations_exist(servings=2, month=4))
        
        # Test with non-existent combinations
        self.assertFalse(check_combinations_exist(servings=3))
        self.assertFalse(check_combinations_exist(servings=2, month=7))  # Month 7 is only in combination3 which has servings=4

    def test_get_cached_combinations(self):
        """Test retrieving cached combinations."""
        # Test retrieving all combinations for servings=2
        combinations = get_cached_combinations(servings=2)
        self.assertEqual(len(combinations), 2)  # Only valid combinations
        self.assertEqual(combinations[0].name, "Test Combination 1")  # Ordered by waste_score
        
        # Test with specific month
        combinations = get_cached_combinations(servings=2, month=1)
        self.assertEqual(len(combinations), 1)
        self.assertEqual(combinations[0].name, "Test Combination 1")
        
        # Test with limit and offset
        combinations = get_cached_combinations(servings=2, limit=1)
        self.assertEqual(len(combinations), 1)
        self.assertEqual(combinations[0].name, "Test Combination 1")
        
        combinations = get_cached_combinations(servings=2, offset=1, limit=1)
        self.assertEqual(len(combinations), 1)
        self.assertEqual(combinations[0].name, "Test Combination 2")
        
        # Test with non-existent combinations
        combinations = get_cached_combinations(servings=3)
        self.assertEqual(len(combinations), 0)

    def test_needs_recalculation_invalid_cache(self):
        """Test checking if a combination needs recalculation when cache is invalid."""
        self.assertTrue(needs_recalculation(self.invalid_combination))

    def test_needs_recalculation_never_calculated(self):
        """Test checking if a combination needs recalculation when it has never been calculated."""
        # Create a combination that has never been calculated
        never_calculated = Combination.objects.create(
            name="Never Calculated",
            servings=2,
            waste_score=0.0,
            last_calculated=None,
            cache_valid=True
        )
        never_calculated.recipes.add(self.recipe1, self.recipe2)
        
        self.assertTrue(needs_recalculation(never_calculated))

    def test_needs_recalculation_recipe_updated(self):
        """Test checking if a combination needs recalculation when a recipe has been updated."""
        # Update a recipe
        self.recipe1.updated_at = timezone.now() + timedelta(hours=1)
        self.recipe1.save()
        
        self.assertTrue(needs_recalculation(self.combination1))

    def test_needs_recalculation_ingredient_updated(self):
        """Test checking if a combination needs recalculation when an ingredient has been updated."""
        # Update an ingredient
        self.ingredient1.updated_at = timezone.now() + timedelta(hours=1)
        self.ingredient1.save()
        
        self.assertTrue(needs_recalculation(self.combination1))

    def test_needs_recalculation_ingredient_deleted(self):
        """Test checking if a combination needs recalculation when a dependent ingredient has been deleted."""
        # Delete a dependent ingredient
        ingredient_id = self.ingredient1.id
        self.ingredient1.delete()
        
        # Manually update the dependent_ingredients to include the deleted ingredient
        self.combination1.dependent_ingredients = json.dumps([ingredient_id, self.ingredient3.id])
        self.combination1.save()
        
        self.assertTrue(needs_recalculation(self.combination1))

    def test_needs_recalculation_no_changes(self):
        """Test checking if a combination needs recalculation when nothing has changed."""
        self.assertFalse(needs_recalculation(self.combination1))

    def test_mark_combination_invalid(self):
        """Test marking a combination as invalid."""
        mark_combination_invalid(self.combination1)
        
        # Refresh from database
        self.combination1.refresh_from_db()
        
        self.assertFalse(self.combination1.cache_valid)

    def test_mark_combinations_invalid_for_recipe(self):
        """Test marking combinations as invalid for a recipe."""
        # Mark combinations for recipe1
        count = mark_combinations_invalid_for_recipe(self.recipe1)
        
        # Should affect combination1 and combination2
        self.assertEqual(count, 2)
        
        # Refresh from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        self.assertFalse(self.combination1.cache_valid)
        self.assertFalse(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)  # Not affected

    def test_mark_combinations_invalid_for_ingredient(self):
        """Test marking combinations as invalid for an ingredient."""
        # Mark combinations for ingredient1
        count = mark_combinations_invalid_for_ingredient(self.ingredient1)
        
        # Should affect combination1 and combination3
        self.assertEqual(count, 2)
        
        # Refresh from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        self.assertFalse(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)  # Not affected
        self.assertFalse(self.combination3.cache_valid)

    def test_mark_top_performers_for_recalculation(self):
        """Test marking top performers for recalculation."""
        # First mark all combinations as invalid
        Combination.objects.all().update(cache_valid=False)
        
        # Mark top performers for servings=2
        count = mark_top_performers_for_recalculation(servings=2)
        
        # Should only count combination1 (top performer with servings=2)
        self.assertEqual(count, 1)
        
        # Mark all top performers
        count = mark_top_performers_for_recalculation()
        
        # Should count combination1 and combination3
        self.assertEqual(count, 2)

    def test_identify_affected_combinations(self):
        """Test identifying affected combinations."""
        # Identify combinations affected by recipe1
        affected_ids = identify_affected_combinations(recipe_id=self.recipe1.id)
        
        # Should include combination1 and combination2
        self.assertEqual(len(affected_ids), 2)
        self.assertIn(self.combination1.id, affected_ids)
        self.assertIn(self.combination2.id, affected_ids)
        
        # Identify combinations affected by ingredient1
        affected_ids = identify_affected_combinations(ingredient_id=self.ingredient1.id)
        
        # Should include combination1 and combination3
        self.assertEqual(len(affected_ids), 2)
        self.assertIn(self.combination1.id, affected_ids)
        self.assertIn(self.combination3.id, affected_ids)
        
        # Identify combinations affected by both
        affected_ids = identify_affected_combinations(
            recipe_id=self.recipe1.id,
            ingredient_id=self.ingredient1.id
        )
        
        # Should include combination1, combination2, and combination3 without duplicates
        self.assertEqual(len(affected_ids), 3)
        self.assertIn(self.combination1.id, affected_ids)
        self.assertIn(self.combination2.id, affected_ids)
        self.assertIn(self.combination3.id, affected_ids)

    @patch('poireaux_app.services.recipes.recipe_combination_service.calculate_waste_score')
    @patch('poireaux_app.services.recipes.recipe_combination_service.check_seasonality_overlap')
    def test_recalculate_combination(self, mock_check_seasonality, mock_calculate_waste):
        """Test recalculating a combination."""
        # Mock the calculate_waste_score function
        mock_calculate_waste.return_value = (10.0, {
            f"{self.ingredient1.id}_g": {
                "ingredient": self.ingredient1,
                "quantity": 350,
                "unit": "g",
                "buy_amount": 500,
                "waste": 150,
                "waste_percentage": 30.0
            }
        })
        
        # Mock the check_seasonality_overlap function
        mock_check_seasonality.return_value = (True, {1, 2, 3, 4})
        
        # Mark the combination as invalid
        self.combination1.cache_valid = False
        self.combination1.save()
        
        # Recalculate the combination
        success = recalculate_combination(self.combination1.id)
        
        # Check that the recalculation was successful
        self.assertTrue(success)
        
        # Refresh from database
        self.combination1.refresh_from_db()
        
        # Check that the combination was updated correctly
        self.assertTrue(self.combination1.cache_valid)
        self.assertEqual(self.combination1.waste_score, 10.0)
        self.assertEqual(json.loads(self.combination1.seasonality_months), [1, 2, 3, 4])
        self.assertEqual(json.loads(self.combination1.dependent_ingredients), [str(self.ingredient1.id)])

    @patch('poireaux_app.services.recipes.recipe_combination_service.recalculate_combination')
    def test_recalculate_marked_combinations(self, mock_recalculate):
        """Test recalculating marked combinations."""
        # Mock the recalculate_combination function
        mock_recalculate.return_value = True
        
        # Mark all combinations as invalid
        Combination.objects.all().update(cache_valid=False)
        
        # Recalculate marked combinations
        count = recalculate_marked_combinations(limit=2)
        
        # Should recalculate 2 combinations (limit)
        self.assertEqual(count, 2)
        self.assertEqual(mock_recalculate.call_count, 2)
        
        # Check that top performers were prioritized
        mock_recalculate.assert_any_call(self.combination1.id)
        mock_recalculate.assert_any_call(self.combination3.id)

    def test_update_top_performers(self):
        """Test updating top performers."""
        # Reset top performers
        Combination.objects.all().update(is_top_performer=False)
        
        # Update top performers
        count = update_top_performers()
        
        # Should mark 2 combinations as top performers (1 for each serving size)
        self.assertEqual(count, 2)
        
        # Refresh from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        self.invalid_combination.refresh_from_db()
        
        # Check that the correct combinations were marked as top performers
        self.assertTrue(self.combination1.is_top_performer)  # Lowest waste_score for servings=2
        self.assertFalse(self.combination2.is_top_performer)
        self.assertTrue(self.combination3.is_top_performer)  # Only combination for servings=4
        self.assertFalse(self.invalid_combination.is_top_performer)