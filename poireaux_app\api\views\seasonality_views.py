from rest_framework.decorators import api_view
from rest_framework.response import Response
from ...models import Recipe
from ...services.recipes.recipe_seasonality_service import (
    calculate_recipe_seasonality,
    format_seasonality,
    calculate_and_update_recipe_seasonality,
    update_all_recipe_seasonality
)
import json
import logging

# Set up logging
logger = logging.getLogger(__name__)

@api_view(['POST'])
def calculate_recipe_seasonality_api(request):
    """
    Calculate a recipe's seasonality based on its ingredients.
    
    POST data should include:
    - ingredients: List of ingredient objects with seasonality data
    - recipe_id: (Optional) Recipe ID for logging
    - recipe_name: (Optional) Recipe name for logging
    
    Returns:
        Dictionary with seasonality data for each month
    """
    try:
        data = request.data
        ingredients = data.get('ingredients', [])
        recipe_id = data.get('recipe_id')
        recipe_name = data.get('recipe_name', 'Unknown Recipe')
        
        if not ingredients:
            return Response({'error': 'No ingredients provided'}, status=400)
        
        # Calculate seasonality
        seasonality = calculate_recipe_seasonality(ingredients, recipe_id, recipe_name)
        
        # If recipe_id is provided, update the database
        if recipe_id:
            try:
                recipe = Recipe.objects.get(id=recipe_id)
                recipe.months = json.dumps(seasonality)
                recipe.save()
                logger.info(f"Updated seasonality in database for recipe ID {recipe_id}")
            except Recipe.DoesNotExist:
                logger.warning(f"Recipe with ID {recipe_id} not found in database")
        
        # Format the seasonality data for frontend display
        formatted_seasonality = format_seasonality(seasonality)
        
        return Response({
            'raw': seasonality,
            'formatted': formatted_seasonality
        })
    
    except Exception as e:
        logger.error(f"Error calculating recipe seasonality: {e}")
        return Response({'error': str(e)}, status=500)

@api_view(['POST'])
def update_all_recipes_seasonality(request):
    """
    Calculate and update seasonality for all recipes in the database.
    
    Returns:
        Count of recipes updated
    """
    try:
        # Update all recipes
        seasonality_results = update_all_recipe_seasonality()
        
        return Response({
            'updated_count': len(seasonality_results),
            'message': f"Updated seasonality for {len(seasonality_results)} recipes"
        })
    
    except Exception as e:
        logger.error(f"Error updating recipe seasonality: {e}")
        return Response({'error': str(e)}, status=500)
    
@api_view(['GET'])
def get_recipe_seasonality(request, pk):
    """
    Get the seasonality data for a specific recipe.
    
    Returns:
        Formatted seasonality data for the recipe
    """
    try:
        recipe = Recipe.objects.get(id=pk)
        
        # Parse the stored seasonality data
        seasonality_data = None
        if recipe.months:
            try:
                seasonality_data = json.loads(recipe.months)
                
                # If it's a string value instead of JSON, recalculate
                if isinstance(seasonality_data, str):
                    logger.warning(f"Recipe {pk} has string value for months: {seasonality_data}")
                    seasonality_data = None
            except json.JSONDecodeError:
                logger.error(f"Error parsing months data for recipe {pk}")
                seasonality_data = None
        
        # If we couldn't parse the stored data, calculate it from ingredients
        if not seasonality_data:
            seasonality_data, formatted_seasonality = calculate_and_update_recipe_seasonality(recipe)
            return Response(formatted_seasonality)
        else:
            # Format the seasonality data for frontend display
            formatted_seasonality = format_seasonality(seasonality_data)
            return Response(formatted_seasonality)
    
    except Recipe.DoesNotExist:
        return Response({'error': 'Recipe not found'}, status=404)
    except Exception as e:
        logger.error(f"Error getting recipe seasonality: {e}")
        return Response({'error': str(e)}, status=500)

@api_view(['POST'])
def get_batch_recipe_seasonality(request):
    """
    Get seasonality data for multiple recipes in a single request.
    
    POST data should include:
    - recipe_ids: List of recipe IDs to fetch seasonality data for
    
    Returns:
        Dictionary mapping recipe IDs to their seasonality data
    """
    try:
        data = request.data
        recipe_ids = data.get('recipe_ids', [])
        
        if not recipe_ids:
            return Response({'error': 'No recipe IDs provided'}, status=400)
        
        # Convert to list if it's not already
        if not isinstance(recipe_ids, list):
            recipe_ids = [recipe_ids]
        
        # Fetch all requested recipes in a single query for efficiency
        recipes = Recipe.objects.prefetch_related('recipe_ingredients__ingredient').filter(id__in=recipe_ids)
        
        # Dictionary to store results
        results = {}
        
        # Process each recipe
        for recipe in recipes:
            # Parse the stored seasonality data
            seasonality_data = None
            if recipe.months:
                try:
                    seasonality_data = json.loads(recipe.months)
                    
                    # Handle string value instead of JSON
                    if isinstance(seasonality_data, str):
                        seasonality_data = None
                except json.JSONDecodeError:
                    seasonality_data = None
            
            # If we don't have valid seasonality data, calculate it
            if not seasonality_data:
                _, formatted_seasonality = calculate_and_update_recipe_seasonality(recipe)
            else:
                # Format the seasonality data for frontend display
                formatted_seasonality = format_seasonality(seasonality_data)
            
            # Store in results dictionary
            results[recipe.id] = formatted_seasonality
        
        # Add empty data for any requested IDs that were not found
        empty_data = {
            'hasSeasonality': False,
            'data': [],
            'seasonalMonths': []
        }
        
        for recipe_id in recipe_ids:
            if str(recipe_id) not in results and recipe_id not in results:
                results[recipe_id] = empty_data
        
        return Response(results)
    
    except Exception as e:
        logger.error(f"Error getting batch recipe seasonality: {e}")
        return Response({'error': str(e)}, status=500) 