.add-ingredient-form {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    max-width: none;
    width: 100%;
    box-sizing: border-box;

}
  
  .ingredient-model-info {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    max-width: none;
    width: 100%;
    box-sizing: border-box;
  }
  
  .ingredient-input {
    padding: 10px;
    margin-right: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 60%;
    font-size: 16px;
    
  }
  
  .add-button {
    padding: 10px 15px;
    background-color: #6a994e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
  }
  
  .add-button:hover {
    background-color: #5a8644;
  }
  
  .ingredient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    padding: 10px 15px;
    margin: 5px 0;
    border-radius: 4px;
    border-left: 4px solid #6a994e;
  }
  
  .delete-btn {
    background-color: #e63946;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .form-message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    background-color: #f8d7da;
    color: #721c24;
    text-align: center;
}


.form-group {
  margin-bottom: 15px;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.ingredient-dropdown {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 16px;
  color: #333;
  box-sizing: border-box;
}

.ingredient-dropdown:focus {
  border-color: #6a994e;
  outline: none;
}

.ingredient-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  box-sizing: border-box;
}

.ingredient-input:focus {
  border-color: #6a994e;
  outline: none;
}

/* Type filter buttons */
.tab-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
  padding: 10px;
  border-radius: 4px;
}

.type-tab {
  padding: 8px 15px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  text-transform: capitalize;
  font-size: 14px;
}

.type-tab:hover {
  background-color: #e9e9e9;
}

.type-tab.active {
  background-color: #6a994e;
  color: white;
  border-color: #386641;
}

/* Table styling for ingredient data */
.table-container {
  max-height: 500px;
  overflow-y: auto;
  margin-bottom: 20px;
  border-radius: 4px;
}

.table-responsive {
  overflow-x: auto;
  width: 100%;
}

.ingredients-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  background-color: transparent;
  box-shadow: none;
  border-radius: 4px;
  font-size: 14px;
  table-layout: auto;
}

.ingredients-table th, 
.ingredients-table td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  white-space: nowrap;
  overflow: visible;
}

.ingredients-table th {
  background-color: rgba(245, 245, 245, 0.7);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.ingredients-table tr:hover {
  background-color: rgba(249, 249, 249, 0.5);
}

/* Column widths */
.ingredients-table th:nth-child(1),
.ingredients-table td:nth-child(1) {
  width: 5%;
  min-width: 50px;
}

.ingredients-table th:nth-child(2),
.ingredients-table td:nth-child(2) {
  width: 15%;
  min-width: 120px;
}

.ingredients-table th:nth-child(3),
.ingredients-table td:nth-child(3) {
  width: 10%;
  min-width: 90px;
}

.ingredients-table th:nth-child(4),
.ingredients-table td:nth-child(4) {
  width: 10%;
  min-width: 90px;
}

.ingredients-table th:nth-child(5),
.ingredients-table td:nth-child(5) {
  width: 10%;
  min-width: 80px;
}

.ingredients-table th:nth-child(6),
.ingredients-table td:nth-child(6) {
  width: 10%;
  min-width: 80px;
}

.ingredients-table th:nth-child(7),
.ingredients-table td:nth-child(7) {
  width: 10%;
  min-width: 90px;
}

.ingredients-table th:nth-child(8),
.ingredients-table td:nth-child(8) {
  width: 10%;
  min-width: 90px;
}

/* Season indicators */
.season-indicator {
  display: flex;
  gap: 2px;
  flex-wrap: nowrap;
}

/* Type cell styling */
.type-cell {
  padding: 3px 8px;
  border-radius: 3px;
  text-transform: capitalize;
}

.type-vegetable {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.type-herb {
  background-color: #f1f8e9;
  color: #558b2f;
}

.type-meat {
  background-color: #ffebee;
  color: #c62828;
}

.type-dairy {
  background-color: #e8eaf6;
  color: #303f9f;
}

.type-oil {
  background-color: #fff8e1;
  color: #ff8f00;
}

.type-spice {
  background-color: #fff3e0;
  color: #e65100;
}

.type-grain {
  background-color: #efebe9;
  color: #5d4037;
}

.type-preserve {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.seasonality-cell {
  min-width: 240px;
}

/* Month indicators */
.month-indicator-container {
  position: relative;
  display: inline-block;
}

.month-indicator {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #e0e0e0;
}

.month-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s;
  white-space: nowrap;
  z-index: 1;
}

.month-indicator-container:hover .month-tooltip {
  visibility: visible;
  opacity: 1;
}

/* Month colors mapping */
.month-active-1 { background-color: #0288d1; } /* January */
.month-active-2 { background-color: #039be5; } /* February */
.month-active-3 { background-color: #4fc3f7; } /* March */
.month-active-4 { background-color: #8bc34a; } /* April */
.month-active-5 { background-color: #7cb342; } /* May */
.month-active-6 { background-color: #689f38; } /* June */
.month-active-7 { background-color: #f57c00; } /* July */
.month-active-8 { background-color: #ef6c00; } /* August */
.month-active-9 { background-color: #e65100; } /* September */
.month-active-10 { background-color: #795548; } /* October */
.month-active-11 { background-color: #5d4037; } /* November */
.month-active-12 { background-color: #4e342e; } /* December */

.App-logo {
  height: 60px;
  margin-bottom: 20px;
}

.ingredient-viewer {
  margin-top: 20px;
  padding: 20px;
  width: 90%;
  max-width: 1400px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Editable cells styles */
.editable-cell {
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
}

.editable-cell:hover:not(.editing) {
  background-color: #f5f5f5;
}

.editable-cell:hover:not(.editing) .edit-icon {
  opacity: 1;
}

.edit-icon {
  opacity: 0;
  font-size: 12px;
  color: #999;
  margin-left: 5px;
  transition: opacity 0.2s;
}

.editable-cell.editing {
  background-color: #f8f9fa;
  padding: 0 !important;
}

.edit-controls {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.edit-controls input,
.edit-controls select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-sizing: border-box;
}

.edit-controls input:focus,
.edit-controls select:focus {
  border-color: #6a994e;
  outline: none;
}

.edit-buttons {
  display: flex;
  gap: 5px;
  margin-top: 5px;
}

.save-button,
.cancel-button {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  flex: 1;
}

.save-button {
  background-color: #6a994e;
  color: white;
}

.save-button:hover:not(:disabled) {
  background-color: #5a8644;
}

.cancel-button {
  background-color: #e63946;
  color: white;
}

.cancel-button:hover:not(:disabled) {
  background-color: #d62c39;
}

.save-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-status {
  font-size: 12px;
  margin-top: 3px;
  text-align: center;
}

.editable-cell.status-saving {
  background-color: #e6f7ff;
}

.editable-cell.status-success {
  background-color: #e6f7e6;
}

.editable-cell.status-error {
  background-color: #ffebee;
}

.status-success .save-status {
  color: #2e7d32;
}

.status-error .save-status {
  color: #c62828;
}

.status-saving .save-status {
  color: #0288d1;
}

/* Add these at the top of the file */
.ingredient-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  background-color: transparent;
}

.ingredient-header {
  background-color: #26a69a;
  color: white;
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.ingredient-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: bold;
}

.ingredient-header p {
  margin: 10px 0 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.ingredient-list-container {
  width: 100%;
  padding: 0;
  margin: 0 auto;
  box-sizing: border-box;
}

.ingredient-list {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ingredient-container {
    padding: 10px;
  }
  
  .ingredient-header {
    padding: 20px;
  }
}

/* Make any "white" containers transparent */
.ingredient-item,
.table-container,
.table-responsive {
  background-color: transparent !important;
  box-shadow: none !important;
}




