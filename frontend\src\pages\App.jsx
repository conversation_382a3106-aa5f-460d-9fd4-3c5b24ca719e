import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Ingredient from './ingredient';
import Recipe from './recipe';
import Combinations from './combinations';
import SavedCombinations from './savedCombinations';
import NavMenu from './NavMenu';
import '../styles/App.css';


function App() {
  // Check if the environment is production
  // Create React App sets NODE_ENV to 'production' for build, and 'development' for start
  const isProduction = process.env.NODE_ENV === 'production';

  return (
    <BrowserRouter>
      <div className="App">
        <NavMenu isProduction={isProduction} /> {/* Pass isProduction to NavMenu */}
        <Routes>
          {isProduction ? (
            // In Production: Only combinations page is available
            <>
              <Route path="/combinations" element={<Combinations isProduction={isProduction} />} />
              <Route path="/saved-combinations" element={<SavedCombinations />} />
              {/* Redirect any other path to /combinations */}
              <Route path="*" element={<Navigate to="/combinations" replace />} />
            </>
          ) : (
            // In Development: All pages are available
            <>
              <Route path="/" element={<Ingredient />} />
              <Route path="/recipes" element={<Recipe />} />
              <Route path="/combinations" element={<Combinations isProduction={isProduction} />} />
              <Route path="/saved-combinations" element={<SavedCombinations />} />
              {/* You might want a catch-all or a 404 page for development too */}
            </>
          )}
        </Routes>
      </div>
    </BrowserRouter>
  );
}

export default App;
