import React, { useState, useRef, useEffect } from 'react';
import { useSwipeable } from 'react-swipeable';
import RecipeCard from '../RecipeCard/RecipeCard';
import './RecipeScroller.css';

const RecipeScroller = ({ combination, onCardClick }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollerRef = useRef(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Touch handling state for finger-following horizontal scrolling
  const [touchState, setTouchState] = useState({
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    isDragging: false,
    isHorizontalSwipe: false,
    startIndex: 0,
    hasMovedHorizontally: false
  });

  // Extract recipes from combination
  const recipes = React.useMemo(() => {
    if (!combination?.recipes || !Array.isArray(combination.recipes)) {
      return [];
    }

    return combination.recipes.map((recipeData) => {
      const recipeId = recipeData.id;
      const name = recipeData.name;
      let ingredients = [];
      const idStr = recipeId.toString();
      const idKeyPrefixed = `Recipe ID: ${recipeId}`;

      if (combination.recipe_specific_ingredients?.[idStr]) {
        ingredients = combination.recipe_specific_ingredients[idStr];
      } else if (combination.recipe_specific_ingredients?.[idKeyPrefixed]) {
        ingredients = combination.recipe_specific_ingredients[idKeyPrefixed];
      }

      return {
        id: recipeId,
        name: name || `Unnamed Recipe (ID: ${recipeId})`,
        specific_ingredients: ingredients || [],
        image: recipeData.image,
        instructions: recipeData.instructions,
      };
    });
  }, [combination]);

  // Remove keyboard navigation from RecipeScroller to avoid conflicts with CombinationScroller
  // Recipe navigation will be handled by clicking on side recipes or using the small navigation buttons

  const goToNext = () => {
    if (isTransitioning || recipes.length === 0) return;

    setIsTransitioning(true);
    // Circular scrolling: if at the end, go to the beginning
    setCurrentIndex(prev => (prev + 1) % recipes.length);

    setTimeout(() => setIsTransitioning(false), 300);
  };

  const goToPrevious = () => {
    if (isTransitioning || recipes.length === 0) return;

    setIsTransitioning(true);
    // Circular scrolling: if at the beginning, go to the end
    setCurrentIndex(prev => (prev - 1 + recipes.length) % recipes.length);

    setTimeout(() => setIsTransitioning(false), 300);
  };

  // Touch handlers for finger-following horizontal scrolling
  const handleTouchStart = (e) => {
    if (window.innerWidth > 768 || recipes.length <= 1) return; // Only on mobile and when there are multiple recipes

    const touch = e.touches[0];
    setTouchState({
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      isDragging: true,
      isHorizontalSwipe: false,
      startIndex: currentIndex,
      hasMovedHorizontally: false
    });
    setIsTransitioning(false);
  };

  const handleTouchMove = (e) => {
    if (window.innerWidth > 768 || !touchState.isDragging) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - touchState.startX;
    const deltaY = touch.clientY - touchState.startY;
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // Determine if this is a horizontal swipe early on
    if (!touchState.isHorizontalSwipe && !touchState.hasMovedHorizontally) {
      if (absDeltaX > 10 || absDeltaY > 10) { // Reduced threshold for faster detection
        const isHorizontal = absDeltaX > absDeltaY;
        setTouchState(prev => ({
          ...prev,
          isHorizontalSwipe: isHorizontal,
          hasMovedHorizontally: true
        }));

        if (!isHorizontal) {
          // This is a vertical swipe, let it pass through
          return;
        }
      } else {
        return; // Not enough movement yet
      }
    }

    // If we determined this is a horizontal swipe, handle it
    if (touchState.isHorizontalSwipe || (touchState.hasMovedHorizontally && absDeltaX > absDeltaY)) {
      e.preventDefault();
      e.stopPropagation();

      setTouchState(prev => ({
        ...prev,
        currentX: touch.clientX,
        currentY: touch.clientY
      }));

      // Update recipe positions in real-time
      updateRecipePositionsDuringTouch(deltaX);
    }
  };

  const handleTouchEnd = (e) => {
    if (window.innerWidth > 768 || !touchState.isDragging) return;

    const deltaX = touchState.currentX - touchState.startX;
    const deltaY = touchState.currentY - touchState.startY;
    const threshold = 30; // Reduced threshold for easier triggering

    // Reset touch state
    setTouchState(prev => ({
      ...prev,
      isDragging: false,
      isHorizontalSwipe: false,
      hasMovedHorizontally: false
    }));

    // Only handle if this was determined to be a horizontal swipe
    if (touchState.isHorizontalSwipe && Math.abs(deltaX) > threshold) {
      e.preventDefault();
      e.stopPropagation();

      if (deltaX > 0) {
        // Swiped right - go to previous
        goToPrevious();
      } else {
        // Swiped left - go to next
        goToNext();
      }
    } else {
      // Snap back to current position
      setIsTransitioning(true);
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  const handleTouchCancel = () => {
    // Reset touch state if touch is cancelled
    setTouchState(prev => ({
      ...prev,
      isDragging: false,
      isHorizontalSwipe: false,
      hasMovedHorizontally: false
    }));
    setIsTransitioning(true);
    setTimeout(() => setIsTransitioning(false), 300);
  };

  const updateRecipePositionsDuringTouch = (deltaX) => {
    if (!scrollerRef.current) return;

    const wheel = scrollerRef.current.querySelector('.recipe-wheel');
    if (!wheel) return;

    const items = wheel.querySelectorAll('.recipe-wheel-item');
    if (items.length === 0) return;

    const containerWidth = scrollerRef.current.offsetWidth;
    const maxDrag = containerWidth * 0.3; // Limit drag distance
    const clampedDeltaX = Math.max(-maxDrag, Math.min(maxDrag, deltaX));
    const dragRatio = clampedDeltaX / containerWidth;

    items.forEach((item, itemIndex) => {
      // Find the actual recipe index for this item
      const recipeIndex = parseInt(item.getAttribute('data-recipe-index')) || itemIndex;
      const relativeIndex = recipeIndex - currentIndex;

      // Base position calculation
      let baseTranslateX = 0;
      let baseScale = 0.75;
      let baseOpacity = 0.5;

      if (relativeIndex === 0) {
        // Active item
        baseTranslateX = 0;
        baseScale = 1;
        baseOpacity = 1;
      } else if (relativeIndex === -1) {
        // Previous item
        baseTranslateX = -200;
        baseScale = 0.75;
        baseOpacity = 0.5;
      } else if (relativeIndex === 1) {
        // Next item
        baseTranslateX = 200;
        baseScale = 0.75;
        baseOpacity = 0.5;
      } else {
        // Hidden items
        baseTranslateX = relativeIndex * 200;
        baseScale = 0.5;
        baseOpacity = 0;
      }

      // Apply drag offset
      const dragOffset = dragRatio * 200;
      const newTranslateX = baseTranslateX + dragOffset;

      // Smooth scale transition during drag
      const scaleOffset = Math.abs(dragRatio) * 0.1;
      const newScale = relativeIndex === 0 ? Math.max(0.9, baseScale - scaleOffset) : baseScale;

      item.style.transition = 'none';
      item.style.transform = `translateX(${newTranslateX}px) scale(${newScale})`;
      item.style.opacity = baseOpacity;
    });
  };



  // Reset to first item if current index is out of bounds
  useEffect(() => {
    if (recipes.length > 0 && currentIndex >= recipes.length) {
      setCurrentIndex(0);
    }
  }, [recipes.length, currentIndex]);

  // Restore transitions when not dragging
  useEffect(() => {
    if (!touchState.isDragging && scrollerRef.current) {
      const wheel = scrollerRef.current.querySelector('.recipe-wheel');
      if (wheel) {
        const items = wheel.querySelectorAll('.recipe-wheel-item');
        items.forEach((item) => {
          item.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
          // Reset any manual transforms when not dragging
          item.style.transform = '';
          item.style.opacity = '';
        });
      }
    }
  }, [touchState.isDragging]);

  // Cleanup touch state when component unmounts or recipes change
  useEffect(() => {
    setTouchState(prev => ({
      ...prev,
      isDragging: false,
      isHorizontalSwipe: false,
      hasMovedHorizontally: false
    }));
  }, [recipes.length]);

  // Remove swipe handlers from RecipeScroller to avoid conflicts with CombinationScroller
  // Recipe navigation will be handled by clicking on side recipes or using the small navigation buttons

  // Get visible recipes for wheel effect
  const getVisibleRecipes = () => {
    if (recipes.length === 0) return [];
    
    const visibleRecipes = [];
    const totalVisible = Math.min(3, recipes.length); // Show up to 3 recipes at once
    
    if (totalVisible === 1) {
      // Only one recipe, show it centered
      visibleRecipes.push({
        recipe: recipes[0],
        index: 0,
        position: 0,
        isActive: true
      });
    } else if (totalVisible === 2) {
      // Two recipes, show current and next
      for (let i = 0; i < 2; i++) {
        const index = (currentIndex + i) % recipes.length;
        visibleRecipes.push({
          recipe: recipes[index],
          index,
          position: i - 0.5, // -0.5, 0.5
          isActive: index === currentIndex
        });
      }
    } else {
      // Three or more recipes, show previous, current, next
      for (let i = 0; i < 3; i++) {
        const index = (currentIndex - 1 + i + recipes.length) % recipes.length;
        visibleRecipes.push({
          recipe: recipes[index],
          index,
          position: i - 1, // -1, 0, 1
          isActive: index === currentIndex
        });
      }
    }
    
    return visibleRecipes;
  };

  const visibleRecipes = getVisibleRecipes();

  if (!combination || recipes.length === 0) {
    return (
      <div className="recipe-scroller-empty">
        <p>No recipes available in this combination</p>
      </div>
    );
  }

  return (
    <div className="recipe-scroller-container">
      {/* Subtle Recipe Navigation Buttons - only show if more than 1 recipe */}
      {recipes.length > 1 && (
        <div className="recipe-nav-controls">
          <button
            className="recipe-nav-button recipe-nav-button-prev"
            onClick={goToPrevious}
            disabled={isTransitioning}
            aria-label="Previous recipe"
            title="Previous recipe (click side recipes or use these buttons)"
          >
            &#8249;
          </button>

          <button
            className="recipe-nav-button recipe-nav-button-next"
            onClick={goToNext}
            disabled={isTransitioning}
            aria-label="Next recipe"
            title="Next recipe (click side recipes or use these buttons)"
          >
            &#8250;
          </button>
        </div>
      )}

      {/* Main Scroller with Wheel Effect */}
      <div
        className="recipe-scroller"
        ref={scrollerRef}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onTouchCancel={handleTouchCancel}
      >
        <div className="recipe-wheel">
          {visibleRecipes.map(({ recipe, index, position, isActive }) => (
            <div
              key={recipe.id}
              className={`recipe-wheel-item ${isActive ? 'active' : ''} position-${position}`}
              data-recipe-index={index}
              onClick={() => {
                if (!isActive) {
                  if (position < 0) {
                    goToPrevious();
                  } else if (position > 0) {
                    goToNext();
                  }
                } else if (onCardClick) {
                  onCardClick(combination);
                }
              }}
            >
              <RecipeCard 
                recipe={recipe} 
                combination={combination}
                isActive={isActive}
              />
            </div>
          ))}
        </div>
      </div>



      {/* Recipe Counter - only show if more than 1 recipe */}
      {recipes.length > 1 && (
        <div className="recipe-counter">
          <span className="recipe-counter-label">Recipe:</span>
          <span className="recipe-counter-value">{currentIndex + 1} / {recipes.length}</span>
        </div>
      )}
    </div>
  );
};

export default RecipeScroller;
