/* Combination Cards Grid */
.combination-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Responsive grid */
  gap: 20px; /* Space between cards */
  padding: 20px 0; /* Padding around the grid */
  justify-items: center; /* Center the cards within their grid cells */
  position: relative; /* For pop-up positioning */
}

/* Loading State Styles */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #555; /* Medium grey text */
}

.loading-spinner {
  border: 4px solid #f3f3f3; /* Light grey border */
  border-top: 4px solid #3498db; /* Blue for spinner */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite; /* Spin animation */
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.1em;
}

/* Background Loading Indicator for Fast Loading */
.background-loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(52, 152, 219, 0.9);
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  z-index: 1000;
  font-size: 0.9em;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.loading-bar {
  width: 20px;
  height: 3px;
  background: rgba(255,255,255,0.3);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.loading-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: white;
  animation: loading-bar-animation 1.5s infinite;
}

@keyframes loading-bar-animation {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* No Combinations Message */
.no-combinations {
  text-align: center;
  padding: 40px;
  color: #777; /* Slightly lighter grey */
  background-color: #f9f9f9; /* Very light grey background */
  border-radius: 8px;
  margin-top: 20px;
}

.no-combinations p {
  font-size: 1.2em;
  margin-bottom: 10px;
}
