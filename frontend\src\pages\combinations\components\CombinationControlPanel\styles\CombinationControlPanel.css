/* Control Panel Styles */
.controls {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 10px; /* Further reduced from 15px */
  margin-bottom: 10px; /* Further reduced from 15px */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Control group styles for all form controls */
.control-group {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #495057;
  min-width: 70px;
  margin-right: 8px;
}

.control-group select {
  padding: 5px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.75rem;
  background-color: white;
  cursor: pointer;
  flex-grow: 1;
  max-width: 200px;
}

.calculation-status {
  margin-top: 8px; /* Reduced from 10px */
  padding: 8px; /* Reduced from 10px */
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 0.75em; /* Further reduced from 0.9em */
}

.last-calculated {
  display: block;
  margin-bottom: 5px;
  color: #666;
}

.recalculation-progress {
  margin-top: 8px;
}

.progress-bar {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.85em;
  color: #666;
}

.button-group {
  display: flex;
  gap: 6px; /* Further reduced from 8px */
  margin-bottom: 8px; /* Further reduced from 12px */
}

.generate-btn, .recalculate-btn {
  padding: 5px 8px; /* Further reduced from 6px 10px */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.75rem; /* Further reduced from 0.85rem */
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.generate-btn {
  background-color: #007bff;
  color: white;
}

.generate-btn:hover {
  background-color: #0069d9;
}

.generate-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.recalculate-btn {
  background-color: #6c757d;
  color: white;
}

.recalculate-btn:hover {
  background-color: #5a6268;
}

.recalculate-btn:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.filter-section {
  margin-top: 8px; /* Further reduced from 12px */
  border-top: 1px solid #dee2e6;
  padding-top: 8px; /* Further reduced from 12px */
}

.filters-container {
  margin-top: 8px; /* Further reduced from 12px */
  padding: 8px; /* Further reduced from 12px */
  background-color: #f1f3f5;
  border-radius: 4px;
}

.filter-group, .sort-group {
  margin-bottom: 8px; /* Further reduced from 12px */
}

.filter-group h4, .sort-group h4 {
  margin-top: 0;
  margin-bottom: 4px; /* Further reduced from 6px */
  font-size: 0.8em; /* Further reduced from 0.9em */
  color: #495057;
}

.filter-row {
  margin-bottom: 6px; /* Further reduced from 8px */
  display: flex;
  align-items: center;
}

.filter-row label {
  margin-right: 6px; /* Further reduced from 8px */
  min-width: 80px; /* Further reduced from 90px */
  font-size: 0.75rem; /* Further reduced from 0.85rem */
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 6px; /* Reduced from 8px */
}

.range-inputs input {
  width: 50px; /* Further reduced from 55px */
  padding: 4px; /* Further reduced from 5px */
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.75rem; /* Further reduced from 0.9rem */
}

.filter-row input[type="text"] {
  flex-grow: 1;
  padding: 5px; /* Further reduced from 6px */
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.75rem; /* Further reduced from 0.9rem */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .controls {
    padding: 4px; /* Much smaller padding for mobile */
    margin-bottom: 4px; /* Much smaller margin for mobile */
  }

  .control-group {
    margin-bottom: 4px; /* Much smaller margin to fit more controls */
    flex-direction: column;
    align-items: flex-start;
    gap: 2px; /* Reduced gap */
  }

  .control-group label {
    font-size: 0.75rem; /* Smaller font size */
    margin-bottom: 2px; /* Smaller margin */
    margin-right: 0;
    min-width: auto;
  }

  .control-group select {
    padding: 4px 8px; /* Smaller padding */
    font-size: 0.75rem; /* Smaller font size */
    width: 100%;
    max-width: 100%;
  }

  .button-group {
    flex-direction: column;
    margin-bottom: 4px; /* Much smaller margin */
  }

  .generate-btn, .recalculate-btn {
    padding: 4px 8px; /* Much smaller buttons */
    font-size: 0.75rem; /* Much smaller text */
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px; /* Reduced margin */
  }

  .filter-row label {
    margin-bottom: 5px;
    font-size: 0.9rem; /* Smaller labels */
  }

  .range-inputs {
    width: 100%;
  }

  .range-inputs input {
    width: 50px; /* Smaller inputs */
    padding: 4px; /* Reduced padding */
  }

  .filter-row input[type="text"] {
    padding: 6px; /* Reduced padding */
    font-size: 0.9rem; /* Smaller text */
  }

  .filters-container {
    padding: 10px; /* Reduced padding */
    margin-top: 10px; /* Reduced margin */
  }

  .filter-group, .sort-group {
    margin-bottom: 10px; /* Reduced margin */
  }

  .filter-group h4, .sort-group h4 {
    font-size: 0.9rem; /* Smaller headings */
    margin-bottom: 8px; /* Reduced margin */
  }
}
