import axios from 'axios';
import { useCallback } from 'react';
import { API_BASE_URL } from '../../../../config/api';

export const useCombinationAPI = () => {
  const fetchCombinationsAPI = useCallback(async (currentServings = 1, currentMonth = null, currentDiet = 'all') => {
    const params = new URLSearchParams({
      servings: currentServings,
    });

    if (currentMonth !== null && currentMonth !== undefined) {
      params.append('month', currentMonth);
    }

    if (currentDiet !== 'all') {
      params.append('diet', currentDiet);
    }

    // Use the combinations endpoint
    const baseUrl = `${API_BASE_URL}/combinations/get/`;
    const url = `${baseUrl}?${params.toString()}`;

    console.log(`Fetching combinations from endpoint (API Hook):`, url);

    try {
      const response = await axios.get(url);
      if (response.data && response.data.error) {
        throw new Error(response.data.error);
      }

      // Handle both paginated and direct array responses
      if (Array.isArray(response.data)) {
          return response.data; // Direct array response
      } else if (response.data && response.data.results && Array.isArray(response.data.results)) {
          return response.data.results; // Paginated response (from new endpoint)
      }

      console.error('Unexpected response structure from fetchCombinationsAPI:', response.data);
      throw new Error('Received invalid data format from server during fetch.');

    } catch (error) {
      throw error;
    }
  }, []);

  const findAndSaveCombinationsAPI = useCallback(async (currentServings, currentMonth, page = 1) => { // Removed currentOrdering
    const params = {
      servings: currentServings,
      page: page,
    };

    if (currentMonth !== null && currentMonth !== undefined) {
      params.season_month = currentMonth;
    }

    console.log('Calling POST /combinations/generate/ with params (API Hook):', params);
    const response = await axios.post(`${API_BASE_URL}/combinations/generate/`, params);
    
    if (response.data && response.data.error) {
      throw new Error(response.data.error);
    }
    if (response.data && response.data.detail) { // Handle DRF detail errors
      throw new Error(response.data.detail);
    }
    if (Array.isArray(response.data)) {
        return response.data;
    }
    console.error('Unexpected response structure from findAndSaveCombinationsAPI: Expected an array, received:', response.data);
    throw new Error('Received invalid data format from server during find/save. Expected an array.');
  }, []);

  const saveCombinationAPI = useCallback(async (combinationId) => {
    const response = await axios.post(`${API_BASE_URL}/saved-combinations/`, { combination_id: combinationId });
    if (response.data && response.data.error) {
      throw new Error(response.data.error);
    }
    return response.data;
  }, []);

  return {
    fetchCombinationsAPI,
    findAndSaveCombinationsAPI,
    saveCombinationAPI,
  };
};
