"""
API views for handling recipe-related operations.
"""
import time
import traceback

from django.core.exceptions import ObjectDoesNotExist
from rest_framework import status
from rest_framework.decorators import api_view, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.response import Response

from poireaux_app.services.recipes.recipe_processing_service import \
    process_recipe_and_variations

from ...services.recipes.recipe_divisibility_service import \
    check_recipes_divisibility
from ...services.recipes.recipe_image_service import \
    generate_and_upload_recipe_image
from ...services.recipes.recipe_rounding_service import \
    round_all_recipe_ingredients
from ...utils.recipe import recipe_crud_utils
from ..serializers import RecipeIngredientSerializer, RecipeSerializer

@api_view(['POST'])
def process_new_recipe_backend(request):
    """
    New endpoint to process an original recipe and its variations.
    Expects: {"original_recipe_id": &lt;integer&gt;}
    """
    try:
        original_recipe_id = request.data.get('original_recipe_id')
        if original_recipe_id is None:  # Check for None explicitly
            return Response(
                {'error': 'original_recipe_id is required and cannot be null.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not isinstance(original_recipe_id, int):
            return Response(
                {'error': 'original_recipe_id must be an integer.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Call the new service function
        result = process_recipe_and_variations(original_recipe_id)

        if result.get("success"):
            return Response(result, status=status.HTTP_200_OK)
        else:
            # Determine appropriate status code based on error type if possible
            # For now, using 400 for general processing errors from the service
            error_message = result.get("error", "An unknown error occurred during processing.")
            if "not found" in error_message.lower():
                return Response(result, status=status.HTTP_404_NOT_FOUND)
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except (ValueError, TypeError) as e:
        print(f"--- Backend ERROR: Invalid input in process_new_recipe_backend: {str(e)} ---")
        print(traceback.format_exc())
        return Response(
            {'error': f"Invalid input: {str(e)}"},
            status=status.HTTP_400_BAD_REQUEST
        )
    except ObjectDoesNotExist as e:
        print(f"--- Backend ERROR: Recipe not found in process_new_recipe_backend: {str(e)} ---")
        print(traceback.format_exc())
        return Response(
            {'error': f"Recipe not found: {str(e)}"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:  # pylint: disable=broad-except
        # Log the exception for debugging
        print(f"--- Backend ERROR: Unexpected error in process_new_recipe_backend: {str(e)} ---")
        print(traceback.format_exc())
        return Response(
            {'error': f"An unexpected server error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def get_recipes(_):
    """
    Get all recipes with their ingredients
    """
    start_time = time.time()
    recipes = recipe_crud_utils.get_all_recipes()
    serializer = RecipeSerializer(recipes, many=True)
    # Log performance metrics
    execution_time = time.time() - start_time
    print(
        f"--- Recipe serialization completed in {execution_time:.2f} seconds ---"
    )
    return Response(serializer.data)

@api_view(['POST'])
@parser_classes([MultiPartParser, FormParser])
def create_recipe(request):
    """
    Create a new recipe with ingredients and a generated image.
    """
    # Log the incoming request data
    print("--- Backend: Received request data ---")
    print(request.data)

    # Make a mutable copy of the request data to allow modification
    mutable_data = request.data.copy()

    # If no image is provided, generate one automatically
    if 'image' not in mutable_data or not mutable_data['image']:
        recipe_name = mutable_data.get('name')
        recipe_instructions = mutable_data.get('instructions')
        if recipe_name:
            try:
                print(
                    "--- Backend: No image provided, generating one automatically. ---"
                )
                image_url = generate_and_upload_recipe_image(recipe_name, recipe_instructions)
                mutable_data['image'] = image_url
                print(
                    f"--- Backend: Added generated image URL to request: {image_url} ---"
                )
            except Exception as e:  # pylint: disable=broad-except # Keeping broad for non-critical image generation
                # This is a non-critical error, so we just log it and continue.
                print(
                    f"--- Backend WARNING: Failed to generate or upload image: {e} ---"
                )

    # Create the recipe using the serializer with the modified data
    recipe_serializer = RecipeSerializer(data=mutable_data)
    if recipe_serializer.is_valid(raise_exception=True):  # Raise exception for detailed errors
        print("--- Backend: RecipeSerializer is valid ---")

        # Save the recipe and its nested ingredients
        recipe = recipe_serializer.save()

        # Return the created recipe with all its data
        return Response(
            RecipeSerializer(recipe).data, status=status.HTTP_201_CREATED
        )

    # This block is technically unreachable due to `raise_exception=True`,
    # but kept for clarity.
    print("--- Backend: RecipeSerializer is NOT valid ---")
    print(recipe_serializer.errors)
    return Response(
        recipe_serializer.errors, status=status.HTTP_400_BAD_REQUEST
    )


@api_view(['DELETE'])
def delete_recipe(_, pk):
    """
    Delete a recipe by ID and optionally its serving variations
    """
    try:
        result = recipe_crud_utils.delete_recipe(pk)
        return Response(result)
    except ObjectDoesNotExist as e:
        print(f"--- Backend ERROR: Recipe not found: {e} ---")
        return Response(
            {"error": f"Recipe not found: {e}"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:  # pylint: disable=broad-except # Keeping broad for general server errors
        print(f"--- Backend ERROR: Failed to delete recipes: {e} ---")
        print(traceback.format_exc())
        return Response(
            {"error": f"Failed to delete recipes: {e}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PATCH'])
@parser_classes([MultiPartParser, FormParser])
def update_recipe(request, pk):
    """
    Update a recipe by ID
    """
    try:
        # Log the incoming request data
        print("--- Backend: Received update data ---")
        print(request.data)
        # Update the recipe
        updated_recipe = recipe_crud_utils.update_recipe(pk, request.data)
        # Return the updated recipe with all its data
        return Response(RecipeSerializer(updated_recipe).data)
    except ObjectDoesNotExist as e:
        print(f"--- Backend ERROR: Failed to update recipe: {e} ---")
        return Response({'error': str(e)}, status=status.HTTP_404_NOT_FOUND)


@api_view(['PATCH'])
def update_recipe_ingredient(_, pk, data):
    """
    Update a recipe ingredient by ID
    """
    try:
        # Log the incoming request data
        print("--- Backend: Received recipe ingredient update data ---")
        print(data)
        # Update the recipe ingredient
        updated_ingredient = recipe_crud_utils.update_recipe_ingredient(
            pk, data
        )
        # Return the updated recipe ingredient
        return Response(RecipeIngredientSerializer(updated_ingredient).data)
    except ObjectDoesNotExist as e:
        print(
            f"--- Backend ERROR: Failed to update recipe ingredient: {e} ---"
        )
        return Response({'error': str(e)}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
def generate_servings(request):
    """
    Generate serving variations (1-4 servings) for recipes marked as (original)
    """
    try:
        # Extract the original recipe ID
        original_recipe_id = request.data.get('original_recipe_id')

        # Generate serving variations
        created_recipes = recipe_crud_utils.generate_serving_variations(original_recipe_id)

        return Response({
            'message': f'Successfully generated {len(created_recipes)} recipes',
            'created_recipes': created_recipes
        })
    except ValueError as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:  # pylint: disable=broad-except # Keeping broad for general server errors
        print(
            f"--- Backend ERROR: Failed to generate serving variations: {e} ---"
        )
        print(traceback.format_exc())
        return Response(
            {"error": f"Failed to generate serving variations: {e}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def process_recipe_fully(request):
    """
    Processes a recipe fully:
    1. Generates serving variations.
    2. Rounds ingredients for the original and variations.
    3. Checks divisibility for the original and variations.
    """
    try:
        original_recipe_id = request.data.get('original_recipe_id')
        if not original_recipe_id:
            return Response({'error': 'original_recipe_id is required'}, status=400)

        # --- Step 1: Attempt to generate serving variations for the specific original_recipe_id ---
        specific_variation_ids = []
        variation_generation_note = None  # To store messages like "variations already exist"

        try:
            print(f"Attempting to generate variations for original recipe ID: "
                  f"{original_recipe_id}")
            # This call might raise ValueError if variations exist.
            generated_info = recipe_crud_utils.generate_serving_variations(
                original_recipe_id
            )
            specific_variation_ids = [var_info['id'] for var_info in generated_info]
            if specific_variation_ids:
                print(f"Successfully generated {len(specific_variation_ids)} "
                      f"new variations for recipe ID {original_recipe_id}.")
            else:
                variation_generation_note = (
                    "No new variations were generated for the specified original recipe "
                    "(they may already exist or not be applicable)."
                )
                print(variation_generation_note)
        except ValueError as exc: # Catch ValueError directly
            variation_generation_note = str(exc)
            print(f"Note during variation generation for recipe ID "
                  f"{original_recipe_id}: {variation_generation_note}")
        except Exception as e:  # pylint: disable=broad-except # General server errors during variation generation
            print("--- Backend ERROR: Failed to generate serving variations for "
                  f"recipe ID {original_recipe_id}: {e} ---")
            print(traceback.format_exc())
            return Response(
                {'error': "Failed to generate serving variations for "
                          f"recipe ID {original_recipe_id}: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # --- Step 2: Fetch ALL recipes for global processing ---
        print("Fetching all recipes for global rounding and divisibility checks...")
        all_recipes_objects = recipe_crud_utils.get_all_recipes()
        if not all_recipes_objects:
            return Response({
                'message': 'No recipes found for global processing.',
                'original_recipe_id_processed_for_variations': original_recipe_id,
                'variation_generation_note': variation_generation_note,
                'generated_specific_variations_count': len(specific_variation_ids)
            }, status=status.HTTP_200_OK)

        all_recipes_data = RecipeSerializer(
            all_recipes_objects, many=True
        ).data
        print(
            f"Fetched {len(all_recipes_objects)} recipes for global processing."
        )

        # --- Step 3: Round ingredients for ALL recipes ---
        global_rounding_results = {}
        try:
            print("Starting global ingredient rounding for all fetched recipes...")
            global_rounding_results = round_all_recipe_ingredients(
                all_recipes_data
            )
            print("Global ingredient rounding complete.")
        except Exception as e:  # pylint: disable=broad-except # General server errors during rounding
            print(
                f"--- Backend ERROR: Failed during global ingredient rounding: {e} ---"
            )
            print(traceback.format_exc())
            global_rounding_results = {'error': str(e), 'status': 'failed'}

        # --- Step 4: Check divisibility for ALL recipes ---
        global_divisibility_results = {}
        try:
            print(
                "Starting global divisibility checks for all fetched recipes..."
            )
            global_divisibility_results = check_recipes_divisibility(
                all_recipes_data
            )
            print("Global divisibility checks complete.")
        except Exception as e:  # pylint: disable=broad-except # General server errors during divisibility checks
            print(
                f"--- Backend ERROR: Failed during global divisibility checks: {e} ---"
            )
            print(traceback.format_exc())
            global_divisibility_results = {'error': str(e), 'status': 'failed'}

        return Response({
            'message': 'Recipe processing complete.',
            'original_recipe_id_processed_for_variations': original_recipe_id,
            'variation_generation_note': variation_generation_note,
            'generated_specific_variations_count': len(specific_variation_ids),
            'generated_specific_variations_ids': specific_variation_ids,
            'global_rounding_results': global_rounding_results,
            'global_divisibility_results': global_divisibility_results,
            'total_recipes_processed_globally': len(all_recipes_objects)
        })

    except Exception as e:  # pylint: disable=broad-except # Final catch-all
        print(
            f"--- Backend ERROR: Unexpected error in process_recipe_fully: {e} ---"
        )
        print(traceback.format_exc())
        return Response(
            {'error': f"An unexpected error occurred: {e}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
