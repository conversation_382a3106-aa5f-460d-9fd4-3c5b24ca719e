import json
import re
from poireaux_app.models import Recipe, RecipeIngredient
from django.db.models import Prefetch

def get_all_recipes():
    """
    Get all recipes with their ingredients prefetched
    """
    return Recipe.objects.prefetch_related(
        'recipe_ingredients__ingredient'
    ).all()

def get_recipe_by_id(recipe_id):
    """
    Get a single recipe by ID
    """
    return Recipe.objects.prefetch_related(
        'recipe_ingredients__ingredient'
    ).get(id=recipe_id)

def create_recipe(recipe_data):
    """
    Create a new recipe. Ingredients are handled by the serializer's create method.
    """
    # The serializer should have already handled the 'months' field deserialization
    # and the creation of nested RecipeIngredient instances.
    
    # Create the recipe
    recipe = Recipe.objects.create(**recipe_data)
    
    return recipe

def update_recipe(recipe_id, update_data):
    """
    Update a recipe by ID with provided data
    """
    recipe = Recipe.objects.get(id=recipe_id)
    
    image_updated = 'image' in update_data
    
    # Update fields
    for key, value in update_data.items():
        setattr(recipe, key, value)
    
    recipe.save()

    if image_updated and recipe.image:
        # If an image was updated, apply it to all serving variations
        # Extract the base name by removing the serving size pattern e.g. (1), (original)
        base_name_match = re.match(r"^(.*?)\s*(?:\(\d+\)|\(original\))?$", recipe.name)
        if base_name_match:
            base_name = base_name_match.group(1).strip()
            
            # Find all recipes (original and serving variations) with the same base name, excluding the current one
            other_recipes = Recipe.objects.filter(
                name__startswith=base_name
            ).exclude(id=recipe.id)

            for other_recipe in other_recipes:
                # Check if the other_recipe name is a variation or the original
                # This ensures we only update related recipes (e.g. "Soup (1)", "Soup (2)", "Soup (original)")
                # and not unrelated recipes that might coincidentally start with the same base_name.
                is_variation = re.search(r'\(\d+\)$', other_recipe.name)
                is_original_marker = '(original)' in other_recipe.name
                current_is_variation = re.search(r'\(\d+\)$', recipe.name)
                current_is_original_marker = '(original)' in recipe.name

                # Determine if the names (without serving/original markers) are identical
                other_base_name_match = re.match(r"^(.*?)\s*(?:\(\d+\)|\(original\))?$", other_recipe.name)
                other_base_name = other_base_name_match.group(1).strip() if other_base_name_match else None

                if other_base_name == base_name: # Ensure they share the exact same base name
                    if (is_variation or is_original_marker) or \
                       (current_is_variation or current_is_original_marker) or \
                       (other_recipe.name == base_name and recipe.name == base_name): # Case where neither has a marker but names match
                        other_recipe.image = recipe.image # Assign the FieldFile object directly
                        other_recipe.save()
    return recipe

def update_recipe_ingredient(ingredient_id, update_data):
    """
    Update a recipe ingredient by ID with provided data
    """
    recipe_ingredient = RecipeIngredient.objects.get(id=ingredient_id)
    
    # Update fields
    for key, value in update_data.items():
        setattr(recipe_ingredient, key, value)
    
    recipe_ingredient.save()
    return recipe_ingredient

def delete_recipe(recipe_id):
    """
    Delete a recipe by ID, and optionally its serving variations
    """
    # Find the recipe to be deleted
    recipe = Recipe.objects.get(id=recipe_id)
    recipe_name = recipe.name
    
    # Check if this is a serving variation (contains a number in parentheses)
    is_serving_variation = bool(re.search(r'\(\d+\)$', recipe_name))
    
    # If this is not a serving variation, just delete this specific recipe
    if not is_serving_variation:
        recipe.delete()
        return {
            'message': f'Successfully deleted recipe "{recipe_name}"',
            'deleted_ids': [recipe.id],
            'deleted_names': [recipe_name],
            'total_deleted': 1
        }
    
    # Extract the base name by removing the serving size pattern
    base_name = re.sub(r'\s*\(\d+\)$', '', recipe_name).strip()
    
    # Find all serving variations of this recipe
    serving_variations = Recipe.objects.filter(
        name__iregex=r'^' + re.escape(base_name) + r'\s*\(\d+\)$'
    )
    
    # Record IDs and names for response
    recipe_ids = list(serving_variations.values_list('id', flat=True))
    recipe_names = list(serving_variations.values_list('name', flat=True))
    
    # Delete all serving variations
    deleted_count, _ = serving_variations.delete()
    
    # Return success response with deletion details
    return {
        'message': f'Successfully deleted {deleted_count} serving variations of "{base_name}"',
        'deleted_ids': recipe_ids,
        'deleted_names': recipe_names,
        'total_deleted': deleted_count,
        'base_name': base_name
    }

def generate_serving_variations(original_recipe_id=None):
    """
    Generate serving variations (1-4 servings) for recipes marked as (original)
    """
    created_recipes = []
    
    if not original_recipe_id:
        # Process all recipes with (original) in the name
        original_recipes = Recipe.objects.filter(name__contains='(original)')
        
        if not original_recipes.exists():
            raise ValueError('No original recipes found')
        
        # Process each original recipe
        for original_recipe in original_recipes:
            result = _generate_variations_for_recipe(original_recipe)
            if result:
                created_recipes.extend(result)
    else:
        # Process just the specified recipe
        original_recipe = Recipe.objects.get(id=original_recipe_id)
        result = _generate_variations_for_recipe(original_recipe)
        if result:
            created_recipes.extend(result)
    
    return created_recipes

def _generate_variations_for_recipe(original_recipe):
    """Helper function to generate serving variations for a single recipe"""
    # Validate that the recipe is marked as original
    if '(original)' not in original_recipe.name:
        print(f"Skipping recipe {original_recipe.name} - not marked as original")
        return None
    
    # Get the base name by removing '(original)' from the name
    base_name = original_recipe.name.replace('(original)', '').strip()
    
    # Check if any serving variations already exist for this recipe
    existing_variations = Recipe.objects.filter(name__startswith=base_name, name__regex=r'\(\d+\)$').exists()
    if existing_variations:
        print(f"Serving variations for '{base_name}' already exist")
        return None
    
    # Get the original recipe's serving size
    original_servings = original_recipe.servings or 1
    created_recipes = []
    
    # Get all recipe ingredients for the original recipe
    recipe_ingredients = RecipeIngredient.objects.filter(recipe=original_recipe)
    
    # Generate recipes for servings 1-4
    for serving_size in range(1, 5):
        print(f"Generating recipe for {serving_size} serving(s)...")
        
        # Calculate scale factor
        scale_factor = serving_size / original_servings
        
        # Create new recipe (copy image from original if it exists)
        new_recipe = Recipe.objects.create(
            name=f"{base_name} ({serving_size})",
            instructions=original_recipe.instructions,
            image=original_recipe.image,  # Copy the image from original recipe
            type=original_recipe.type,
            diet=original_recipe.diet,
            servings=serving_size,
            months=original_recipe.months,
            divisible=original_recipe.divisible
        )
        
        # Create scaled ingredients for the new recipe
        for ingredient in recipe_ingredients:
            scaled_quantity = float(ingredient.quantity) * scale_factor
            RecipeIngredient.objects.create(
                recipe=new_recipe,
                ingredient=ingredient.ingredient,
                quantity=scaled_quantity,
                unit=ingredient.unit
            )
        
        created_recipes.append({
            'id': new_recipe.id,
            'name': new_recipe.name,
            'servings': serving_size
        })
        
        print(f"Successfully created recipe for {serving_size} serving(s)")
    
    return created_recipes 
