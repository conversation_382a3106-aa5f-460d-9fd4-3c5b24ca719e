import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ControlPanel from '../ControlPanel';

// Mock the Tooltip component
jest.mock('../../../../components/Tooltip', () => {
  return ({ children, content }) => (
    <div data-testid="tooltip" data-content={content}>
      {children}
    </div>
  );
});

describe('ControlPanel component', () => {
  // Default props
  const defaultProps = {
    servings: 2,
    month: null,
    generating: false,
    recalculating: false,
    calculationStatus: {
      total_combinations: 10,
      valid_combinations: 8,
      invalid_combinations: 2,
      top_performers: 3,
      invalid_top_performers: 1
    },
    lastCalculated: '2025-04-17T10:00:00Z',
    filterConfig: {
      minWasteScore: 0,
      maxWasteScore: 100,
      showInvalidCache: true,
      searchTerm: ''
    },
    sortConfig: {
      key: 'waste_score',
      direction: 'asc'
    },
    onServingsChange: jest.fn(),
    onMonthChange: jest.fn(),
    onGenerate: jest.fn(),
    onTriggerRecalculation: jest.fn(),
    onUpdateFilter: jest.fn(),
    onUpdateSort: jest.fn()
  };

  test('renders correctly with default props', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Check that the component renders
    expect(screen.getByText('All combinations consist of exactly 3 recipes.')).toBeInTheDocument();
    
    // Check that the servings dropdown is rendered with the correct value
    expect(screen.getByLabelText('Servings:')).toHaveValue('2');
    
    // Check that the month dropdown is rendered with the correct value
    expect(screen.getByLabelText('Month:')).toHaveValue('');
    
    // Check that the generate button is rendered
    expect(screen.getByText('Generate Combinations')).toBeInTheDocument();
    
    // Check that the recalculate button is rendered
    expect(screen.getByText('Recalculate')).toBeInTheDocument();
    
    // Check that the last calculated timestamp is rendered
    expect(screen.getByText(/Last calculated:/)).toBeInTheDocument();
    
    // Check that the filters toggle button is rendered
    expect(screen.getByText('Show Filters & Sorting')).toBeInTheDocument();
  });

  test('shows loading state when generating', () => {
    render(<ControlPanel {...defaultProps} generating={true} />);
    
    // Check that the generate button shows loading state
    expect(screen.getByText('Generating...')).toBeInTheDocument();
    expect(screen.getByText('Generating...')).toBeDisabled();
  });

  test('shows loading state when recalculating', () => {
    render(<ControlPanel {...defaultProps} recalculating={true} />);
    
    // Check that the recalculate button shows loading state
    expect(screen.getByText('Recalculating...')).toBeInTheDocument();
    expect(screen.getByText('Recalculating...')).toBeDisabled();
    
    // Check that the progress bar is rendered
    expect(screen.getByText(/Recalculating.../)).toBeInTheDocument();
    expect(screen.getByText(/80%/)).toBeInTheDocument(); // 8/10 = 80%
  });

  test('calls onServingsChange when servings dropdown changes', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Change the servings dropdown
    fireEvent.change(screen.getByLabelText('Servings:'), { target: { value: '4' } });
    
    // Check that onServingsChange was called with the correct value
    expect(defaultProps.onServingsChange).toHaveBeenCalledWith(4);
  });

  test('calls onMonthChange when month dropdown changes', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Change the month dropdown
    fireEvent.change(screen.getByLabelText('Month:'), { target: { value: '7' } });
    
    // Check that onMonthChange was called with the correct value
    expect(defaultProps.onMonthChange).toHaveBeenCalledWith(7);
  });

  test('calls onGenerate when generate button is clicked', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Click the generate button
    fireEvent.click(screen.getByText('Generate Combinations'));
    
    // Check that onGenerate was called
    expect(defaultProps.onGenerate).toHaveBeenCalled();
  });

  test('calls onTriggerRecalculation when recalculate button is clicked', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Click the recalculate button
    fireEvent.click(screen.getByText('Recalculate'));
    
    // Check that onTriggerRecalculation was called
    expect(defaultProps.onTriggerRecalculation).toHaveBeenCalled();
  });

  test('toggles filters when filter toggle button is clicked', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Initially, filters should be hidden
    expect(screen.queryByText('Filter Options')).not.toBeInTheDocument();
    
    // Click the filter toggle button
    fireEvent.click(screen.getByText('Show Filters & Sorting'));
    
    // Filters should now be visible
    expect(screen.getByText('Filter Options')).toBeInTheDocument();
    expect(screen.getByText('Sort By')).toBeInTheDocument();
    
    // Click the filter toggle button again
    fireEvent.click(screen.getByText('Hide Filters & Sorting'));
    
    // Filters should be hidden again
    expect(screen.queryByText('Filter Options')).not.toBeInTheDocument();
  });

  test('calls onUpdateFilter when filter inputs change', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Show filters
    fireEvent.click(screen.getByText('Show Filters & Sorting'));
    
    // Change min waste score
    fireEvent.change(screen.getAllByRole('spinbutton')[0], { target: { value: '10' } });
    expect(defaultProps.onUpdateFilter).toHaveBeenCalledWith({ minWasteScore: 10 });
    
    // Change max waste score
    fireEvent.change(screen.getAllByRole('spinbutton')[1], { target: { value: '50' } });
    expect(defaultProps.onUpdateFilter).toHaveBeenCalledWith({ maxWasteScore: 50 });
    
    // Toggle show invalid cache
    fireEvent.click(screen.getByLabelText(/Show combinations with outdated calculations/));
    expect(defaultProps.onUpdateFilter).toHaveBeenCalledWith({ showInvalidCache: false });
    
    // Change search term
    fireEvent.change(screen.getByPlaceholderText('Search by recipe name...'), { target: { value: 'chicken' } });
    expect(defaultProps.onUpdateFilter).toHaveBeenCalledWith({ searchTerm: 'chicken' });
  });

  test('calls onUpdateSort when sort buttons are clicked', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Show filters
    fireEvent.click(screen.getByText('Show Filters & Sorting'));
    
    // Click waste score sort button
    fireEvent.click(screen.getByText('Waste Score'));
    expect(defaultProps.onUpdateSort).toHaveBeenCalledWith('waste_score');
    
    // Click last calculated sort button
    fireEvent.click(screen.getByText('Last Calculated'));
    expect(defaultProps.onUpdateSort).toHaveBeenCalledWith('last_calculated');
    
    // Click recipe count sort button
    fireEvent.click(screen.getByText('Recipe Count'));
    expect(defaultProps.onUpdateSort).toHaveBeenCalledWith('recipes.length');
  });

  test('shows active sort direction indicator', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Show filters
    fireEvent.click(screen.getByText('Show Filters & Sorting'));
    
    // Check that the waste score sort button has the active class and up arrow
    const wasteScoreButton = screen.getByText('Waste Score').closest('button');
    expect(wasteScoreButton).toHaveClass('active');
    expect(screen.getByText('↑')).toBeInTheDocument();
    
    // Render with different sort config
    const newProps = {
      ...defaultProps,
      sortConfig: {
        key: 'last_calculated',
        direction: 'desc'
      }
    };
    
    render(<ControlPanel {...newProps} />);
    
    // Show filters
    fireEvent.click(screen.getByText('Show Filters & Sorting'));
    
    // Check that the last calculated sort button has the active class and down arrow
    const lastCalculatedButton = screen.getAllByText('Last Calculated')[1].closest('button');
    expect(lastCalculatedButton).toHaveClass('active');
    expect(screen.getAllByText('↓')[0]).toBeInTheDocument();
  });

  test('formats date correctly', () => {
    render(<ControlPanel {...defaultProps} />);
    
    // Check that the date is formatted correctly
    const date = new Date('2025-04-17T10:00:00Z');
    const formattedDate = date.toLocaleString();
    expect(screen.getByText(`Last calculated: ${formattedDate}`)).toBeInTheDocument();
  });

  test('handles null lastCalculated', () => {
    render(<ControlPanel {...defaultProps} lastCalculated={null} />);
    
    // Check that "Never" is displayed when lastCalculated is null
    expect(screen.queryByText(/Last calculated:/)).not.toBeInTheDocument();
  });

  test('calculates progress percentage correctly', () => {
    render(<ControlPanel {...defaultProps} recalculating={true} />);
    
    // Check that the progress percentage is calculated correctly
    // 8/10 = 80%
    expect(screen.getByText(/Recalculating... 80%/)).toBeInTheDocument();
    
    // Check with different calculation status
    const newProps = {
      ...defaultProps,
      recalculating: true,
      calculationStatus: {
        total_combinations: 20,
        valid_combinations: 5,
        invalid_combinations: 15,
        top_performers: 3,
        invalid_top_performers: 1
      }
    };
    
    render(<ControlPanel {...newProps} />);
    
    // 5/20 = 25%
    expect(screen.getByText(/Recalculating... 25%/)).toBeInTheDocument();
  });

  test('handles null calculationStatus', () => {
    render(<ControlPanel {...defaultProps} calculationStatus={null} recalculating={true} />);
    
    // Check that 0% is displayed when calculationStatus is null
    expect(screen.getByText(/Recalculating... 0%/)).toBeInTheDocument();
  });
});