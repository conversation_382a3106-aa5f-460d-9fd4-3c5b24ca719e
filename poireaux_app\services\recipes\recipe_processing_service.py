from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist, ValidationError

from poireaux_app.models import Recipe, RecipeIngredient
from poireaux_app.utils.recipe import recipe_crud_utils
from poireaux_app.services.recipes.recipe_rounding_service import round_all_recipe_ingredients
from poireaux_app.services.recipes.recipe_divisibility_service import check_recipes_divisibility
from poireaux_app.api.serializers.recipe_serializers import RecipeSerializer # Assuming a serializer exists

# Placeholder for a more specific serializer if needed for service functions
def serialize_recipes_for_services(recipe_ids):
    """
    Fetches and serializes recipes with their ingredients for service consumption.
    """
    recipes = Recipe.objects.filter(id__in=recipe_ids).prefetch_related(
        'recipe_ingredients__ingredient'  # Corrected prefetch
    )
    # Using RecipeSerializer with many=True. Adjust if a different structure is needed.
    # The services expect a list of dicts, where each dict has 'recipe_ingredients'
    # which is a list of dicts, each with 'ingredient_data'.
    # Ensure RecipeSerializer provides this structure or create a custom one.
    
    # A more direct serialization might be needed if RecipeSerializer is too heavy or not structured correctly
    # For now, let's assume RecipeSerializer can be adapted or a similar utility exists.
    # This is a common pattern, but the exact serialization depends on what
    # `round_all_recipe_ingredients` and `check_recipes_divisibility` expect.
    # Based on the problem description, they expect a list of dictionaries.
    
    serialized_data = []
    for recipe in recipes:
        ingredients_data = []
        for ri in recipe.recipe_ingredients.all():
            ingredient_detail = {
                "id": ri.id,
                "quantity": ri.quantity,
                "unit": ri.unit,
                "ingredient_data": {  # This key name is expected by downstream services
                    "id": ri.ingredient.id,
                    "name": ri.ingredient.name,
                    "type": ri.ingredient.type,
                    "divisible_by_measurement": ri.ingredient.divisible_by_measurement, # Corrected attribute
                    "divisible_by_int": ri.ingredient.divisible_by_int,
                    "bought_by": ri.ingredient.bought_by,
                    "bought_by_amount": ri.ingredient.bought_by_amount,
                    # Add other relevant ingredient fields
                }
            }
            ingredients_data.append(ingredient_detail)
        
        serialized_data.append({
            "id": recipe.id,
            "name": recipe.name,
            "servings": recipe.servings,
            "recipe_ingredients": ingredients_data,
            "divisible": recipe.divisible # for re-serialization before divisibility check
            # Add other relevant recipe fields
        })
    return serialized_data


@transaction.atomic
def process_recipe_and_variations(original_recipe_id: int):
    """
    Processes an original recipe and its generated variations by:
    1. Fetching the original recipe.
    2. Generating serving variations (1-4 servings).
    3. Applying ingredient rounding to the original and all variations.
    4. Checking divisibility for the original and all variations.
    """
    try:
        # 1. Fetch Original Recipe
        original_recipe = Recipe.objects.get(id=original_recipe_id)
        if "(original)" not in original_recipe.name.lower():
            # Or handle as an error, depending on desired behavior
            # For now, let's assume it must be an original
            raise ValidationError(f"Recipe ID {original_recipe_id} is not an original recipe.")

        # 2. Generate Variations & Scale Ingredients
        # generate_serving_variations is expected to create and save new Recipe instances
        # and their RecipeIngredient instances with scaled quantities.
        # It should return the new Recipe instances or their IDs.
        # For simplicity, let's assume it returns a list of the created Recipe objects.
        # If it returns IDs, we'll need to fetch them.
        variation_recipes = recipe_crud_utils.generate_serving_variations(original_recipe_id)
        
        all_recipe_ids_to_process = [original_recipe.id] + [var_recipe['id'] for var_recipe in variation_recipes]

        # 3. Prepare Data for Rounding Service
        # This serialization needs to match what `round_all_recipe_ingredients` expects.
        recipes_data_for_rounding = serialize_recipes_for_services(all_recipe_ids_to_process)

        # 4. Apply Rounding
        # `round_all_recipe_ingredients` will be modified to accept `include_originals=True`
        # It returns a dictionary of updates: {recipe_id: {recipe_ingredient_id: new_quantity}}
        rounding_updates = round_all_recipe_ingredients(recipes_data_for_rounding, include_originals=True)

        # Apply rounding updates to the database
        for recipe_id_str, ingredients_to_update in rounding_updates.items():
            # The keys from `round_all_recipe_ingredients` might be strings if they come from JSON-like processing
            recipe_id = int(recipe_id_str) 
            for ingredient_update in ingredients_to_update: # ingredients_to_update is a list of dicts
                ri_id_str = ingredient_update.get('id')
                new_quantity = ingredient_update.get('quantity')

                # Ensure keys exist before trying to convert/use them
                if ri_id_str is None or new_quantity is None:
                    print(f"Warning: Skipping ingredient update due to missing 'id' or 'quantity' for recipe {recipe_id}. Update data: {ingredient_update}")
                    continue

                ri_id = int(str(ri_id_str)) # Ensure ri_id_str is converted to string before int if it could be other types
                try:
                    recipe_ingredient_to_update = RecipeIngredient.objects.get(id=ri_id, recipe_id=recipe_id)
                    recipe_ingredient_to_update.quantity = new_quantity
                    recipe_ingredient_to_update.save(update_fields=['quantity'])
                except ObjectDoesNotExist:
                    # Log or handle missing RecipeIngredient if necessary
                    print(f"Warning: RecipeIngredient with id {ri_id} for recipe {recipe_id} not found during rounding update.")
                    pass # Or raise an error
        
        # Update the in-memory recipes_data_for_rounding with the new quantities
        # before sending to the divisibility check.
        for recipe_data in recipes_data_for_rounding:
            recipe_id_key = recipe_data.get('id')
            if recipe_id_key in rounding_updates:
                updates_for_recipe = rounding_updates[recipe_id_key]
                for ingredient_in_recipe_data in recipe_data.get('recipe_ingredients', []):
                    ri_id_key = ingredient_in_recipe_data.get('id')
                    for update_item in updates_for_recipe:
                        if update_item.get('id') == ri_id_key:
                            ingredient_in_recipe_data['quantity'] = update_item.get('quantity')
                            break # Found the update for this ingredient

        # 5. Check Divisibility using the in-memory updated data
        # No need to re-serialize from DB if we update the existing structure
        divisibility_results = check_recipes_divisibility(recipes_data_for_rounding)

        # Update `divisible` field on Recipe models
        for recipe_id_str, is_divisible in divisibility_results.items():
            recipe_id = int(recipe_id_str)
            try:
                recipe_to_update = Recipe.objects.get(id=recipe_id)
                recipe_to_update.divisible = is_divisible
                recipe_to_update.save(update_fields=['divisible'])
            except ObjectDoesNotExist:
                # Log or handle missing Recipe if necessary
                print(f"Warning: Recipe with id {recipe_id} not found during divisibility update.")
                pass # Or raise an error
        
        processed_variation_ids = [var_recipe['id'] for var_recipe in variation_recipes]

        return {
            "success": True,
            "message": f"Successfully processed original recipe {original_recipe_id} and its variations.",
            "original_recipe_id": original_recipe_id,
            "generated_variation_ids": processed_variation_ids,
            "rounding_applied_to_ids": list(map(int, rounding_updates.keys())), # Ensure keys are int
            "divisibility_results": {int(k): v for k, v in divisibility_results.items()} # Ensure keys are int
        }

    except ObjectDoesNotExist:
        return {"success": False, "error": f"Recipe with ID {original_recipe_id} not found."}
    except ValidationError as ve:
        return {"success": False, "error": str(ve)}
    except Exception as e:
        # Log the exception e for debugging
        print(f"An unexpected error occurred in process_recipe_and_variations: {e}")
        return {"success": False, "error": f"An unexpected error occurred: {str(e)}"}
