#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


from dotenv import load_dotenv


def main():
    """Run administrative tasks."""
    load_dotenv()  # Load environment variables from .env file
    # Determine which settings file to use based on an environment variable
    # Default to development settings if DJANGO_ENV is not set
    DJANGO_ENV = os.environ.get('DJANGO_ENV', 'development')  # pylint: disable=invalid-name

    if DJANGO_ENV == 'production':
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'poireaux.settings.production')
    else: # Default to development
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'poireaux.settings.development')

    try:
        from django.core.management import execute_from_command_line  # pylint: disable=import-outside-toplevel
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
