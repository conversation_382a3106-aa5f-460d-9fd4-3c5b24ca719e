import json
import time
from ...models import Ingredient

def get_all_ingredients(ingredient_type=None, detailed=True):
    """
    Get all ingredients, optionally filtered by type
    """
    start_time = time.time()
    
    # Initialize queryset with all ingredients 
    queryset = Ingredient.objects.all()
    
    # Apply type filter if provided
    if ingredient_type and ingredient_type != 'all':
        queryset = queryset.filter(type=ingredient_type)
    
    # Convert queryset to list for serialization
    ingredients = list(queryset)
    
    # Log performance metrics
    execution_time = time.time() - start_time
    print(f"--- Ingredients fetched in {execution_time:.2f} seconds ---")
    print(f"--- Total ingredients: {len(ingredients)} ---")
    
    return ingredients

def update_ingredient(ingredient_id, update_data):
    """
    Update an ingredient by ID with provided data
    """
    ingredient = Ingredient.objects.get(id=ingredient_id)
    
    # Handle divisible_by_int - ensure it's stored as a JSON string if provided
    if 'divisible_by_int' in update_data and not isinstance(update_data['divisible_by_int'], str):
        try:
            # If it's an array, convert to a JSON string
            if isinstance(update_data['divisible_by_int'], list):
                update_data['divisible_by_int'] = json.dumps(update_data['divisible_by_int'])
            # If it's a comma-separated string, parse and convert to JSON
            elif isinstance(update_data['divisible_by_int'], str) and ',' in update_data['divisible_by_int']:
                values = [int(x.strip()) for x in update_data['divisible_by_int'].split(',')]
                update_data['divisible_by_int'] = json.dumps(values)
        except Exception as e:
            print(f"Error processing divisible_by_int: {e}")
            raise ValueError(f"Invalid divisible_by_int format: {e}")
    
    # Update fields
    for key, value in update_data.items():
        setattr(ingredient, key, value)
    
    ingredient.save()
    return ingredient 