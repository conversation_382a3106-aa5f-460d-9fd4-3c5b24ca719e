import React, { useState } from 'react';
import { API_BASE_URL } from '../../../config/api';
import '../../../styles/MonthSelection.css'; // Assuming this contains relevant styles
import '../../ingredient/styles/index.css'; // Styles for the ingredient page

const MONTHS = [
  'january', 'february', 'march', 'april', 'may', 'june', 
  'july', 'august', 'september', 'october', 'november', 'december'
];

const MEASUREMENT_UNITS = ['unit', 'g', 'ml', 'tbsp', 'tsp', 'cup', 'piece', 'bunch', 'loaf', 'jar', 'bottle'];
const INGREDIENT_TYPES = ['vegetable', 'herb', 'meat', 'dairy', 'oil', 'spice', 'grain', 'preserve'];

const IngredientAdder = () => {
  const [name, setName] = useState('');
  const [seasons, setSeasons] = useState(
    MONTHS.reduce((acc, month) => ({ ...acc, [month]: false }), {})
  );
  const [divisibleByInteger, setDivisibleByInteger] = useState('');
  const [divisibleByMeasurement, setDivisibleByMeasurement] = useState(MEASUREMENT_UNITS[0]);
  const [type, setType] = useState(INGREDIENT_TYPES[0]);
  const [needsCalculating, setNeedsCalculating] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState('');

  const handleSeasonChange = (month) => {
    setSeasons(prevSeasons => ({
      ...prevSeasons,
      [month]: !prevSeasons[month]
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setFeedbackMessage('');

    if (!name.trim()) {
      setFeedbackMessage('Ingredient name is required.');
      return;
    }

    let parsedDivisibleByInt = null;
    if (divisibleByInteger.trim()) {
      try {
        const numbers = divisibleByInteger.split(',').map(s => parseInt(s.trim(), 10)).filter(n => !isNaN(n));
        if (numbers.length > 0) {
          parsedDivisibleByInt = `[${numbers.join(',')}]`;
        }
      } catch (error) {
        setFeedbackMessage('Invalid format for "Divisible By Integer". Please use comma-separated numbers (e.g., 1,2,4).');
        return;
      }
    }
    
    const ingredientData = {
      name: name.trim(),
      ...seasons,
      divisible_by_int: parsedDivisibleByInt,
      divisible_by_measurement: divisibleByMeasurement,
      type: type,
      needs_calculating: needsCalculating,
    };

    try {
      const response = await fetch(`${API_BASE_URL}/ingredients/`, { // Assuming /ingredients/ is the POST endpoint
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ingredientData),
      });

      if (response.ok) {
        setFeedbackMessage('Ingredient added successfully!');
        // Clear form
        setName('');
        setSeasons(MONTHS.reduce((acc, month) => ({ ...acc, [month]: false }), {}));
        setDivisibleByInteger('');
        setDivisibleByMeasurement(MEASUREMENT_UNITS[0]);
        setType(INGREDIENT_TYPES[0]);
        setNeedsCalculating(false);
      } else {
        const errorData = await response.json();
        setFeedbackMessage(`Error adding ingredient: ${errorData.detail || response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to submit ingredient:', error);
      setFeedbackMessage('Failed to submit ingredient. See console for details.');
    }
  };

  return (
    <div className="ingredient-adder-container">
      <h3>Add New Ingredient</h3>
      {feedbackMessage && <p className={feedbackMessage.startsWith('Error') || feedbackMessage.startsWith('Failed') ? 'error-message' : 'success-message'}>{feedbackMessage}</p>}
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="name">Name:</label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
          />
        </div>

        <div>
          <label>Seasons:</label>
          <div className="month-checkbox-container">
            {MONTHS.map(month => (
              <div key={month} className="month-checkbox-item">
                <input
                  type="checkbox"
                  id={`season-${month}`}
                  checked={seasons[month]}
                  onChange={() => handleSeasonChange(month)}
                />
                <label htmlFor={`season-${month}`}>{month.charAt(0).toUpperCase() + month.slice(1)}</label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <label htmlFor="divisibleByInteger">Divisible By Integer (e.g., "1,2,4"):</label>
          <input
            type="text"
            id="divisibleByInteger"
            value={divisibleByInteger}
            onChange={(e) => setDivisibleByInteger(e.target.value)}
          />
        </div>

        <div>
          <label htmlFor="divisibleByMeasurement">Divisible By Measurement:</label>
          <select
            id="divisibleByMeasurement"
            value={divisibleByMeasurement}
            onChange={(e) => setDivisibleByMeasurement(e.target.value)}
          >
            {MEASUREMENT_UNITS.map(unit => (
              <option key={unit} value={unit}>{unit}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="type">Type:</label>
          <select
            id="type"
            value={type}
            onChange={(e) => setType(e.target.value)}
          >
            {INGREDIENT_TYPES.map(ingType => (
              <option key={ingType} value={ingType}>{ingType.charAt(0).toUpperCase() + ingType.slice(1)}</option>
            ))}
          </select>
        </div>

        <div>
          <input
            type="checkbox"
            id="needsCalculating"
            checked={needsCalculating}
            onChange={(e) => setNeedsCalculating(e.target.checked)}
          />
          <label htmlFor="needsCalculating">Needs Calculating</label>
        </div>

        <button type="submit">Add Ingredient</button>
      </form>
      <style jsx>{`
        .ingredient-adder-container {
          padding: 20px;
          border: 1px solid #ccc;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .ingredient-adder-container h3 {
          margin-top: 0;
        }
        .ingredient-adder-container div {
          margin-bottom: 10px;
        }
        .ingredient-adder-container label {
          display: block;
          margin-bottom: 5px;
          font-weight: bold;
        }
        .ingredient-adder-container input[type="text"],
        .ingredient-adder-container select {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          box-sizing: border-box;
        }
        .ingredient-adder-container input[type="checkbox"] {
          margin-right: 5px;
        }
        .ingredient-adder-container .month-checkbox-container {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
        }
        .ingredient-adder-container .month-checkbox-item {
          display: flex;
          align-items: center;
        }
        .ingredient-adder-container .month-checkbox-item label {
          font-weight: normal;
          margin-left: 5px;
          margin-bottom: 0;
        }
        .ingredient-adder-container button {
          padding: 10px 15px;
          background-color: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
        .ingredient-adder-container button:hover {
          background-color: #0056b3;
        }
        .success-message {
          color: green;
          background-color: #e6ffed;
          border: 1px solid #b7ebc9;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
        .error-message {
          color: red;
          background-color: #ffeeee;
          border: 1px solid #ffc0c0;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 15px;
        }
      `}</style>
    </div>
  );
};

export default IngredientAdder;
