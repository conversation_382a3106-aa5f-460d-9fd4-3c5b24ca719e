import json
from typing import Set
from ...models import Recipe

def get_recipe_months(recipe: Recipe) -> Set[int]:
    """
    Get the months when a recipe is in season.
    
    Args:
        recipe: Recipe object
        
    Returns:
        Set of months (1-12) when the recipe is in season
    """
    try:
        if recipe.months:
            # Handle case where months are stored as text values 
            # Convert month names to numbers
            month_mapping = {
                'january': 1, 'jan': 1,
                'february': 2, 'feb': 2,
                'march': 3, 'mar': 3,
                'april': 4, 'apr': 4,
                'may': 5,
                'june': 6, 'jun': 6,
                'july': 7, 'jul': 7,
                'august': 8, 'aug': 8,
                'september': 9, 'sep': 9, 'sept': 9,
                'october': 10, 'oct': 10,
                'november': 11, 'nov': 11,
                'december': 12, 'dec': 12
            }
            
            try:
                # Try parsing as a JSON array of month names or numbers
                months_data = json.loads(recipe.months)
                
                # Add this new conditional block to handle month objects with boolean values
                if isinstance(months_data, dict):
                    # Handle case where months are stored as a dictionary with boolean values
                    result_months = set()
                    for month_name, is_in_season in months_data.items():
                        month_key = month_name.lower()
                        if month_key in month_mapping and is_in_season:
                            result_months.add(month_mapping[month_key])
                    return result_months
                
                # Rest of the existing code for arrays
                result_months = set()
                for month in months_data:
                    if isinstance(month, int) and 1 <= month <= 12:
                        result_months.add(month)
                    elif isinstance(month, str):
                        month_key = month.lower()
                        if month_key in month_mapping:
                            result_months.add(month_mapping[month_key])
                
                return result_months
            except (json.JSONDecodeError, ValueError):
                # Handle case where it's a comma or other separator string
                month_list = [m.strip().lower() for m in recipe.months.replace('[', '').replace(']', '').replace('"', '').replace("'", '').split(',')]
                result_months = set()
                for month in month_list:
                    if month in month_mapping:
                        result_months.add(month_mapping[month])
                return result_months or set()  # Default to all months if empty
        else:
            # If recipe doesn't have months data, calculate from ingredients
            months = set(range(1, 13))  # Start with all months
            
            has_processed_seasonal_ingredients = False
            
            for ingredient_link in recipe.recipe_ingredients.all():
                ingredient = ingredient_link.ingredient
                
                # Skip ingredients that don't have seasonality
                if not ingredient.has_seasonality:
                    continue
                
                # Calculate months for this ingredient
                ingredient_months = set()
                if ingredient.january:
                    ingredient_months.add(1)
                if ingredient.february:
                    ingredient_months.add(2)
                if ingredient.march:
                    ingredient_months.add(3)
                if ingredient.april:
                    ingredient_months.add(4)
                if ingredient.may:
                    ingredient_months.add(5)
                if ingredient.june:
                    ingredient_months.add(6)
                if ingredient.july:
                    ingredient_months.add(7)
                if ingredient.august:
                    ingredient_months.add(8)
                if ingredient.september:
                    ingredient_months.add(9)
                if ingredient.october:
                    ingredient_months.add(10)
                if ingredient.november:
                    ingredient_months.add(11)
                if ingredient.december:
                    ingredient_months.add(12)
                
                # Intersect with running total (only keep months common to all seasonal ingredients)
                if ingredient_months:
                    months.intersection_update(ingredient_months)
            
            if not has_processed_seasonal_ingredients:
                return set()  # No seasonal ingredients found
            
            return months
    except (json.JSONDecodeError, ValueError, AttributeError):
        # Return empty set on error instead of all months
        return set()