# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV DJANGO_ENV production

# Set work directory
WORKDIR /app

# Install system dependencies (if any are needed - for psycopg2, build essentials might be,
# but python:slim often has them. If build fails here, might need: apt-get update && apt-get install -y build-essential libpq-dev)
# RUN apt-get update && apt-get install -y some-package

# Install dependencies
# Copy requirements.txt first to leverage Docker cache
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . /app/

# Copy startup script
COPY startup.sh /app/startup.sh
RUN chmod +x /app/startup.sh

# Collect static files (if your Django app serves its own static files, not just an API)
# If your frontend handles all static assets and Django is just an API, you might skip this.
# However, Django admin still needs static files.
# Ensure your settings.py has STATIC_ROOT configured, e.g., STATIC_ROOT = BASE_DIR / 'staticfiles'
# RUN python manage.py collectstatic --noinput

# Expose port (Cloud Run expects 8080 by default, Gunicorn will run on this port)
EXPOSE 8080

# Run startup script
CMD ["/app/startup.sh"]
