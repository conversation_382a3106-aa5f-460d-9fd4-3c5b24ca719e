from .views.ingredient_views import get_ingredients as getIngredients
from .views.ingredient_views import update_ingredient as updateIngredient
from .views.ingredient_views import create_ingredient as createIngredient

from .views.recipe_views import get_recipes as getRecipes
from .views.recipe_views import create_recipe as createRecipe
from .views.recipe_views import delete_recipe as deleteRecipe
from .views.recipe_views import update_recipe as updateRecipe
from .views.recipe_views import update_recipe_ingredient as updateRecipeIngredient
from .views.recipe_views import generate_servings as generateServings
from .views.recipe_views import process_recipe_fully as processRecipeFully

# Import combination views
from .views.combination_views import CombinationListView as getCombinations
from .views.combination_views import generate_combinations as generateCombinations

# Import seasonality views
from .views.seasonality_views import calculate_recipe_seasonality_api
from .views.seasonality_views import update_all_recipes_seasonality
from .views.seasonality_views import get_recipe_seasonality
from .views.seasonality_views import get_batch_recipe_seasonality

# Import divisibility views
from .views.divisibility_views import check_all_recipes_divisibility
from .views.divisibility_views import round_ingredient_quantities

# Keep the original function names in the imports to maintain backward compatibility
# with the existing urls.py file 
