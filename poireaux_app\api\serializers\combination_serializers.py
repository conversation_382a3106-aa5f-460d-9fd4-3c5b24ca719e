from rest_framework import serializers
from ...models import Combination, Recipe, SavedCombination
from .recipe_serializers import RecipeSerializer, RecipeLiteSerializer, CombinationRecipeSerializer # Import CombinationRecipeSerializer

class CombinationMinimalSerializer(serializers.ModelSerializer):
    """
    Ultra-lightweight serializer for fast initial loading.
    Only includes essential fields for list display.
    """
    recipe_names = serializers.SerializerMethodField()

    class Meta:
        model = Combination
        fields = ['id', 'name', 'waste_score', 'servings', 'diet', 'recipe_names']

    def get_recipe_names(self, obj):
        """Get recipe names without loading full recipe objects."""
        return [recipe.name for recipe in obj.recipes.all()]

class CombinationListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing combinations - includes all fields needed by frontend components.
    """
    recipes = RecipeSerializer(many=True, read_only=True)

    class Meta:
        model = Combination
        fields = [
            'id', 'name', 'waste_score', 'servings', 'recipes', 'diet',
            'recipe_specific_ingredients', 'detailed_ingredients'
        ]


class CombinationSerializer(serializers.ModelSerializer):
    """
    The full serializer for combination details.
    """
    recipes = RecipeSerializer(many=True, read_only=True)
    
    class Meta:
        model = Combination
        fields = [
            'id', 'name', 'recipes', 'waste_score', 'servings',
            'is_divisible', 'seasonality_months', 'created_at',
            'last_calculated', 'ingredient_waste', 'is_top_performer',
            'cache_valid', 'dependent_ingredients', 'ingredients_length',
            'detailed_ingredients', 'recipe_specific_ingredients', 'diet' # Added new fields
        ]

class SavedCombinationSerializer(serializers.ModelSerializer):
    combination = CombinationSerializer(read_only=True)
    combination_id = serializers.PrimaryKeyRelatedField(
        queryset=Combination.objects.all(), source='combination', write_only=True
    )

    class Meta:
        model = SavedCombination
        fields = ['id', 'combination', 'combination_id', 'created_at']

    def create(self, validated_data):
        combination_instance = validated_data.pop('combination')
        saved_combination = SavedCombination.objects.create(combination=combination_instance, **validated_data)
        return saved_combination
