from django.test import TestCase
from django.utils import timezone
import json

from poireaux_app.models import Recipe, Ingredient, RecipeIngredient, Combination

class SignalsTestCase(TestCase):
    """Test case for the signal handlers."""
    
    def setUp(self):
        """Set up test data."""
        # Create test ingredients
        self.ingredient1 = Ingredient.objects.create(
            name="Test Ingredient 1",
            bought_by="weight",
            bought_by_amount=500,  # 500g packaging
            unit="g"
        )
        self.ingredient2 = Ingredient.objects.create(
            name="Test Ingredient 2",
            bought_by="weight",
            bought_by_amount=250,  # 250g packaging
            unit="g"
        )
        
        # Create test recipes
        self.recipe1 = Recipe.objects.create(
            name="Test Recipe 1",
            servings=2
        )
        self.recipe2 = Recipe.objects.create(
            name="Test Recipe 2",
            servings=4
        )
        
        # Create recipe ingredients
        self.recipe_ingredient1 = RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient1,
            quantity=200,
            unit="g"
        )
        self.recipe_ingredient2 = RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient2,
            quantity=100,
            unit="g"
        )
        
        # Create test combinations
        self.combination1 = Combination.objects.create(
            name="Test Combination 1",
            servings=2,
            waste_score=25.5,
            seasonality_months=json.dumps([1, 2, 3]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=True,
            dependent_ingredients=json.dumps([self.ingredient1.id])
        )
        self.combination1.recipes.add(self.recipe1)
        
        self.combination2 = Combination.objects.create(
            name="Test Combination 2",
            servings=4,
            waste_score=30.0,
            seasonality_months=json.dumps([4, 5, 6]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=False,
            dependent_ingredients=json.dumps([self.ingredient2.id])
        )
        self.combination2.recipes.add(self.recipe2)
        
        self.combination3 = Combination.objects.create(
            name="Test Combination 3",
            servings=2,
            waste_score=15.0,
            seasonality_months=json.dumps([7, 8, 9]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=True,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient2.id])
        )
        self.combination3.recipes.add(self.recipe1, self.recipe2)

    def test_recipe_update_signal(self):
        """Test that updating a recipe marks affected combinations as invalid."""
        # Verify all combinations are valid initially
        self.assertTrue(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)
        
        # Update recipe1
        self.recipe1.name = "Updated Recipe 1"
        self.recipe1.save()
        
        # Refresh combinations from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        # Combinations containing recipe1 should be marked as invalid
        self.assertFalse(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)  # Not affected
        self.assertFalse(self.combination3.cache_valid)

    def test_recipe_delete_signal(self):
        """Test that deleting a recipe marks affected combinations as invalid."""
        # Verify all combinations are valid initially
        self.assertTrue(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)
        
        # Delete recipe2
        self.recipe2.delete()
        
        # Refresh combinations from database
        self.combination1.refresh_from_db()
        
        # Combinations containing recipe2 should be deleted due to CASCADE
        self.assertTrue(self.combination1.cache_valid)  # Not affected
        self.assertEqual(Combination.objects.count(), 1)  # Only combination1 remains
        
        # Verify combination2 and combination3 were deleted
        with self.assertRaises(Combination.DoesNotExist):
            Combination.objects.get(id=self.combination2.id)
        
        with self.assertRaises(Combination.DoesNotExist):
            Combination.objects.get(id=self.combination3.id)

    def test_ingredient_update_signal(self):
        """Test that updating an ingredient marks affected combinations as invalid."""
        # Verify all combinations are valid initially
        self.assertTrue(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)
        
        # Update ingredient1's packaging size
        self.ingredient1.bought_by_amount = 1000
        self.ingredient1.save(update_fields=['bought_by_amount'])
        
        # Refresh combinations from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        # Combinations dependent on ingredient1 should be marked as invalid
        self.assertFalse(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)  # Not affected
        self.assertFalse(self.combination3.cache_valid)

    def test_ingredient_delete_signal(self):
        """Test that deleting an ingredient marks affected combinations as invalid."""
        # Verify all combinations are valid initially
        self.assertTrue(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)
        
        # Delete ingredient1
        self.ingredient1.delete()
        
        # Refresh combinations from database
        # Note: In a real application, the combinations might be deleted due to CASCADE,
        # but for this test we're focusing on the signal behavior
        
        # Combinations dependent on ingredient1 should be marked as invalid
        # But they might be deleted due to CASCADE, so we need to check if they exist first
        try:
            self.combination1.refresh_from_db()
            self.assertFalse(self.combination1.cache_valid)
        except Combination.DoesNotExist:
            pass  # Combination was deleted, which is also acceptable
        
        try:
            self.combination2.refresh_from_db()
            self.assertTrue(self.combination2.cache_valid)  # Not affected
        except Combination.DoesNotExist:
            self.fail("Combination2 should not be deleted")
        
        try:
            self.combination3.refresh_from_db()
            self.assertFalse(self.combination3.cache_valid)
        except Combination.DoesNotExist:
            pass  # Combination was deleted, which is also acceptable

    def test_recipe_ingredient_update_signal(self):
        """Test that updating a recipe ingredient marks affected combinations as invalid."""
        # Verify all combinations are valid initially
        self.assertTrue(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)
        
        # Update recipe_ingredient1
        self.recipe_ingredient1.quantity = 300
        self.recipe_ingredient1.save()
        
        # Refresh combinations from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        # Combinations containing recipe1 should be marked as invalid
        self.assertFalse(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)  # Not affected
        self.assertFalse(self.combination3.cache_valid)

    def test_recipe_ingredient_delete_signal(self):
        """Test that deleting a recipe ingredient marks affected combinations as invalid."""
        # Verify all combinations are valid initially
        self.assertTrue(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)
        self.assertTrue(self.combination3.cache_valid)
        
        # Delete recipe_ingredient1
        self.recipe_ingredient1.delete()
        
        # Refresh combinations from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        # Combinations containing recipe1 should be marked as invalid
        self.assertFalse(self.combination1.cache_valid)
        self.assertTrue(self.combination2.cache_valid)  # Not affected
        self.assertFalse(self.combination3.cache_valid)

    def test_combination_save_signal(self):
        """Test that saving a combination with a new waste score updates top performers."""
        # Reset top performers
        Combination.objects.all().update(is_top_performer=False)
        
        # Update combination1 with a new waste score
        self.combination1.waste_score = 5.0
        self.combination1.save(update_fields=['waste_score'])
        
        # Refresh combinations from database
        self.combination1.refresh_from_db()
        self.combination2.refresh_from_db()
        self.combination3.refresh_from_db()
        
        # Check that top performers were updated
        # For servings=2, combination1 should be the top performer (lowest waste score)
        # For servings=4, combination2 should be the top performer (only one)
        self.assertTrue(self.combination1.is_top_performer)
        self.assertTrue(self.combination2.is_top_performer)
        self.assertFalse(self.combination3.is_top_performer)