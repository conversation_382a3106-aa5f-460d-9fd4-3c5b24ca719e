.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

/* .App-header {
  background: linear-gradient(to bottom,
    #87CEEB 0%,
    #87CEEB 60%,
    #4a7c59 60%,
    #4a7c59 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: black;
} */

.App-header::before {
  content: "";
  position: absolute;
  top: 50px;
  right: 50px;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, #ffff88 20%, #ffff00 40%, transparent 70%);
  border-radius: 50%;
  box-shadow: 0 0 30px #ffff88;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.two-column-container {
  display: flex;
  gap: 30px;
  width: 100%;
  max-width: 1450px;
  margin: 0 auto;
  padding: 0 15px;
}

.column {
  flex: 1;
  min-width: 0; /* Prevents flex children from overflowing */
}