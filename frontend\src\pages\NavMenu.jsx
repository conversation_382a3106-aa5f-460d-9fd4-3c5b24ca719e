import React from 'react';
import { NavLink } from 'react-router-dom';
import '../styles/NavMenu.css';

function NavMenu({ isProduction }) { // Receive isProduction prop
  return (
    <nav className="nav-menu">
      <div className="nav-container">
        <div className="logo">
          <span className="logo-text">Poireaux</span>
        </div>
        <div className="nav-links">
          {!isProduction && ( // Only show these links if not in production
            <>
              <NavLink
                to="/"
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                end
              >
                Ingredients
              </NavLink>
              <NavLink
                to="/recipes"
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              >
                Recipes
              </NavLink>
            </>
          )}
          <NavLink
            to="/combinations"
            className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
          >
            Combinations
          </NavLink>
          <NavLink
            to="/saved-combinations"
            className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
          >
            Saved Combinations
          </NavLink>
        </div>
      </div>
    </nav>
  );
}

export default NavMenu;
