// Utility function for React.memo comparison in RecipeCard

export const recipeCardPropsAreEqual = (prevProps, nextProps) => {
  // Basic check for recipe ID
  if (prevProps.recipe.id !== nextProps.recipe.id) {
    return false;
  }

  // Compare recipe.image as it's a direct prop that can change
  if (prevProps.recipe.image !== nextProps.recipe.image) {
    return false;
  }
  
  // Check other relevant, non-function props of recipe
  const relevantKeys = ['name', 'type', 'servings', 'divisible', 'diet', 'instructions'];
  for (const key of relevantKeys) {
    if (prevProps.recipe[key] !== nextProps.recipe[key]) {
      return false;
    }
  }

  // Compare ingredients
  const prevIngredients = prevProps.recipe.ingredients || [];
  const nextIngredients = nextProps.recipe.ingredients || [];
  if (prevIngredients.length !== nextIngredients.length) {
    return false;
  }
  // A simple way to check if ingredients content changed:
  if (JSON.stringify(prevIngredients) !== JSON.stringify(nextIngredients)) {
      return false;
  }

  // Compare seasonality data (result of formatSeasonality)
  // It's crucial that formatSeasonality is pure or memoized if it's expensive
  const prevSeasonality = prevProps.formatSeasonality(prevProps.recipe);
  const nextSeasonality = nextProps.formatSeasonality(nextProps.recipe);
  if (JSON.stringify(prevSeasonality) !== JSON.stringify(nextSeasonality)) {
    return false;
  }
  
  // Compare handler functions and formatSeasonality itself
  if (
    prevProps.handleEditClick !== nextProps.handleEditClick ||
    prevProps.handleDeleteRecipe !== nextProps.handleDeleteRecipe ||
    prevProps.onRecipeUpdate !== nextProps.onRecipeUpdate ||
    prevProps.formatSeasonality !== nextProps.formatSeasonality 
  ) {
    return false;
  }

  return true;
};