.nav-menu {
    background-color: #2a9d8f;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .logo-text {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .nav-links {
    display: flex;
    gap: 1.5rem;
  }
  
  .nav-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    padding: 0.5rem 0;
    font-weight: 500;
    position: relative;
  }
  
  .nav-link:hover {
    color: white;
  }
  
  .nav-link.active {
    color: white;
  }
  
  .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: white;
    border-radius: 1.5px;
  }

  /* Mobile adjustments */
  @media (max-width: 768px) {
    .nav-menu {
      padding: 0.3rem 0.8rem; /* Reduced padding */
    }

    .logo-text {
      font-size: 1.2rem; /* Smaller logo */
    }

    .nav-links {
      gap: 1rem; /* Reduced gap */
    }

    .nav-link {
      padding: 0.3rem 0; /* Reduced padding */
      font-size: 0.9rem; /* Smaller text */
    }
  }
