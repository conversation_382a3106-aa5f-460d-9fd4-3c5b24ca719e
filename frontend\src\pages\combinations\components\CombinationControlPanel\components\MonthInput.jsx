import React from 'react';
import PropTypes from 'prop-types';
import '../styles/MonthInput.css';

const MonthInput = ({ month, onMonthChange }) => {
  const currentMonthIndex = new Date().getMonth(); // 0-11
  const currentMonthNumber = currentMonthIndex + 1; // 1-12
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];
  const currentMonthName = monthNames[currentMonthIndex];

  return (
    <div className="control-group">
      <label htmlFor="month">Month:</label>
      <select
        id="month"
        value={month === null ? '' : month} // Handle null explicitly for "Any month"
        onChange={(e) => onMonthChange(e.target.value === '' ? null : Number(e.target.value))}
      >
        <option value="">Any month</option>
        <option value={currentMonthNumber}>{currentMonthName}</option>
      </select>
    </div>
  );
};

MonthInput.propTypes = {
  month: PropTypes.number,
  onMonthChange: PropTypes.func.isRequired,
};

export default MonthInput;