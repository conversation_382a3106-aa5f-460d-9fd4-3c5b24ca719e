/* Skeleton Recipe Card styles */
.skeleton {
  position: relative;
  overflow: hidden;
}

.skeleton::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  animation: shimmer 1.5s infinite;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
}

.skeleton-header {
  background-color: #f5f7fa;
  padding: 15px;
  border-bottom: 1px solid #eaeaea;
}

.skeleton-title {
  height: 24px;
  width: 70%;
  background-color: #e0e0e0;
  margin-bottom: 12px;
  border-radius: 4px;
}

.skeleton-meta {
  display: flex;
  gap: 10px;
}

.skeleton-badge {
  height: 18px;
  width: 60px;
  background-color: #e0e0e0;
  border-radius: 4px;
}

.skeleton-seasonality {
  height: 60px;
  background-color: #e0e0e0;
  margin-bottom: 15px;
  border-radius: 4px;
}

.skeleton-content {
  height: 120px;
  background-color: #e0e0e0;
  border-radius: 4px;
  position: relative;
}

/* Create lines in the skeleton content for a more realistic look */
.skeleton-content::before {
  content: "";
  position: absolute;
  top: 10px;
  left: 0;
  width: 100%;
  height: calc(100% - 20px);
  background: repeating-linear-gradient(
    #e0e0e0,
    #e0e0e0 12px,
    #eaeaea 12px,
    #eaeaea 20px
  );
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 0.8; }
  100% { opacity: 0.6; }
} 