import React from 'react';
import RecipeCard from './RecipeCard';

const RecipeList = ({
  recipes,
  activeServingSize,
  formatSeasonality,
  handleEditClick,
  handleDeleteRecipe,
  filterRecipesBySize,
  onRecipeUpdate // Added onRecipeUpdate prop
}) => {
  // Filter recipes by pattern in name
  const filteredRecipes = filterRecipesBySize(recipes);

  if (filteredRecipes.length === 0) {
    return <p>No recipes found for the selected filter.</p>;
  }

  return (
    <div className="recipe-cards">
      {filteredRecipes.map((recipe) => (
        <RecipeCard 
          key={recipe.id}
          recipe={recipe}
          formatSeasonality={formatSeasonality}
          handleEditClick={handleEditClick}
          handleDeleteRecipe={handleDeleteRecipe}
          onRecipeUpdate={onRecipeUpdate} // Pass onRecipeUpdate to RecipeCard
        />
      ))}
    </div>
  );
};

export default RecipeList; 