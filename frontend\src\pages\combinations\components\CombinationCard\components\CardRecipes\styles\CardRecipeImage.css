/* Styles for CardRecipeImage.jsx */

.card-recipe-image-no-recipes-placeholder-container {
  text-align: center;
  margin-bottom: 20px;
}

.card-recipe-image-no-recipes-placeholder-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f0f0f0;
  text-align: center;
  color: #666;
  font-size: 0.9em;
}

.recipe-image-display { /* Existing class, adding styles */
  text-align: center;
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Allow this to take up space in card-body */
  width: 100%; /* Take full width of parent (card-body) */
  margin: 0; /* Remove all margins */
  min-height: 0; /* Prevent content from expanding parent beyond flex allocation */
  height: 100%; /* Take full height */
}

.recipe-image-with-controls { /* Existing class, adding styles */
  display: flex;
  align-items: center;
  justify-content: space-between; /* Push buttons to the edges */
  flex-grow: 1; /* Allow this container to grow and fill recipe-image-display */
  width: 100%;
  height: 100%; /* Take full height */
  margin: 0; /* Remove margins */
  /* Conditional margin-bottom will be handled by a modifier class or kept inline if too complex */
}

.recipe-image-with-controls.has-margin-bottom {
  margin-bottom: 10px;
}

.card-recipe-image-nav-button {
  background: transparent;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0px 1px;
  line-height: 1;
  color: #333;
  min-width: 30px;
  min-height: 30px;
  flex-shrink: 0;
}

.card-recipe-image-img {
  width: 100%; /* Fill full width */
  height: 100%; /* Fill full height */
  display: block;
  border: none; /* Remove border for full coverage */
  border-radius: 4px;
  object-fit: cover; /* Cover the entire container */
  flex-grow: 1; /* Allow image to take up remaining space between buttons */
  margin: 0; /* Remove all margins */
  min-width: 0; /* Prevent content from expanding parent beyond flex allocation in a row */
}



.card-recipe-image-img-placeholder {
  width: 100%; /* Should behave like the image */
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none; /* Remove border for full coverage */
  border-radius: 4px;
  background-color: #f0f0f0;
  text-align: center;
  color: #666;
  font-size: 0.9em;
  flex-grow: 1; /* Mirror image behavior */
  margin: 0; /* Remove margins */
  min-width: 0; /* Mirror image behavior */
}

.card-recipe-image-count {
  font-size: 0.85em;
  color: #666;
  margin-top: 5px;
  flex-shrink: 0;
}
