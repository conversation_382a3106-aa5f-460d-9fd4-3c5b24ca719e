# Generated by Django 5.1.7 on 2025-05-29 07:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('poireaux_app', '0023_albertheijnproduct_bought_by_amounts'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='albertheijnproduct',
            name='bought_by_amounts',
        ),
        migrations.AddField(
            model_name='albertheijnproduct',
            name='bonus_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Price during bonus period', max_digits=8, null=True),
        ),
        migrations.AddField(
            model_name='albertheijnproduct',
            name='bought_by_amount',
            field=models.IntegerField(default=1, help_text='Amount in which the product is sold (e.g., 1 for unit, 500 for grams)'),
        ),
    ]
