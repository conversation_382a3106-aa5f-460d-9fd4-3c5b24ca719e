import json
from typing import List, Dict, Any, Set, Union
from ...models import Recipe, Ingredient

def extract_ingredient_data(recipe_ingredient) -> Dict[str, Any]:
    """
    Extract ingredient data from a RecipeIngredient object, converting to the format
    expected by the calculate_recipe_seasonality function.
    
    Args:
        recipe_ingredient: A RecipeIngredient object with related ingredient data
        
    Returns:
        Dictionary with ingredient data including seasonality information
    """
    ingredient = recipe_ingredient.ingredient
    return {
        'id': ingredient.id,
        'name': ingredient.name,
        'has_seasonality': ingredient.has_seasonality,
        'january': ingredient.january,
        'february': ingredient.february,
        'march': ingredient.march,
        'april': ingredient.april,
        'may': ingredient.may,
        'june': ingredient.june,
        'july': ingredient.july,
        'august': ingredient.august,
        'september': ingredient.september,
        'october': ingredient.october,
        'november': ingredient.november,
        'december': ingredient.december
    }

def calculate_recipe_seasonality(
    ingredients: List[Dict[str, Any]], 
    recipe_id: int = None,
    recipe_name: str = 'Unknown Recipe'
) -> Dict[str, bool]:
    """
    Calculate a recipe's seasonality based on its ingredients.
    
    Args:
        ingredients: List of ingredient dictionaries
        recipe_id: Optional recipe ID for logging and database updates
        recipe_name: Name of the recipe for logging
        
    Returns:
        Dictionary mapping month names to boolean availability
    """
    # Dictionary of all months, start with all True (in season)
    recipe_seasonality = {
        'january': True, 'february': True, 'march': True, 'april': True, 
        'may': True, 'june': True, 'july': True, 'august': True, 
        'september': True, 'october': True, 'november': True, 'december': True
    }
    
    if not ingredients or not isinstance(ingredients, list) or len(ingredients) == 0:
        print(f"No ingredients found for recipe ID {recipe_id} ({recipe_name}), cannot calculate seasonality")
        return recipe_seasonality
    
    at_least_one_ingredient_has_seasonality = False
    
    # For each ingredient, check its seasonality
    for ingredient_data in ingredients:
        if not ingredient_data:
            continue
        
        # Handle both direct ingredients and recipe ingredients
        ingredient = ingredient_data.get('ingredient_data', ingredient_data)
        
        # Skip ingredients without seasonality data
        if not ingredient.get('has_seasonality', False):
            continue
        
        # Check if any month is marked as in season
        month_flags = [
            ingredient.get('january', False), 
            ingredient.get('february', False),
            ingredient.get('march', False), 
            ingredient.get('april', False),
            ingredient.get('may', False), 
            ingredient.get('june', False),
            ingredient.get('july', False), 
            ingredient.get('august', False),
            ingredient.get('september', False), 
            ingredient.get('october', False),
            ingredient.get('november', False), 
            ingredient.get('december', False)
        ]
        
        # If at least one month is seasonal, consider this ingredient for the calculation
        if any(month_flags):
            at_least_one_ingredient_has_seasonality = True
            
            # Update recipe seasonality (logical AND with each ingredient's seasonality)
            recipe_seasonality['january'] = recipe_seasonality['january'] and ingredient.get('january', False)
            recipe_seasonality['february'] = recipe_seasonality['february'] and ingredient.get('february', False)
            recipe_seasonality['march'] = recipe_seasonality['march'] and ingredient.get('march', False)
            recipe_seasonality['april'] = recipe_seasonality['april'] and ingredient.get('april', False)
            recipe_seasonality['may'] = recipe_seasonality['may'] and ingredient.get('may', False)
            recipe_seasonality['june'] = recipe_seasonality['june'] and ingredient.get('june', False)
            recipe_seasonality['july'] = recipe_seasonality['july'] and ingredient.get('july', False)
            recipe_seasonality['august'] = recipe_seasonality['august'] and ingredient.get('august', False)
            recipe_seasonality['september'] = recipe_seasonality['september'] and ingredient.get('september', False)
            recipe_seasonality['october'] = recipe_seasonality['october'] and ingredient.get('october', False)
            recipe_seasonality['november'] = recipe_seasonality['november'] and ingredient.get('november', False)
            recipe_seasonality['december'] = recipe_seasonality['december'] and ingredient.get('december', False)
    
    return recipe_seasonality

def format_seasonality(seasonality_data: Dict[str, bool]) -> Dict[str, Any]:
    """
    Format seasonality data for frontend display.
    
    Args:
        seasonality_data: Dictionary mapping month names to boolean availability
        
    Returns:
        Formatted seasonality data for the frontend
    """
    # Map DB month names to month numbers (1-12)
    month_map = {
        'january': 1, 'february': 2, 'march': 3, 'april': 4, 
        'may': 5, 'june': 6, 'july': 7, 'august': 8, 
        'september': 9, 'october': 10, 'november': 11, 'december': 12
    }
    
    # Month abbreviations for display
    month_abbreviations = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ]
    
    # Convert to array of month numbers
    seasonal_months = [
        month_map[month] for month, is_active in seasonality_data.items() 
        if is_active
    ]
    seasonal_months.sort()
    
    # Format data for frontend display
    formatted_data = []
    for i, name in enumerate(month_abbreviations):
        month_number = i + 1  # 1-12
        db_month_name = next(key for key, value in month_map.items() if value == month_number)
        is_active = seasonality_data.get(db_month_name, False)
        
        formatted_data.append({
            'name': name,
            'isActive': is_active,
            'monthNumber': month_number
        })
    
    return {
        'hasSeasonality': len(seasonal_months) > 0,
        'data': formatted_data,
        'seasonalMonths': seasonal_months
    }

def calculate_and_update_recipe_seasonality(recipe, update_db=True):
    """
    Calculate and optionally update seasonality for a specific recipe.
    
    Args:
        recipe: Recipe object
        update_db: Whether to save the result to the database
        
    Returns:
        Tuple of (raw_seasonality_data, formatted_seasonality_data)
    """
    # Get recipe ingredients with prefetch for better performance
    recipe_ingredients = list(recipe.recipe_ingredients.all().select_related('ingredient'))
    
    # Convert to the format expected by calculate_recipe_seasonality
    ingredients = []
    for ri in recipe_ingredients:
        ingredient_data = extract_ingredient_data(ri)
        ingredients.append({'ingredient_data': ingredient_data})
    
    # Calculate seasonality
    seasonality_data = calculate_recipe_seasonality(ingredients, recipe.id, recipe.name)
    
    # Update the database if requested
    if update_db:
        recipe.months = json.dumps(seasonality_data)
        recipe.save()
    
    # Format the seasonality data for frontend display
    formatted_seasonality = format_seasonality(seasonality_data)
    
    return seasonality_data, formatted_seasonality

def update_all_recipe_seasonality() -> Dict[int, Dict[str, bool]]:
    """
    Calculate and update seasonality for all recipes in the database.
    Uses batch processing for better performance.
    
    Returns:
        Dictionary mapping recipe IDs to their updated seasonality data
    """
    results = {}
    
    # Get all recipes with their ingredients in a single query
    recipes = Recipe.objects.prefetch_related('recipe_ingredients__ingredient').all()
    print(f"Processing seasonality update for {len(recipes)} recipes...")
    
    for recipe in recipes:
        # Skip recipes without ingredients
        if not recipe.recipe_ingredients.exists():
            print(f"Recipe {recipe.id} ({recipe.name}) has no ingredients, skipping seasonality update")
            continue
        
        # Calculate seasonality and update
        seasonality_data, _ = calculate_and_update_recipe_seasonality(recipe, update_db=True)
        
        # Store the result
        results[recipe.id] = seasonality_data
    
    print(f"Updated seasonality data for {len(results)} recipes")
    return results 