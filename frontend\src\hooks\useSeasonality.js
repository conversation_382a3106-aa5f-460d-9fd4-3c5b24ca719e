import { useState } from 'react';
import { ENDPOINTS } from '../config/api';

// Simple request cache for API calls
const requestCache = {};

/**
 * Shared hook for seasonality data handling and formatting
 * @returns {Object} Seasonality formatting utilities and helper functions
 */
export const useSeasonality = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Month mapping constants
  const MONTH_NAMES = [
    'January', 'February', 'March', 'April', 
    'May', 'June', 'July', 'August', 
    'September', 'October', 'November', 'December'
  ];
  
  const MONTH_ABBREVIATIONS = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  
  const MONTH_NAME_TO_NUMBER = {
    january: 1, february: 2, march: 3, april: 4, 
    may: 5, june: 6, july: 7, august: 8, 
    september: 9, october: 10, november: 11, december: 12
  };

  /**
   * Convert a month number (1-12) to its name
   * @param {number} monthNum - Month number (1-12)
   * @param {boolean} abbreviated - Whether to use abbreviated month names
   * @returns {string} Month name
   */
  const formatMonth = (monthNum, abbreviated = false) => {
    if (monthNum < 1 || monthNum > 12) {
      console.warn(`Invalid month number: ${monthNum}`);
      return '';
    }
    return abbreviated 
      ? MONTH_ABBREVIATIONS[monthNum - 1] 
      : MONTH_NAMES[monthNum - 1];
  };

  /**
   * Convert a month name to its number (1-12)
   * @param {string} monthName - Month name (case insensitive)
   * @returns {number|null} Month number or null if invalid
   */
  const monthNameToNumber = (monthName) => {
    if (!monthName) return null;
    const normalized = monthName.toLowerCase();
    return MONTH_NAME_TO_NUMBER[normalized] || null;
  };

  /**
   * Safely parse seasonal months data from various formats
   * @param {any} data - Seasonality data in various formats
   * @returns {number[]} Array of month numbers (1-12)
   */
  const safelyParseSeasonalMonths = (data) => {
    if (!data) return [];
    
    // If it's already an array, validate each element is a number
    if (Array.isArray(data)) {
      return data.map(month => {
        // Convert any string numbers to actual numbers
        if (typeof month === 'string' && !isNaN(parseInt(month))) {
          return parseInt(month);
        }
        return month;
      }).filter(month => typeof month === 'number' && month >= 1 && month <= 12);
    }
    
    // Handle JSON strings
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed)) {
          // Clean the parsed array to ensure valid months
          return parsed
            .map(month => {
              if (typeof month === 'string' && !isNaN(parseInt(month))) {
                return parseInt(month);
              }
              return month;
            })
            .filter(month => typeof month === 'number' && month >= 1 && month <= 12);
        } else if (typeof parsed === 'object' && parsed !== null) {
          // Handle object format - some APIs might return {1: true, 2: true, ...}
          // Also handle DB format like {january: true, february: false, ...}
          const monthNumbers = [];
          
          // First, try numeric keys
          for (const key in parsed) {
            if (parsed[key] === true) {
              const num = parseInt(key);
              if (!isNaN(num) && num >= 1 && num <= 12) {
                monthNumbers.push(num);
              }
            }
          }
          
          // If no numeric keys found, try month names
          if (monthNumbers.length === 0) {
            for (const key in parsed) {
              if (parsed[key] === true) {
                const monthNumber = monthNameToNumber(key);
                if (monthNumber) {
                  monthNumbers.push(monthNumber);
                }
              }
            }
          }
          
          return monthNumbers.sort((a, b) => a - b);
        }
        return [];
      } catch (err) {
        console.error('Error parsing seasonality months:', err, data);
        
        // Last resort: split by comma and try to parse
        try {
          if (data.includes(',')) {
            return data.split(',')
              .map(str => str.trim())
              .map(str => parseInt(str))
              .filter(num => !isNaN(num) && num >= 1 && num <= 12);
          }
        } catch (splitErr) {
          console.error('Error parsing comma-separated months:', splitErr);
        }
        return [];
      }
    }
    
    // For any other data type
    console.warn('Unhandled seasonality data type:', typeof data, data);
    return [];
  };

  /**
   * Format raw seasonality data for display
   * @param {Object} rawSeasonality - Raw seasonality data (can be from DB or API)
   * @returns {Object} Formatted seasonality data
   */
  const formatSeasonalityData = (rawSeasonality) => {
    if (!rawSeasonality) {
      return { 
        hasSeasonality: false, 
        data: null, 
        seasonalMonths: [] 
      };
    }
    
    // Check if rawSeasonality is already in the processed format
    if (
      typeof rawSeasonality === 'object' &&
      rawSeasonality !== null &&
      typeof rawSeasonality.hasSeasonality === 'boolean' &&
      Array.isArray(rawSeasonality.data) &&
      // Ensure it looks like the detailed month data array
      rawSeasonality.data.length > 0 && // Can be 0 if no seasonality but still processed
      rawSeasonality.data.every(item =>
        typeof item === 'object' &&
        item !== null &&
        typeof item.name === 'string' &&
        typeof item.isActive === 'boolean' &&
        typeof item.monthNumber === 'number'
      ) &&
      Array.isArray(rawSeasonality.seasonalMonths)
    ) {
      return rawSeasonality;
    }

    // First, convert raw data to array of month numbers
    let seasonalMonths = [];
    
    // If it's already in the {january: true, ...} format
    // This condition now needs to be more specific to avoid catching the already processed format again
    if (typeof rawSeasonality === 'object' && !Array.isArray(rawSeasonality) && !rawSeasonality.hasOwnProperty('hasSeasonality')) {
      seasonalMonths = Object.entries(rawSeasonality)
        .filter(([_, isInSeason]) => isInSeason)
        .map(([month, _]) => monthNameToNumber(month) || 0)
        .filter(num => num > 0)
        .sort((a, b) => a - b);
    } else if (typeof rawSeasonality !== 'object' || Array.isArray(rawSeasonality)) {
      // Otherwise try to parse it with our helper (handles arrays, strings, etc.)
      seasonalMonths = safelyParseSeasonalMonths(rawSeasonality);
    }
    // If rawSeasonality was an object but didn't fit the {january: true} or pre-processed pattern,
    // seasonalMonths might remain empty, which is handled below.
    
    // Generate UI-friendly format with active flags for all months
    const formattedData = MONTH_ABBREVIATIONS.map((name, index) => {
      const monthNumber = index + 1; // 1-12
      const isActive = seasonalMonths.includes(monthNumber);
      
      return {
        name,
        isActive,
        monthNumber
      };
    });
    
    return { 
      hasSeasonality: seasonalMonths.length > 0, 
      data: formattedData,
      seasonalMonths: seasonalMonths 
    };
  };

  /**
   * Format recipe seasonality data, with API calls if needed
   * @param {Object} recipe - Recipe object with potential seasonality data
   * @returns {Promise<Object>} Formatted seasonality data
   */
  const formatRecipeSeasonality = async (recipe) => {
    // If no recipe or recipe ID, return default data
    if (!recipe || !recipe.id) {
      return {
        hasSeasonality: false,
        data: [],
        seasonalMonths: []
      };
    }

    // Check cache first before making API call
    const cacheKey = `recipe_seasonality_${recipe.id}`;
    if (requestCache[cacheKey]) {
      return requestCache[cacheKey];
    }
    
    // Try to fetch the formatted seasonality data from the API
    try {
      // Track in-flight requests
      if (!requestCache[`${cacheKey}_loading`]) {
        requestCache[`${cacheKey}_loading`] = true;
        
        const response = await fetch(ENDPOINTS.RECIPES.SEASONALITY.GET(recipe.id));
        
        if (response.ok) {
          const data = await response.json();
          // Store in cache
          requestCache[cacheKey] = data;
          delete requestCache[`${cacheKey}_loading`];
          return data;
        } else {
          console.warn(`Failed to fetch seasonality for recipe ${recipe.id}:`, response.statusText);
          delete requestCache[`${cacheKey}_loading`];
          // Fall back to using stored data or calculating
        }
      } else {
        // Wait a bit and return whatever is in cache now
        await new Promise(resolve => setTimeout(resolve, 100));
        return requestCache[cacheKey] || formatSeasonalityData(null);
      }
    } catch (err) {
      console.error("Error fetching recipe seasonality:", err);
      delete requestCache[`${cacheKey}_loading`];
      // Fall back to using stored data or calculating
    }
    
    // Try to use the stored months data if available
    let recipeSeasonality = null;
    
    if (recipe.months) {
      try {
        // Parse the JSON string from the database
        recipeSeasonality = JSON.parse(recipe.months);
        
        // Handle the case where months contains a season name like "Winter" instead of JSON
        if (typeof recipeSeasonality === 'string') {
          console.warn(`Recipe ${recipe.id} has a string value "${recipeSeasonality}" for months instead of JSON. Will calculate from ingredients.`);
          recipeSeasonality = null;
        }
      } catch (e) {
        console.error(`Error parsing recipe months data for recipe ${recipe.id}:`, e);
        // Fall back to calculating if parsing fails
        recipeSeasonality = null;
      }
    }
    
    // Use the generic formatter with whatever data we have
    const result = formatSeasonalityData(recipeSeasonality);
    
    // Store in cache
    requestCache[cacheKey] = result;
    
    return result;
  };

  /**
   * Fetch seasonality data for multiple recipes in a single API call
   * @param {Array} recipeIds - Array of recipe IDs to fetch
   * @returns {Promise<Object>} Object mapping recipe IDs to seasonality data
   */
  const fetchBatchSeasonality = async (recipeIds) => {
    if (!recipeIds || !recipeIds.length) {
      return {};
    }

    // Filter out IDs that are already in cache
    const uncachedIds = recipeIds.filter(id => !requestCache[`recipe_seasonality_${id}`]);
    
    // If all recipes are cached, return them from cache
    if (uncachedIds.length === 0) {
      const results = {};
      recipeIds.forEach(id => {
        results[id] = requestCache[`recipe_seasonality_${id}`];
      });
      return results;
    }
    
    // Generate a unique key for this batch request
    const batchCacheKey = `batch_${uncachedIds.sort().join('_')}`;
    
    // Check if this exact batch is already being fetched
    if (requestCache[`${batchCacheKey}_loading`]) {
      // Wait a bit and try again
      await new Promise(resolve => setTimeout(resolve, 100));
      return fetchBatchSeasonality(recipeIds);
    }
    
    // Mark this batch as being loaded
    requestCache[`${batchCacheKey}_loading`] = true;
    
    try {
      // Make the batch request
      const response = await fetch(ENDPOINTS.RECIPES.SEASONALITY.BATCH, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipe_ids: uncachedIds
        })
      });
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      const batchData = await response.json();
      
      // Update the cache with individual recipe data
      Object.entries(batchData).forEach(([id, data]) => {
        requestCache[`recipe_seasonality_${id}`] = data;
      });
      
      // Clean up loading flag
      delete requestCache[`${batchCacheKey}_loading`];
      
      // Combine with any already cached results
      const results = { ...batchData };
      
      // Add any cached recipes that weren't in the batch request
      recipeIds.forEach(id => {
        if (!results[id] && requestCache[`recipe_seasonality_${id}`]) {
          results[id] = requestCache[`recipe_seasonality_${id}`];
        }
      });
      
      return results;
    } catch (err) {
      console.error("Error fetching batch seasonality:", err);
      // Clean up loading flag
      delete requestCache[`${batchCacheKey}_loading`];
      
      // Fall back to individual requests
      const results = {};
      for (const id of recipeIds) {
        const recipe = { id };
        results[id] = await formatRecipeSeasonality(recipe);
      }
      return results;
    }
  };

  /**
   * Clear the seasonality cache for testing or when data is updated
   */
  const clearSeasonalityCache = () => {
    for (const key in requestCache) {
      if (key.startsWith('recipe_seasonality_') || key.startsWith('batch_')) {
        delete requestCache[key];
      }
    }
  };

  return {
    isLoading,
    error,
    setIsLoading,
    setError,
    formatMonth,
    monthNameToNumber,
    safelyParseSeasonalMonths,
    formatSeasonalityData,
    formatRecipeSeasonality,
    fetchBatchSeasonality,
    clearSeasonalityCache,
    MONTH_NAMES,
    MONTH_ABBREVIATIONS,
    MONTH_NAME_TO_NUMBER
  };
};

export default useSeasonality; 