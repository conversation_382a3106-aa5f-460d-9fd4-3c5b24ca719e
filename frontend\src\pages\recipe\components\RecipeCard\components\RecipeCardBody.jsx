import React from 'react';
import SeasonalityDisplay from '../../../../../components/SeasonalityDisplay';
import '../styles/RecipeCardBody.css';

const RecipeCardBody = ({ recipe, seasonalityData, toggleInstructions, instructionsExpanded }) => {
  if (!recipe) {
    return null;
  }

  return (
    <div className="recipe-body">
      <div className="recipe-seasonality">
        {seasonalityData && seasonalityData.seasonalMonths && seasonalityData.seasonalMonths.length > 0 ? (
          <SeasonalityDisplay 
            seasonalMonths={seasonalityData.seasonalMonths} 
            size="small"
            highlightCurrent={true}
          />
        ) : (
          <p className="no-seasonality">No seasonality data available</p>
        )}
      </div>
      
      <div className="recipe-ingredients">
        <h4>Ingredients:</h4>
        <ul>
          {recipe.recipe_ingredients?.map((recipeIngredient, index) => (
            <li key={index}>
              {recipeIngredient.ingredient_name}: {recipeIngredient.quantity} {recipeIngredient.unit}
            </li>
          ))}
        </ul>
      </div>
      
      <div className="recipe-instructions">
        <div className="instructions-header" onClick={toggleInstructions}>
          <h4>Instructions:</h4>
          <span className="dropdown-arrow">
            {instructionsExpanded ? '▲' : '▼'}
          </span>
        </div>
        
        {instructionsExpanded && (
          <div className="instructions-content">
            <p>{recipe.instructions}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecipeCardBody;
