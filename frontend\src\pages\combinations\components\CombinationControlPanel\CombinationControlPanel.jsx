import React from 'react';
import './styles/CombinationControlPanel.css';
// Tooltip is now in FindCombinationsButton
import ServingsInput from './components/ServingsInput'; // Import the new component
import MonthInput from './components/MonthInput'; // Import the new MonthInput component
import FindCombinationsButton from './components/FindCombinationsButton'; // Import the new button component

const ControlPanel = ({
  servings,
  month,
  diet, // New prop for diet
  loading, // Use the general loading state from the hook
  onServingsChange,
  onMonthChange,
  onDietChange, // New handler prop for diet
  onFindAndSaveCombinations, // New handler prop
  isProduction, // Accept isProduction prop
}) => {

  return (
    <div className="controls">

      <ServingsInput servings={servings} onServingsChange={onServingsChange} />
      
      <MonthInput month={month} onMonthChange={onMonthChange} />
      
      <div className="control-group">
        <label htmlFor="diet-select">Filter by Diet:</label>
        <select
          id="diet-select"
          value={diet}
          onChange={(e) => onDietChange(e.target.value)}
        >
          <option value="vegan">Vegan</option>
          <option value="vegetarian">Vegetarian</option>
          <option value="fish">Fish</option>
          <option value="meat">Meat</option>
        </select>
      </div>

      {!isProduction && ( // Conditionally render the button group
        <div className="button-group">
          <FindCombinationsButton
            loading={loading}
            onFindAndSave={onFindAndSaveCombinations}
            servings={servings}
            month={month}
          />
        </div>
      )}
    </div>
  );
};

export default ControlPanel;
