import React from 'react';

// Updated props: removed testImages and currentRecipeIndex, added imageUrl
const CardRecipeImage = ({ imageUrl, activeRecipe, goToPreviousRecipe, goToNextRecipe, recipes }) => {
  // Updated condition to check for imageUrl
  if (!imageUrl) {
    // Display a placeholder or a message if no image URL is provided
    return (
      <div className="recipe-image-container" style={{
        textAlign: 'center',
        marginBottom: '10px',
        width: '100%',
        height: 'auto',
        minHeight: '300px',
        border: '1px solid #ccc',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f0f0f0',
        position: 'relative'
      }}>
        <span>No image for {activeRecipe?.name || 'recipe'}</span>
        {/* Recipe name overlay for placeholder */}
        {activeRecipe?.name && (
          <div style={{
            position: 'absolute',
            bottom: '15px',
            left: '50%',
            transform: 'translateX(-50%)',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '6px',
            fontSize: '1.1em',
            fontWeight: '500',
            textAlign: 'center',
            maxWidth: '90%',
            zIndex: 2
          }}>
            {activeRecipe.name}
          </div>
        )}
      </div>
    );
  }

  // Check if there are multiple recipes to enable navigation
  const canNavigate = recipes && recipes.length > 1;

  return (
    <div style={{ width: '100%' }}>
      <div className="recipe-image-container" style={{
        textAlign: 'center',
        marginBottom: '10px',
        position: 'relative',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '300px' // Ensure consistent height for centering
      }}>
        {canNavigate && (
          <button onClick={goToPreviousRecipe} style={{
            position: 'absolute',
            left: '10px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 3,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid #ddd',
            borderRadius: '50%',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>
            &lt;
          </button>
        )}
        <img
          src={imageUrl} // Use the imageUrl prop
          alt={`Recipe Image for ${activeRecipe?.name || 'Current Recipe'}`}
          style={{
            width: '100%',
            height: 'auto', // Let height be determined by aspect ratio
            maxHeight: '400px', // Increased to better accommodate 1024x1536 images
            objectFit: 'cover', // Use cover to fill the space nicely
            display: 'block',
            borderRadius: '8px'
          }}
        />
        {canNavigate && (
          <button onClick={goToNextRecipe} style={{
            position: 'absolute',
            right: '10px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 3,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid #ddd',
            borderRadius: '50%',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>
            &gt;
          </button>
        )}
      </div>
      {/* Recipe name below the card */}
      {activeRecipe?.name && (
        <div style={{
          textAlign: 'center',
          marginTop: '8px',
          padding: '8px 16px',
          backgroundColor: '#f8f9fa',
          borderRadius: '6px',
          fontSize: '1.1em',
          fontWeight: '500',
          color: '#333',
          border: '1px solid #e9ecef'
        }}>
          {activeRecipe.name}
        </div>
      )}
    </div>
  );
};

export default CardRecipeImage;
