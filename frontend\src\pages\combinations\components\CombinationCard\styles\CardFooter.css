.card-footer {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.serving-info {
  font-size: 0.9em;
  color: #495057;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.save-btn, .delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

.save-btn:hover {
  background-color: #218838;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-footer {
    flex-direction: column;
    gap: 10px;
  }
  
  .serving-info {
    margin-bottom: 10px;
  }
}