.ingredients-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.ingredients-table th, 
.ingredients-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.ingredients-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.ingredients-table tr:hover {
  background-color: #f5f5f5;
}

.type-cell {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.type-vegetable { background-color: #E8F5E9; color: #2E7D32; }
.type-herb { background-color: #F1F8E9; color: #558B2F; }
.type-meat { background-color: #FFEBEE; color: #C62828; }
.type-dairy { background-color: #E8EAF6; color: #3949AB; }
.type-oil { background-color: #FFF8E1; color: #FF8F00; }
.type-spice { background-color: #FBE9E7; color: #D84315; }
.type-grain { background-color: #EFEBE9; color: #6D4C41; }
.type-preserve { background-color: #E0F7FA; color: #006064; }

.seasonality-cell {
  min-width: 200px;
}

/* Editable cell styles */
.editable-cell {
  position: relative;
  cursor: pointer;
}

.editable-cell:hover .edit-icon {
  opacity: 1;
}

.edit-icon {
  opacity: 0;
  margin-left: 5px;
  color: #999;
  transition: opacity 0.2s;
  font-size: 12px;
}

.editable-cell.editing {
  padding: 0;
}

.edit-controls {
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.edit-controls input,
.edit-controls select {
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

.edit-buttons {
  display: flex;
  gap: 5px;
}

.save-button,
.cancel-button {
  padding: 2px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.save-button {
  background-color: #4CAF50;
  color: white;
}

.cancel-button {
  background-color: #f44336;
  color: white;
}

.save-status {
  font-size: 12px;
  margin-top: 5px;
  text-align: center;
}

.status-saving {
  background-color: #FFF9C4;
}

.status-success {
  background-color: #E8F5E9;
}

.status-error {
  background-color: #FFEBEE;
} 