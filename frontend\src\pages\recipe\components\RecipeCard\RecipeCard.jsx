import React, { useState } from 'react';
import './styles/RecipeCard.css';

// Import sub-components
import RecipeCardHeader from './components/RecipeCardHeader';
import RecipeImageUploader from './components/RecipeImageUploader';
import RecipeCardBody from './components/RecipeCardBody';
import RecipeCardFooter from './components/RecipeCardFooter';

// Import custom hooks and comparison function
import useRecipeImageUpload from './hooks/useRecipeImageUpload';
import useRecipeInstructions from './hooks/useRecipeInstructions';
import { recipeCardPropsAreEqual } from './hooks/recipeCardComparison'; // Import the comparison function

const RecipeCard = ({ recipe, formatSeasonality, handleEditClick, handleDeleteRecipe, onRecipeUpdate }) => {
  const seasonalityData = formatSeasonality(recipe);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    currentImage,
    uploadError,
    isUploading,
    onDrop,
  } = useRecipeImageUpload(recipe.image, recipe.id, onRecipeUpdate);

  const {
    instructionsExpanded,
    toggleInstructions,
  } = useRecipeInstructions();

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleCardClick = (e) => {
    // Don't toggle if clicking on the image uploader area
    if (e.target.closest('.recipe-image-container')) {
      return;
    }
    toggleExpanded();
  };

  return (
    <div key={recipe.id} className={`recipe-card ${isExpanded ? 'expanded' : 'collapsed'}`}>
      {/* Always visible: Header and Image */}
      <div className="recipe-card-always-visible" onClick={handleCardClick}>
        <RecipeCardHeader recipe={recipe} />

        <RecipeImageUploader
          recipeName={recipe.name}
          currentImage={currentImage}
          isUploading={isUploading}
          uploadError={uploadError}
          onDrop={onDrop}
        />

        {/* Click indicator */}
        <div className="expand-indicator">
          <span>{isExpanded ? '▲ Click to collapse' : '▼ Click to expand'}</span>
        </div>
      </div>

      {/* Expandable content: Body and Footer */}
      {isExpanded && (
        <div className="recipe-card-expandable">
          <RecipeCardBody
            recipe={recipe}
            seasonalityData={seasonalityData}
            toggleInstructions={toggleInstructions}
            instructionsExpanded={instructionsExpanded}
          />

          <RecipeCardFooter
            recipe={recipe}
            handleEditClick={handleEditClick}
            handleDeleteRecipe={handleDeleteRecipe}
          />
        </div>
      )}
    </div>
  );
};

// The recipeCardPropsAreEqual function is now imported and used here
const MemoizedRecipeCard = React.memo(RecipeCard, recipeCardPropsAreEqual);

export default MemoizedRecipeCard;
