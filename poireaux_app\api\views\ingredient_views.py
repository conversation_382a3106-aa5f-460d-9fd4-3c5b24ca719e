from rest_framework.decorators import api_view
from rest_framework.response import Response
from ...services.ingredients import ingredient_service
from ..serializers import IngredientSerializer, IngredientListSerializer
from ...models import Ingredient # Import the Ingredient model

@api_view(['GET'])
def get_ingredients(request):
    """
    Get all ingredients, optionally filtered by type
    """
    # Get optional query parameters for filtering
    ingredient_type = request.query_params.get('type', None)
    
    # Check if detailed view is requested
    detailed = request.query_params.get('detailed', 'true').lower() == 'true'
    
    # Get ingredients from service
    ingredients = ingredient_service.get_all_ingredients(ingredient_type)
    
    # Choose appropriate serializer based on detail level
    if detailed:
        serializer = IngredientSerializer(ingredients, many=True)
    else:
        serializer = IngredientListSerializer(ingredients, many=True)
    
    # Add cache headers to response to improve client-side caching
    response = Response(serializer.data)
    response["Cache-Control"] = "max-age=300"  # Cache for 5 minutes
    
    return response

@api_view(['POST'])
def create_ingredient(request):
    """
    Create a new ingredient
    """
    serializer = IngredientSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=201)
    return Response(serializer.errors, status=400)

@api_view(['PATCH'])
def update_ingredient(request, pk):
    """
    Update an ingredient by ID
    """
    try:
        # Log the incoming request data
        print("--- Backend: Received ingredient update data ---")
        print(request.data)
        
        # Update the ingredient
        updated_ingredient = ingredient_service.update_ingredient(pk, request.data)
        
        # Return the updated ingredient with all its data
        return Response(IngredientSerializer(updated_ingredient).data)
    except ValueError as e:
        # Handle validation errors (e.g., bad data format)
        return Response({"error": str(e)}, status=400)
    except Ingredient.DoesNotExist:
        # Handle case where the ingredient ID does not exist
        return Response({'error': f'Ingredient with ID {pk} not found.'}, status=404)
    except Exception as e:
        # Handle unexpected server errors
        # Log the full error for debugging
        print(f"Unexpected error updating ingredient {pk}: {e}")
        return Response({'error': 'An unexpected error occurred on the server.'}, status=500)
