import json
from decimal import Decimal
from typing import List, Dict, Any, Union, Optional
from ...models import Recipe, RecipeIngredient, Ingredient

# List of ingredients that can't be divided (like bay leaves)
# These will be rounded to the nearest whole number, but never less than 1 if quantity is > 0

def check_recipes_divisibility(recipes: List[Dict[str, Any]]) -> Dict[int, bool]:
    """
    Check divisibility for a group of recipes.
    
    Args:
        recipes: List of recipe dictionaries
        
    Returns:
        Dictionary mapping recipe IDs to their divisibility status
    """
    results = {}
    
    # Group recipes by name
    recipe_groups = {}
    for recipe in recipes:
        name = recipe.get('name', '')
        if name not in recipe_groups:
            recipe_groups[name] = []
        recipe_groups[name].append(recipe)
    
    # Process each recipe group
    for name, recipe_set in recipe_groups.items():
        # For each recipe in the group, check its own divisibility
        for recipe in recipe_set:
            recipe_id = recipe.get('id')
            recipe_servings = recipe.get('servings', 1)
            recipe_ingredients = recipe.get('recipe_ingredients', [])
            
            # Skip recipes without ingredients
            if not recipe_ingredients:
                print(f"Recipe {recipe_id} ({recipe.get('name')}) has no ingredients, skipping divisibility check")
                results[recipe_id] = False
                continue
            
            # Check if ingredients can be properly divided with current serving size
            # We'll check if rounding would significantly change the current quantities
            is_divisible = True
            for ingredient_data in recipe_ingredients:
                ingredient = ingredient_data.get('ingredient_data', ingredient_data)
                if not ingredient:
                    continue
                
                # Get current quantity from the database
                quantity = float(ingredient_data.get('quantity', 0))
                # Get ingredient name
                ingredient_name = getattr(ingredient, 'name', '').lower() if hasattr(ingredient, 'name') else ingredient.get('name', '').lower()
                # Default to True for ingredient divisibility
                ingredient_is_divisible = True
                
                
                
                # Parse the divisible_by_int to get allowed divisors
                allowed_divisors = [1]  # Default
                try:
                    divisible_by_int = getattr(ingredient, 'divisible_by_int', '[1]') if hasattr(ingredient, 'divisible_by_int') else ingredient.get('divisible_by_int', '[1]')
                    if isinstance(divisible_by_int, str):
                        allowed_divisors = json.loads(divisible_by_int)
                    elif isinstance(divisible_by_int, list):
                        allowed_divisors = divisible_by_int
                except Exception as e:
                    print(f"Error parsing divisible_by_int: {e}")
                
                # Get measurement type
                measurement_type = getattr(ingredient, 'divisible_by_measurement', 'unit') if hasattr(ingredient, 'divisible_by_measurement') else ingredient.get('divisible_by_measurement', 'unit')
                
                # For gram/ml measurements, check if current quantity matches the expected rounded value
                if measurement_type in ['g', 'ml'] and allowed_divisors and allowed_divisors[0] > 0:
                    base_unit = allowed_divisors[0]
                    rounded_value = round(quantity / base_unit) * base_unit
                    print(f"DEBUG: Divisibility - Grams/ML: Base Unit: {base_unit}, Current Quantity: {quantity}, Expected Rounded Value: {rounded_value}")
                    
                    # If the stored value differs significantly from what would be expected after rounding,
                    # this suggests the recipe is not properly divisible
                    if abs(rounded_value - quantity) > 1e-9 and abs(rounded_value - quantity) > base_unit * 0.1:
                        print(f"[{recipe.get('name')}] Ingredient \"{ingredient_name}\" stored value ({quantity:.3f} {measurement_type}) " 
                              f"differs from expected rounded value ({rounded_value:.3f} {measurement_type})")
                        ingredient_is_divisible = False
                        is_divisible = False
                
                # For unit-type measurements, check if the current quantity is divisible by the allowed divisors
                elif measurement_type in ['unit', 'tsp', 'tbsp', 'handful', 'sprig', 'pinch']:
                    exactly_divisible = False
                    for divisor in allowed_divisors:
                        if abs((quantity * divisor) % 1) < 1e-9:
                            exactly_divisible = True
                            break
                    
                    if not exactly_divisible:
                        # If not exactly divisible, check if it's close to a valid fraction
                        valid_fraction = False
                        rounding_margin = 0.3
                        
                        # Special handling for very small quantities (less than 0.25)
                        # These should be at least 0.5 for unit-type measurements
                        if quantity > 0.001 and quantity < 0.25:
                            print(f"[{recipe.get('name')}] Ingredient \"{ingredient_name}\" has very small quantity ({quantity}) " 
                                 f"for measurement type {measurement_type} - should be at least 0.5")
                            ingredient_is_divisible = False
                            is_divisible = False
                        else:
                            for divisor in allowed_divisors:
                                for i in range(int(quantity * divisor) + 2):
                                    valid_value = i / divisor
                                    # Skip zero values for unit-type measurements
                                    if valid_value < 0.001:
                                        continue
                                    if abs(quantity - valid_value) <= rounding_margin:
                                        valid_fraction = True
                                        break
                            
                            if not valid_fraction:
                                print(f"[{recipe.get('name')}] Ingredient \"{ingredient_name}\" quantity ({quantity}) " 
                                      f"is not a valid fraction for measurement type {measurement_type}")
                                ingredient_is_divisible = False
                                is_divisible = False
                
                # Set the is_divisible field on the ingredient data
                ingredient_data['is_divisible'] = ingredient_is_divisible
            
            print(f"Recipe {recipe_id} ({recipe.get('name')}, servings: {recipe_servings}) divisibility: {is_divisible}")
            results[recipe_id] = is_divisible
    
    return results 
