import React, { useState, useEffect } from 'react';
import { useCombinations } from './hooks/useCombinations';
import CombinationList from './components/CombinationList'; // Added import
import SideFilterPanel from './components/SideFilterPanel/SideFilterPanel';
import ErrorMessageDisplay from './components/ErrorMessageDisplay'; // Added import
import './styles/combinations.css';

function CombinationsPage({ isProduction }) { // Accept isProduction prop
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const {
    combinations, // Now holds only the current page's results
    loading, // General loading state
    error,
    servings,
    month,
    diet, // New state for diet
    setServings,
    setMonth,
    setDiet, // New setter for diet
    findAndSaveCombinations, // The function ControlPanel needs
    saveCombination, // Keep for CombinationCard
    retryOperation, // Ensure this retries the current page fetch correctly
  } = useCombinations();



  return (
    <div className="combinations-container">
      <h1>Optimal Recipe Combinations</h1>
      <p className="subtitle">Find and save combinations of 3 recipes that minimize food waste</p>

      <ErrorMessageDisplay error={error} onRetry={() => retryOperation('fetch')} />

      {/* <CombinationHeader /> Header component for better separation */}

      {/* Use side filter panel for both mobile and desktop */}
      <SideFilterPanel
        servings={servings}
        month={month}
        diet={diet}
        loading={loading}
        onServingsChange={setServings}
        onMonthChange={setMonth}
        onDietChange={setDiet}
        onFindAndSaveCombinations={findAndSaveCombinations}
        isProduction={isProduction}
        isMobile={isMobile}
      />

      <CombinationList
        loading={loading}
        combinations={combinations}
        onSaveCombination={saveCombination}
      />
    </div>
  );
}

export default CombinationsPage;
