# Generated by Django 5.1.7 on 2025-04-17 09:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('poireaux_app', '0016_ingredient_needs_calculating'),
    ]

    operations = [
        migrations.AddField(
            model_name='combination',
            name='cache_valid',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='combination',
            name='dependent_ingredients',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='combination',
            name='ingredient_waste',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='combination',
            name='is_top_performer',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='combination',
            name='last_calculated',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddIndex(
            model_name='combination',
            index=models.Index(fields=['servings'], name='poireaux_ap_serving_583bb3_idx'),
        ),
        migrations.AddIndex(
            model_name='combination',
            index=models.Index(fields=['waste_score'], name='poireaux_ap_waste_s_d0f03d_idx'),
        ),
        migrations.AddIndex(
            model_name='combination',
            index=models.Index(fields=['is_top_performer'], name='poireaux_ap_is_top__5804a0_idx'),
        ),
        migrations.AddIndex(
            model_name='combination',
            index=models.Index(fields=['cache_valid'], name='poireaux_ap_cache_v_15114d_idx'),
        ),
    ]
