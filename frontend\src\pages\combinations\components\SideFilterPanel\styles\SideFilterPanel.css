/* Filter Toggle Button */
.filter-toggle-btn {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px 16px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  transition: all 0.2s ease;
}

.filter-toggle-btn:hover {
  background: rgba(248, 249, 250, 0.95);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.filter-icon {
  font-size: 1rem;
}

/* Overlay */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  z-index: 1001;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Side Panel */
.side-filter-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1002;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.side-filter-panel.open {
  transform: translateX(0);
}

/* Panel Header */
.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(248, 249, 250, 0.8);
  backdrop-filter: blur(5px);
}

.filter-panel-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
}

/* Panel Content */
.filter-panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30px;
  padding: 16px;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-section h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  color: #495057;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 8px;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.filter-select {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid rgba(206, 212, 218, 0.8);
  border-radius: 6px;
  font-size: 0.9rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.filter-select:hover {
  border-color: rgba(74, 144, 226, 0.6);
}

.filter-actions {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 16px 0;
  margin-top: 20px;
  background: rgba(248, 249, 250, 0.3);
  border-radius: 8px;
}

/* Override styles for input components within the filter panel */
.side-filter-panel .control-group {
  margin-bottom: 0;
  display: block;
}

.side-filter-panel .control-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
  min-width: auto;
  margin-right: 0;
}

.side-filter-panel .control-group select {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid rgba(206, 212, 218, 0.8);
  border-radius: 6px;
  font-size: 0.9rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: none;
}

.side-filter-panel .control-group select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.side-filter-panel .control-group select:hover {
  border-color: rgba(74, 144, 226, 0.6);
}

/* Style the Find Combinations button in the filter panel */
.side-filter-panel .find-save-btn {
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.side-filter-panel .find-save-btn:hover {
  background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
  box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);
  transform: translateY(-1px);
}

.side-filter-panel .find-save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Mobile Modal Styles */
.side-filter-panel.mobile-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  right: auto;
  bottom: auto;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 400px;
  height: auto;
  max-height: 80vh;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  opacity: 0;
  pointer-events: none; /* Prevent click interception when closed */
  transition: all 0.3s ease;
}

.side-filter-panel.mobile-modal.open {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  pointer-events: auto; /* Re-enable click events when open */
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .filter-toggle-btn {
    top: 70px;
    right: 15px;
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .side-filter-panel:not(.mobile-modal) {
    width: 280px;
  }

  .filter-panel-header {
    padding: 15px;
  }

  .filter-panel-header h3 {
    font-size: 1.1rem;
  }

  .filter-panel-content {
    padding: 15px;
  }

  .filter-group {
    margin-bottom: 16px;
  }

  /* Mobile modal specific adjustments */
  .side-filter-panel.mobile-modal .filter-panel-content {
    max-height: 60vh;
    overflow-y: auto;
  }

  .side-filter-panel.mobile-modal .filter-section {
    margin-bottom: 20px;
    padding: 12px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .side-filter-panel {
    width: 100%;
    max-width: 300px;
  }
}
