from django.test import LiveServerTestCase
from django.urls import reverse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import json
import time
from django.utils import timezone

from poireaux_app.models import Recipe, Ingredient, RecipeIngredient, Combination

class CombinationWorkflowTestCase(LiveServerTestCase):
    """End-to-end test for the combination workflow."""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Set up the Selenium webdriver
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')  # Run in headless mode
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        cls.selenium = webdriver.Chrome(options=options)
        cls.selenium.implicitly_wait(10)
    
    @classmethod
    def tearDownClass(cls):
        cls.selenium.quit()
        super().tearDownClass()
    
    def setUp(self):
        """Set up test data."""
        # Create test ingredients
        self.ingredient1 = Ingredient.objects.create(
            name="Test Ingredient 1",
            bought_by="weight",
            bought_by_amount=500,  # 500g packaging
            unit="g"
        )
        self.ingredient2 = Ingredient.objects.create(
            name="Test Ingredient 2",
            bought_by="weight",
            bought_by_amount=250,  # 250g packaging
            unit="g"
        )
        self.ingredient3 = Ingredient.objects.create(
            name="Test Ingredient 3",
            bought_by="unit",
            bought_by_amount=6,  # Pack of 6
            unit="unit"
        )
        
        # Create test recipes
        self.recipe1 = Recipe.objects.create(
            name="Test Recipe 1",
            servings=2
        )
        self.recipe2 = Recipe.objects.create(
            name="Test Recipe 2",
            servings=4
        )
        self.recipe3 = Recipe.objects.create(
            name="Test Recipe 3",
            servings=2
        )
        self.recipe4 = Recipe.objects.create(
            name="Test Recipe 4",
            servings=2
        )
        
        # Create recipe ingredients
        RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient1,
            quantity=200,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient3,
            quantity=2,
            unit="unit"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient1,
            quantity=150,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient2,
            quantity=100,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe3,
            ingredient=self.ingredient2,
            quantity=75,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe3,
            ingredient=self.ingredient3,
            quantity=1,
            unit="unit"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe4,
            ingredient=self.ingredient1,
            quantity=100,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe4,
            ingredient=self.ingredient2,
            quantity=50,
            unit="g"
        )
    
    def test_combination_workflow(self):
        """
        Test the end-to-end workflow for combinations:
        1. Generate combinations
        2. View cached results
        3. Trigger recalculation
        4. See updated results
        """
        # Navigate to the combinations page
        self.selenium.get(f"{self.live_server_url}/combinations/")
        
        # Wait for the page to load
        try:
            WebDriverWait(self.selenium, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "controls"))
            )
        except TimeoutException:
            self.fail("Timed out waiting for page to load")
        
        # Step 1: Generate combinations
        
        # Select 2 servings
        servings_select = Select(self.selenium.find_element(By.ID, "servings"))
        servings_select.select_by_value("2")
        
        # Click the generate button
        generate_button = self.selenium.find_element(By.CLASS_NAME, "generate-btn")
        generate_button.click()
        
        # Wait for combinations to be generated
        try:
            WebDriverWait(self.selenium, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "combination-card"))
            )
        except TimeoutException:
            self.fail("Timed out waiting for combinations to be generated")
        
        # Check that combinations were generated
        combination_cards = self.selenium.find_elements(By.CLASS_NAME, "combination-card")
        self.assertGreater(len(combination_cards), 0)
        
        # Step 2: View cached results
        
        # Refresh the page to simulate a new visit
        self.selenium.refresh()
        
        # Wait for the page to load
        try:
            WebDriverWait(self.selenium, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "controls"))
            )
        except TimeoutException:
            self.fail("Timed out waiting for page to load")
        
        # Check that combinations are loaded from cache
        combination_cards = self.selenium.find_elements(By.CLASS_NAME, "combination-card")
        self.assertGreater(len(combination_cards), 0)
        
        # Step 3: Trigger recalculation
        
        # Update an ingredient to invalidate combinations
        self.ingredient1.bought_by_amount = 1000
        self.ingredient1.save(update_fields=['bought_by_amount'])
        
        # Click the recalculate button
        recalculate_button = self.selenium.find_element(By.CLASS_NAME, "recalculate-btn")
        recalculate_button.click()
        
        # Wait for recalculation to start
        try:
            WebDriverWait(self.selenium, 10).until(
                EC.text_to_be_present_in_element(
                    (By.CLASS_NAME, "recalculate-btn"),
                    "Recalculating..."
                )
            )
        except TimeoutException:
            self.fail("Timed out waiting for recalculation to start")
        
        # Wait for recalculation to complete
        try:
            WebDriverWait(self.selenium, 30).until(
                EC.text_to_be_present_in_element(
                    (By.CLASS_NAME, "recalculate-btn"),
                    "Recalculate"
                )
            )
        except TimeoutException:
            self.fail("Timed out waiting for recalculation to complete")
        
        # Step 4: See updated results
        
        # Refresh the page to see updated results
        self.selenium.refresh()
        
        # Wait for the page to load
        try:
            WebDriverWait(self.selenium, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "controls"))
            )
        except TimeoutException:
            self.fail("Timed out waiting for page to load")
        
        # Check that combinations are still displayed
        combination_cards = self.selenium.find_elements(By.CLASS_NAME, "combination-card")
        self.assertGreater(len(combination_cards), 0)
        
        # Check that the last calculated timestamp is recent
        last_calculated_element = self.selenium.find_element(By.CLASS_NAME, "last-calculated")
        last_calculated_text = last_calculated_element.text
        self.assertIn("Last calculated:", last_calculated_text)
        
        # Additional test: Filter combinations
        
        # Show filters
        filter_toggle = self.selenium.find_element(By.CLASS_NAME, "filter-toggle")
        filter_toggle.click()
        
        # Wait for filters to be displayed
        try:
            WebDriverWait(self.selenium, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "filters-container"))
            )
        except TimeoutException:
            self.fail("Timed out waiting for filters to be displayed")
        
        # Enter a search term
        search_input = self.selenium.find_element(By.XPATH, "//input[@placeholder='Search by recipe name...']")
        search_input.clear()
        search_input.send_keys("Test Recipe 1")
        
        # Wait for filtered results
        time.sleep(1)  # Allow time for the filter to be applied
        
        # Check that filtered combinations are displayed
        filtered_cards = self.selenium.find_elements(By.CLASS_NAME, "combination-card")
        
        # At least one combination should contain "Test Recipe 1"
        recipe_names_visible = False
        for card in filtered_cards:
            if "Test Recipe 1" in card.text:
                recipe_names_visible = True
                break
        
        self.assertTrue(recipe_names_visible, "Filtered combinations should contain 'Test Recipe 1'")