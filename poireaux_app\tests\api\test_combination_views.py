import json
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from unittest.mock import patch

from poireaux_app.models import Recipe, Ingredient, RecipeIngredient, Combination

class CombinationViewsTestCase(TestCase):
    """Test case for the combination API views."""
    
    def setUp(self):
        """Set up test data and API client."""
        self.client = APIClient()
        
        # Create test ingredients
        self.ingredient1 = Ingredient.objects.create(
            name="Test Ingredient 1",
            bought_by="weight",
            bought_by_amount=500,  # 500g packaging
            unit="g"
        )
        self.ingredient2 = Ingredient.objects.create(
            name="Test Ingredient 2",
            bought_by="weight",
            bought_by_amount=250,  # 250g packaging
            unit="g"
        )
        self.ingredient3 = Ingredient.objects.create(
            name="Test Ingredient 3",
            bought_by="unit",
            bought_by_amount=6,  # Pack of 6
            unit="unit"
        )
        
        # Create test recipes
        self.recipe1 = Recipe.objects.create(
            name="Test Recipe 1",
            servings=2
        )
        self.recipe2 = Recipe.objects.create(
            name="Test Recipe 2",
            servings=4
        )
        self.recipe3 = Recipe.objects.create(
            name="Test Recipe 3",
            servings=2
        )
        
        # Create recipe ingredients
        RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient1,
            quantity=200,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe1,
            ingredient=self.ingredient3,
            quantity=2,
            unit="unit"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient1,
            quantity=150,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe2,
            ingredient=self.ingredient2,
            quantity=100,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe3,
            ingredient=self.ingredient2,
            quantity=75,
            unit="g"
        )
        RecipeIngredient.objects.create(
            recipe=self.recipe3,
            ingredient=self.ingredient3,
            quantity=1,
            unit="unit"
        )
        
        # Create test combinations
        self.combination1 = Combination.objects.create(
            name="Test Combination 1",
            servings=2,
            waste_score=25.5,
            seasonality_months=json.dumps([1, 2, 3]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=True,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient3.id])
        )
        self.combination1.recipes.add(self.recipe1, self.recipe2)
        
        self.combination2 = Combination.objects.create(
            name="Test Combination 2",
            servings=2,
            waste_score=30.0,
            seasonality_months=json.dumps([4, 5, 6]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=False,
            dependent_ingredients=json.dumps([self.ingredient2.id, self.ingredient3.id])
        )
        self.combination2.recipes.add(self.recipe1, self.recipe3)
        
        self.combination3 = Combination.objects.create(
            name="Test Combination 3",
            servings=4,
            waste_score=15.0,
            seasonality_months=json.dumps([7, 8, 9]),
            last_calculated=timezone.now(),
            cache_valid=True,
            is_top_performer=True,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient2.id])
        )
        self.combination3.recipes.add(self.recipe2, self.recipe3)
        
        # Create an invalid combination
        self.invalid_combination = Combination.objects.create(
            name="Invalid Combination",
            servings=2,
            waste_score=40.0,
            seasonality_months=json.dumps([10, 11, 12]),
            last_calculated=timezone.now(),
            cache_valid=False,
            is_top_performer=False,
            dependent_ingredients=json.dumps([self.ingredient1.id, self.ingredient2.id])
        )
        self.invalid_combination.recipes.add(self.recipe1, self.recipe2)

    def test_get_combinations(self):
        """Test getting all combinations."""
        url = reverse('get_combinations')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 4)  # All combinations including invalid ones

    def test_get_combination(self):
        """Test getting a specific combination."""
        url = reverse('get_combination', args=[self.combination1.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['name'], "Test Combination 1")
        self.assertEqual(response.data['waste_score'], 25.5)

    def test_get_combination_not_found(self):
        """Test getting a non-existent combination."""
        url = reverse('get_combination', args=[999])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.data['error'], "Combination not found")
 
    @patch('poireaux_app.api.views.combination_views.generate_optimal_combinations')
    def test_generate_combinations_with_cache(self, mock_generate):
        """Test generating combinations with caching."""
        # Set up the URL and parameters
        url = reverse('generate_combinations')
        data = {
            'servings': 2,
            'limit': 10,
            'save_results': False
        }
        
        # Make the request
        response = self.client.post(url, data, format='json')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that generate_optimal_combinations was not called (used cache)
        mock_generate.assert_not_called()
        
        # Check that we got the expected number of combinations
        self.assertEqual(len(response.data), 2)  # Only valid combinations with servings=2

    @patch('poireaux_app.api.views.combination_views.generate_optimal_combinations')
    def test_generate_combinations_force_recalculate(self, mock_generate):
        """Test generating combinations with force_recalculate."""
        # Mock the generate_optimal_combinations function
        mock_generate.return_value = [
            {
                'recipes': [self.recipe1, self.recipe2],
                'waste_score': 20.0,
                'overlapping_months': [1, 2, 3],
                'servings': 2,
                'ingredient_waste': {}
            }
        ]
        
        # Set up the URL and parameters
        url = reverse('generate_combinations')
        data = {
            'servings': 2,
            'limit': 10,
            'save_results': False,
            'force_recalculate': True
        }
        
        # Make the request
        response = self.client.post(url, data, format='json')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that generate_optimal_combinations was called
        mock_generate.assert_called_once_with(
            servings=2,
            season_month=None,
            limit=10,
            offset=0,
            recipe_filter=None
        )

    @patch('poireaux_app.api.views.combination_views.generate_optimal_combinations')
    def test_generate_combinations_with_recipe_filter(self, mock_generate):
        """Test generating combinations with a recipe filter."""
        # Mock the generate_optimal_combinations function
        mock_generate.return_value = [
            {
                'recipes': [self.recipe1, self.recipe2],
                'waste_score': 20.0,
                'overlapping_months': [1, 2, 3],
                'servings': 2,
                'ingredient_waste': {}
            }
        ]
        
        # Set up the URL and parameters
        url = reverse('generate_combinations')
        data = {
            'servings': 2,
            'limit': 10,
            'save_results': False,
            'recipe_filter': [self.recipe1.id, self.recipe2.id, self.recipe3.id]
        }
        
        # Make the request
        response = self.client.post(url, data, format='json')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that generate_optimal_combinations was called with the recipe filter
        mock_generate.assert_called_once_with(
            servings=2,
            season_month=None,
            limit=10,
            offset=0,
            recipe_filter=[self.recipe1.id, self.recipe2.id, self.recipe3.id]
        )

    def test_generate_combinations_invalid_recipe_filter(self):
        """Test generating combinations with an invalid recipe filter."""
        # Set up the URL and parameters
        url = reverse('generate_combinations')
        data = {
            'servings': 2,
            'limit': 10,
            'save_results': False,
            'recipe_filter': [self.recipe1.id, self.recipe2.id]  # Only 2 recipes, need at least 3
        }
        
        # Make the request
        response = self.client.post(url, data, format='json')
        
        # Check that the response is an error
        self.assertEqual(response.status_code, 400)
        self.assertIn("At least 3 recipes are required", response.data['error'])

    @patch('poireaux_app.api.views.combination_views.calculate_waste_score')
    def test_calculate_waste_score_view(self, mock_calculate_waste):
        """Test calculating waste score for a set of recipes."""
        # Mock the calculate_waste_score function
        mock_calculate_waste.return_value = (20.0, {
            f"{self.ingredient1.id}_g": {
                "ingredient": self.ingredient1,
                "quantity": 350,
                "unit": "g",
                "buy_amount": 500,
                "waste": 150,
                "waste_percentage": 30.0
            }
        })
        
        # Set up the URL and parameters
        url = reverse('calculate_waste_score')
        params = {
            'recipe_ids[]': [self.recipe1.id, self.recipe2.id, self.recipe3.id],
            'servings': 2
        }
        
        # Make the request
        response = self.client.get(url, params)
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that calculate_waste_score was called with the correct parameters
        mock_calculate_waste.assert_called_once()
        
        # Check the response data
        self.assertEqual(response.data['waste_score'], 20.0)
        self.assertEqual(response.data['servings'], 2)
        self.assertEqual(len(response.data['recipe_ids']), 3)
        self.assertIn('ingredient_waste', response.data)

    def test_calculate_waste_score_view_invalid_recipe_count(self):
        """Test calculating waste score with an invalid number of recipes."""
        # Set up the URL and parameters
        url = reverse('calculate_waste_score')
        params = {
            'recipe_ids[]': [self.recipe1.id, self.recipe2.id],  # Only 2 recipes, need exactly 3
            'servings': 2
        }
        
        # Make the request
        response = self.client.get(url, params)
        
        # Check that the response is an error
        self.assertEqual(response.status_code, 400)
        self.assertIn("Exactly 3 recipes are required", response.data['error'])

    @patch('poireaux_app.api.views.combination_views.recalculate_combination')
    def test_trigger_recalculation_specific_combinations(self, mock_recalculate):
        """Test triggering recalculation for specific combinations."""
        # Mock the recalculate_combination function
        mock_recalculate.return_value = True
        
        # Set up the URL and parameters
        url = reverse('trigger_recalculation')
        data = {
            'combination_ids': [self.combination1.id, self.combination2.id]
        }
        
        # Make the request
        response = self.client.post(url, data, format='json')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that recalculate_combination was called for each combination
        self.assertEqual(mock_recalculate.call_count, 2)
        mock_recalculate.assert_any_call(self.combination1.id)
        mock_recalculate.assert_any_call(self.combination2.id)
        
        # Check the response data
        self.assertEqual(response.data['recalculated_count'], 2)

    @patch('poireaux_app.api.views.combination_views.mark_top_performers_for_recalculation')
    @patch('poireaux_app.api.views.combination_views.recalculate_marked_combinations')
    def test_trigger_recalculation_top_performers(self, mock_recalculate_marked, mock_mark_top):
        """Test triggering recalculation for top performers."""
        # Mock the functions
        mock_mark_top.return_value = 2
        mock_recalculate_marked.return_value = 2
        
        # Set up the URL and parameters
        url = reverse('trigger_recalculation')
        data = {
            'all_top_performers': True,
            'servings': 2
        }
        
        # Make the request
        response = self.client.post(url, data, format='json')
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check that the functions were called with the correct parameters
        mock_mark_top.assert_called_once_with(2)
        mock_recalculate_marked.assert_called_once_with(limit=100)
        
        # Check the response data
        self.assertEqual(response.data['recalculated_count'], 2)

    def test_get_calculation_status(self):
        """Test getting the calculation status."""
        # Set up the URL
        url = reverse('get_calculation_status')
        
        # Make the request
        response = self.client.get(url)
        
        # Check that the response is successful
        self.assertEqual(response.status_code, 200)
        
        # Check the response data
        self.assertEqual(response.data['total_combinations'], 4)
        self.assertEqual(response.data['valid_combinations'], 3)
        self.assertEqual(response.data['invalid_combinations'], 1)
        self.assertEqual(response.data['top_performers'], 2)
        self.assertEqual(response.data['invalid_top_performers'], 0)
        
        # Check the serving stats
        self.assertIn('serving_stats', response.data)
        self.assertIn('2', response.data['serving_stats'])
        self.assertIn('4', response.data['serving_stats'])
        
        # Check the stats for servings=2
        serving_2_stats = response.data['serving_stats']['2']
        self.assertEqual(serving_2_stats['total'], 3)
        self.assertEqual(serving_2_stats['valid'], 2)
        self.assertEqual(serving_2_stats['invalid'], 1)
        self.assertEqual(serving_2_stats['top_performers'], 1)
        self.assertEqual(serving_2_stats['invalid_top_performers'], 0)