import React from 'react';
import '../styles/CardData.css';

// This function might be needed here if displaying overall pantry/shopping lists
const aggregateIngredients = (ingredients) => {
  if (!Array.isArray(ingredients)) {
    return [];
  }
  const aggregated = ingredients.reduce((acc, ingredient) => {
    const key = `${ingredient.name}-${ingredient.unit}`;
    if (!acc[key]) {
      acc[key] = { ...ingredient, quantity: 0 };
    }
    acc[key].quantity += parseFloat(ingredient.quantity) || 0;
    return acc;
  }, {});
  return Object.values(aggregated).map(ing => ({
    ...ing,
    quantity: parseFloat(ing.quantity.toFixed(2))
  }));
};

const CardData = ({ combination }) => {
  // Logic for overall pantry and shopping list
  // This uses combination.detailed_ingredients which should contain all ingredients for the combo
  const detailedCombinationIngredients = Array.isArray(combination?.detailed_ingredients)
    ? combination.detailed_ingredients
    : [];
  const PANTRY_ITEM_TYPES = ['spice', 'oil', 'preserve', 'grain']; // Consider making this configurable or a constant
  const pantryIngredientsRaw = detailedCombinationIngredients.filter(ing => PANTRY_ITEM_TYPES.includes(ing.type));
  const shoppingListIngredientsRaw = detailedCombinationIngredients.filter(ing => !PANTRY_ITEM_TYPES.includes(ing.type));
  const aggregatedPantryIngredients = aggregateIngredients(pantryIngredientsRaw);
  const aggregatedShoppingListIngredients = aggregateIngredients(shoppingListIngredientsRaw);

  return (
    <div className="card-data-lists-wrapper">
      {/* Optional: Display overall pantry/shopping lists for the entire combination */}
      {aggregatedPantryIngredients.length > 0 && (
        <details className="ingredients-section pantry-items" style={{ marginTop: '15px' }}>
          <summary>Overall Pantry Items for Combination ({aggregatedPantryIngredients.length})</summary>
          <ul>
            {aggregatedPantryIngredients.map((ing, index) => (
              <li key={`combo-pantry-${ing.name}-${index}`}>{ing.quantity}{ing.unit} {ing.name}</li>
            ))}
          </ul>
        </details>
      )}
      {aggregatedShoppingListIngredients.length > 0 && (
        <details className="ingredients-section shopping-list-items" style={{ marginTop: '15px' }}>
          <summary>Overall Shopping List for Combination ({aggregatedShoppingListIngredients.length})</summary>
          <ul>
            {aggregatedShoppingListIngredients.map((ing, index) => (
              <li key={`combo-shop-${ing.name}-${index}`}>{ing.quantity}{ing.unit} {ing.name}</li>
            ))}
          </ul>
        </details>
      )}
    </div>
  );
};

export default CardData;