import React from 'react';

const EditInput = ({
  ingredient,
  field,
  editValues,
  handleInputChange,
  typeOptions,
  measurementOptions
}) => {
  const key = `${ingredient.id}-${field}`;

  if (field === 'type') {
    return (
      <select
        value={editValues[key] || ''}
        onChange={(e) => handleInputChange(ingredient.id, field, e.target.value)}
        autoFocus
      >
        {typeOptions.filter(t => t !== 'all').map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    );
  } else if (field === 'divisible_by_measurement') {
    return (
      <select
        value={editValues[key] || ''}
        onChange={(e) => handleInputChange(ingredient.id, field, e.target.value)}
        autoFocus
      >
        {measurementOptions.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    );
  } else if (field === 'needs_calculating') {
    return (
      <select
        value={editValues[key] === undefined ? ingredient.needs_calculating : editValues[key]}
        onChange={(e) => handleInputChange(ingredient.id, field, e.target.value === 'true')}
        autoFocus
      >
        <option value="true">Yes</option>
        <option value="false">No</option>
      </select>
    );
  } else {
    return (
      <input
        type="text"
        value={editValues[key] || ''}
        onChange={(e) => handleInputChange(ingredient.id, field, e.target.value)}
        autoFocus
      />
    );
  }
};

export default EditInput;