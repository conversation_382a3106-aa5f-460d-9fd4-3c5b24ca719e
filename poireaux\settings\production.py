# poireaux/settings/production.py

from .base import * # noqa F403
import os

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# SECRET_KEY should be set via an environment variable in production.
# The value in base.py is a fallback, but it's better to ensure it's always set.
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')
if not SECRET_KEY:
    raise ValueError("No DJANGO_SECRET_KEY set for production environment")

# ALLOWED_HOSTS should be set via an environment variable in production.
# Example: 'yourdomain.com,www.yourdomain.com'
allowed_hosts_env = os.environ.get('DJANGO_ALLOWED_HOSTS')
if not allowed_hosts_env:
    raise ValueError("No DJANGO_ALLOWED_HOSTS set for production environment")
ALLOWED_HOSTS = [host.strip() for host in allowed_hosts_env.split(',')]


# Database configuration for production (e.g., Supabase or other cloud provider)
# Ensure these environment variables are set in your production environment.
DB_NAME = os.environ.get('DB_NAME')
DB_USER = os.environ.get('DB_USER')
DB_PASSWORD = os.environ.get('DB_PASSWORD')
DB_HOST = os.environ.get('DB_HOST')
DB_PORT = os.environ.get('DB_PORT', '5432') # Default to 5432 if not set

if not all([DB_NAME, DB_USER, DB_PASSWORD, DB_HOST]):
    raise ValueError("One or more database environment variables (DB_NAME, DB_USER, DB_PASSWORD, DB_HOST) are not set for production.")

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': DB_NAME,
        'USER': DB_USER,
        'PASSWORD': DB_PASSWORD,
        'HOST': DB_HOST,
        'PORT': DB_PORT,
        'CONN_MAX_AGE': 300,  # Keep connections alive for 5 minutes (reduced)
        'OPTIONS': {
            'connect_timeout': 10,
            'application_name': 'poireaux_production',
        },
    }
}

# Email backend for production (e.g., SendGrid, Mailgun, AWS SES)
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_HOST = os.environ.get('EMAIL_HOST')
# EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
# EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True') == 'True'
# EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
# EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
# DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', 'webmaster@localhost')
# SERVER_EMAIL = os.environ.get('SERVER_EMAIL', 'root@localhost') # For error reports

# Static files (CSS, JavaScript, Images)
# Ensure 'whitenoise' is in MIDDLEWARE for serving static files in production if not using a CDN
# MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware') # Insert after SecurityMiddleware
# STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Security settings for production
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Cloud Run handles SSL termination, so disable Django's SSL redirect
SECURE_SSL_REDIRECT = False

# Enable HSTS since we're using HTTPS externally
SECURE_HSTS_SECONDS = 31536000 # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True

# Enable secure cookies since Cloud Run serves over HTTPS externally
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Tell Django to trust the X-Forwarded-Proto header from Cloud Run
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# CORS settings for production
# Ensure that the production frontend URL is explicitly allowed.
CORS_ALLOWED_ORIGINS = [
    "https://poireaux-d79c7.web.app", # Explicitly allow your Firebase Hosting frontend
]
# If you also use the FRONTEND_URL environment variable, you might merge them:
production_frontend_url = os.environ.get('FRONTEND_URL')
if production_frontend_url:
    CORS_ALLOWED_ORIGINS.append(production_frontend_url.rstrip('/'))

CORS_ALLOW_CREDENTIALS = True # Set to True if your frontend needs to send cookies or authorization headers

# Logging configuration for production
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO', # Or 'WARNING' / 'ERROR'
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        # Add other handlers like 'mail_admins' or file logging as needed
        # 'mail_admins': {
        #     'level': 'ERROR',
        #     'class': 'django.utils.log.AdminEmailHandler',
        #     'formatter': 'verbose',
        # },
        # 'file': {
        #     'level': 'INFO',
        #     'class': 'logging.handlers.RotatingFileHandler',
        #     'filename': '/var/log/django/poireaux.log', # Ensure this path is writable
        #     'maxBytes': 1024*1024*5, # 5 MB
        #     'backupCount': 5,
        #     'formatter': 'verbose',
        # },
    },
    'root': {
        'handlers': ['console'], # Add 'file' or other handlers here
        'level': 'INFO', # Default logging level for all loggers
    },
    'loggers': {
        'django': {
            'handlers': ['console'], # Add 'file' or other handlers here
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': False, # Don't pass to root logger if handled here
        },
        'django.request': { # Specific logger for request errors
            'handlers': ['console'], # Add 'mail_admins' for critical request errors
            'level': 'ERROR',
            'propagate': False,
        },
    },
}

# Caching configuration - temporarily disabled to fix startup issues
# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
#         'LOCATION': 'unique-snowflake',
#         'TIMEOUT': 300,  # 5 minutes default timeout
#         'OPTIONS': {
#             'MAX_ENTRIES': 1000,
#             'CULL_FREQUENCY': 3,
#         }
#     }
# }

# Any other production-specific settings can go here.
# For example, Celery configuration, etc.
