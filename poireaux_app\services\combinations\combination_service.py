"""
This module provides services for generating, filtering, and scoring recipe combinations.
"""
import itertools
import json
import traceback
from decimal import Decimal
from typing import Any, Dict, List, Optional, Set, Tuple

from django.db import IntegrityError
from django.utils import timezone

from poireaux_app.models import Combination, Recipe, RecipeIngredient
from poireaux_app.utils.combination.combination_crud_utils import \
    get_recipe_months

# Define pantry item types (consistent with frontend)
PANTRY_ITEM_TYPES = ['spice', 'oil', 'preserve', 'grain']
N_FOR_POOL = 30  # Number of top individual combinations to consider for triplets
N_FOR_PANTRY_FILTER = 15 # Number of top combinations to consider after pantry score filter
NUM_COMBINATIONS_IN_SUPER_SET = 3  # We are looking for a super set of 3 combinations
NUM_RECIPES_PER_COMBINATION = 3  # Each combination has 3 recipes
TOTAL_RECIPES_IN_SUPER_SET = NUM_COMBINATIONS_IN_SUPER_SET * NUM_RECIPES_PER_COMBINATION

DIET_TYPES = ['vegan', 'vegetarian', 'fish', 'meat']

def _calculate_triplet_recipe_uniqueness_score(
    triplet_combo_data: List[Dict],
    expected_total_recipes: int
) -> int:
    """
    Calculates a uniqueness score for a set of three combinations (a triplet).
    A lower score indicates more unique recipes (0 means all recipes are unique).
    """
    all_recipe_ids_in_triplet = set()
    for combo_data in triplet_combo_data:
        for recipe in combo_data['recipes']:
            all_recipe_ids_in_triplet.add(recipe.id)

    # Score is the difference between expected total recipes and unique recipes.
    # A smaller difference means more unique recipes across the triplet.
    return expected_total_recipes - len(all_recipe_ids_in_triplet)


def _get_pantry_score_for_triplet(
    triplet_combo_data: List[Dict],
    pantry_types: List[str],
) -> Tuple[int, float]:
    """
    Calculates the pantry score for a set of three combinations (a triplet).
    A lower pantry score (total count of pantry items, including duplicates) is better.
    Also returns the sum of individual waste scores for tie-breaking.
    """
    # Calculate total pantry ingredient count
    total_pantry_items = 0

    for combo_data in triplet_combo_data:
        for recipe in combo_data['recipes']:
            for ri in recipe.recipe_ingredients.all():
                if (hasattr(ri, 'ingredient') and ri.ingredient and
                        ri.ingredient.type in pantry_types):
                    total_pantry_items += 1

    sum_individual_waste_scores = sum(combo_d['score']
                                    for combo_d in triplet_combo_data)

    return total_pantry_items, sum_individual_waste_scores


def _get_pantry_score_for_single_combination(
    combo_data: Dict,
    pantry_types: List[str],
) -> int:
    """
    Calculates the pantry score for a single combination.
    A lower pantry score (total count of pantry items, including duplicates) is better.
    """
    total_pantry_items = 0
    for recipe in combo_data['recipes']:
        for ri in recipe.recipe_ingredients.all():
            if (hasattr(ri, 'ingredient') and ri.ingredient and
                    ri.ingredient.type in pantry_types):
                total_pantry_items += 1
    return total_pantry_items


def _find_best_triplet_from_pool(
    top_n_pool: List[Dict],
    pantry_item_types: List[str],
    num_combinations_in_triplet: int,
    expected_total_recipes: int
) -> Optional[List[Dict]]:
    """
    Finds the best triplet of combinations from the top_n_pool.
    Prioritizes uniqueness, then pantry score, then individual waste scores.
    """
    if len(top_n_pool) < num_combinations_in_triplet:
        return None

    best_triplet_data = None
    min_uniqueness_score = float('inf')
    min_pantry_score = float('inf')  # Changed from Decimal as it's now an int score
    min_pantry_triplet_waste_score_sum = float('inf')

    for current_triplet_candidate_data in itertools.combinations(
            top_n_pool, num_combinations_in_triplet):
        current_triplet_list = list(current_triplet_candidate_data)

        current_uniqueness_score = _calculate_triplet_recipe_uniqueness_score(
            current_triplet_list, expected_total_recipes
        )
        current_pantry_quantity, current_sum_waste_scores = _get_pantry_score_for_triplet(
            current_triplet_list, pantry_item_types
        )
        
        # Prioritize uniqueness score first
        if current_uniqueness_score < min_uniqueness_score:
            min_uniqueness_score = current_uniqueness_score
            min_pantry_score = current_pantry_quantity
            min_pantry_triplet_waste_score_sum = current_sum_waste_scores
            best_triplet_data = current_triplet_list
        # If uniqueness scores are equal, prioritize pantry score
        elif current_uniqueness_score == min_uniqueness_score:
            if current_pantry_quantity < min_pantry_score:
                min_pantry_score = current_pantry_quantity
                min_pantry_triplet_waste_score_sum = current_sum_waste_scores
                best_triplet_data = current_triplet_list
            # If both uniqueness and pantry scores are equal, use waste score sum as tie-breaker
            elif current_pantry_quantity == min_pantry_score:
                if current_sum_waste_scores < min_pantry_triplet_waste_score_sum:
                    min_pantry_triplet_waste_score_sum = current_sum_waste_scores
                    best_triplet_data = current_triplet_list

    if best_triplet_data:
        # Print only for the final chosen triplet summary
        print(f"--- Best Triplet Found: Uniqueness Score={min_uniqueness_score}, Pantry Score (Total Items)={min_pantry_score}, Individual Waste Scores Sum={min_pantry_triplet_waste_score_sum:.2f} ---")
        print("--- Triplet Combinations Details ---")
        for i, combo_data in enumerate(best_triplet_data):
            combo_name = " + ".join(sorted([recipe.name for recipe in combo_data['recipes']]))
            single_combo_pantry_score = _get_pantry_score_for_single_combination(
                combo_data, pantry_item_types
            )
            single_combo_waste_score = combo_data['score']
            print(f"  Combination {i+1}: '{combo_name}', Pantry Items={single_combo_pantry_score}, Waste Score={single_combo_waste_score:.2f}")
    return best_triplet_data


def generate_recipe_combinations(
    recipes: List[Recipe]
) -> List[List[Recipe]]:
    """
    Generate all possible combinations of exactly 3 recipes.
    """
    all_combinations = []

    for combo in itertools.combinations(recipes, 3):
        all_combinations.append(list(combo))

    return all_combinations


def check_recipe_divisibility(recipe: Recipe) -> bool:
    """
    Check if a recipe can be divided properly for the given number of servings.
    """
    return recipe.divisible


def filter_by_divisibility(combinations: List[List[Recipe]]) -> List[List[Recipe]]:
    """
    Filter combinations based on whether all recipes can be properly divided.
    """
    divisible_combinations = []

    for combo in combinations:
        if all(check_recipe_divisibility(recipe) for recipe in combo):
            divisible_combinations.append(combo)

    return divisible_combinations


def check_seasonality_overlap(
    combo: List[Recipe],
    min_overlapping_months: int = 1
) -> Tuple[bool, Set[int]]:
    """
    Check if recipes in a combination have overlapping seasonality.
    """
    overlapping_months = set(range(1, 13))

    for recipe in combo:
        recipe_months = get_recipe_months(recipe)
        overlapping_months.intersection_update(recipe_months)

    has_overlap = len(overlapping_months) >= min_overlapping_months

    return has_overlap, overlapping_months


def filter_by_seasonality(
    combinations: List[List[Recipe]],
    month: int = None,
    min_overlapping_months: int = 1
) -> List[Tuple[List[Recipe], Set[int]]]:
    """
    Filter combinations based on seasonality overlap.
    """
    seasonal_combinations = []

    for combo in combinations:
        has_overlap, overlapping_months = check_seasonality_overlap(
            combo, min_overlapping_months)

        if month is not None and month not in overlapping_months:
            continue

        if has_overlap:
            seasonal_combinations.append((combo, overlapping_months))

    return seasonal_combinations


def _find_optimal_package_size(needed_quantity: Decimal, package_sizes: List[int]) -> Dict[str, Any]:
    """
    Find the package size that results in the least waste percentage.

    Args:
        needed_quantity: The amount of ingredient needed
        package_sizes: List of available package sizes

    Returns:
        Dict containing optimal package_size, units_to_buy, bought_amount, waste, and waste_percentage
    """
    if not package_sizes:
        return None

    best_option = None
    min_waste_percentage = float('inf')

    for package_size in package_sizes:
        if package_size <= 0:
            continue

        package_size_decimal = Decimal(package_size)
        units_to_buy = (needed_quantity / package_size_decimal).quantize(Decimal('0.01'))
        units_to_buy = units_to_buy.quantize(Decimal('1'), rounding='ROUND_UP')

        bought_amount = units_to_buy * package_size_decimal
        waste = bought_amount - needed_quantity
        waste_percentage = (waste / bought_amount * 100).quantize(
            Decimal('0.01')) if bought_amount > 0 else Decimal('0')

        if waste_percentage < min_waste_percentage:
            min_waste_percentage = waste_percentage
            best_option = {
                'package_size': package_size_decimal,
                'units_to_buy': units_to_buy,
                'bought_amount': bought_amount,
                'waste': waste,
                'waste_percentage': waste_percentage
            }

    return best_option


def calculate_ingredient_waste(
    combo: List[Recipe],
    servings: int
) -> Dict[str, Dict[str, Any]]:
    """
    Calculate waste for each ingredient in a combination.
    For ingredients with multiple package sizes, selects the size with minimum waste.
    """
    ingredient_totals = {}

    for recipe in combo:
        multiplier = Decimal(servings) / Decimal(recipe.servings)

        for recipe_ingredient in recipe.recipe_ingredients.all():
            ingredient = recipe_ingredient.ingredient

            if not ingredient.needs_calculating:
                continue

            quantity = recipe_ingredient.quantity * multiplier
            unit = recipe_ingredient.unit

            key = f"{ingredient.id}_{unit}"

            if key not in ingredient_totals:
                ingredient_totals[key] = {
                    'ingredient': ingredient,
                    'quantity': quantity,
                    'unit': unit,
                    'waste': 0,
                    'buy_amount': 0
                }
            else:
                ingredient_totals[key]['quantity'] += quantity

    for key, data in ingredient_totals.items():
        ingredient = data['ingredient']
        needed_quantity = data['quantity']

        # Handle both legacy integer format and new JSON array format
        package_sizes = []
        if isinstance(ingredient.bought_by_amount, list):
            # New format: JSON array of package sizes
            package_sizes = [size for size in ingredient.bought_by_amount if size > 0]
        elif isinstance(ingredient.bought_by_amount, (int, float)) and ingredient.bought_by_amount > 0:
            # Legacy format: single integer/float value
            package_sizes = [int(ingredient.bought_by_amount)]

        if not package_sizes:
            continue

        # Find the optimal package size (least waste)
        optimal_option = _find_optimal_package_size(needed_quantity, package_sizes)

        if optimal_option:
            ingredient_totals[key]['waste'] = optimal_option['waste']
            ingredient_totals[key]['buy_amount'] = optimal_option['bought_amount']
            ingredient_totals[key]['waste_percentage'] = optimal_option['waste_percentage']
            ingredient_totals[key]['optimal_package_size'] = optimal_option['package_size']

    return ingredient_totals


def calculate_waste_score(
    combo: List[Recipe],
    servings: int
) -> Tuple[float, Dict[str, Dict[str, Any]]]:
    """
    Calculate waste score for a combination of recipes.
    """
    ingredient_waste_details = calculate_ingredient_waste(combo, servings)

    waste_percentages = []
    for data in ingredient_waste_details.values():
        if 'waste_percentage' in data:
            waste_percentages.append(data['waste_percentage'])

    if not waste_percentages:
        waste_score = 0.0
    else:
        waste_score = float(sum(waste_percentages))

    return waste_score, ingredient_waste_details




def filter_by_category_uniqueness(
    combinations_with_months: List[Tuple[List[Recipe], Set[int]]]
) -> List[Tuple[List[Recipe], Set[int]]]:
    """
    Filters out combinations that contain recipes with duplicate categories.
    """
    filtered_combinations = []
    for combo, months in combinations_with_months:
        categories_in_combo = {
            recipe.category for recipe in combo if hasattr(
                recipe, 'category') and recipe.category}

        if len(categories_in_combo) == len(combo):
            filtered_combinations.append((combo, months))

    return filtered_combinations




def _filter_by_dietary_restrictions(
    combinations_with_months: List[Tuple[List[Recipe], Set[int]]],
    diet_type: str
) -> List[Tuple[List[Recipe], Set[int]]]:
    """
    Filters combinations based on dietary restrictions.
    For 'fish' and 'meat' diets, ensures no more than one recipe of that type.
    """
    filtered_combinations = []
    for combo, months in combinations_with_months:
        if diet_type == 'fish':
            fish_count = sum(1 for recipe in combo if recipe.diet == 'fish')
            meat_count = sum(1 for recipe in combo if recipe.diet == 'meat')
            if fish_count == 1 and meat_count == 0:
                filtered_combinations.append((combo, months))
        elif diet_type == 'meat':
            meat_count = sum(1 for recipe in combo if recipe.diet == 'meat')
            fish_count = sum(1 for recipe in combo if recipe.diet == 'fish')
            if meat_count == 1 and fish_count == 0:
                filtered_combinations.append((combo, months))
        else:  # For 'vegan' and 'vegetarian', no specific meat/fish limit
            filtered_combinations.append((combo, months))
    return filtered_combinations




def _format_and_save_combinations(
    top_combinations_data: List[Dict],
    servings: int,
    diet: str,  # New parameter for diet
    generation_timestamp: Optional[timezone.datetime] = None
) -> List[int]:
    """
    Formats and saves the top N combinations to the database.
    """
    saved_combination_ids = []
    for combo_data in top_combinations_data:
        try:
            combo_recipes = combo_data['recipes']
            overlapping_months = combo_data['months']
            waste_score = combo_data['score']
            ingredient_waste_details = combo_data['details']

            dependent_ingredients = [int(key.split('_')[0])
                                     for key in ingredient_waste_details
                                     if key.split('_')[0].isdigit()]

            unique_ingredient_ids = set()
            for recipe in combo_recipes:
                # pylint: disable=protected-access
                if hasattr(recipe, '_prefetched_objects_cache') and \
                   'recipe_ingredients' in recipe._prefetched_objects_cache:
                    for ri in recipe.recipe_ingredients.all():
                        unique_ingredient_ids.add(ri.ingredient_id)
                else:
                    recipe_ingredients = RecipeIngredient.objects.filter(  # pylint: disable=no-member
                        recipe_id=recipe.id)
                    for ri in recipe_ingredients:
                        unique_ingredient_ids.add(ri.ingredient_id)
            ingredients_count = len(unique_ingredient_ids)

            formatted_waste = {}
            for key, data in ingredient_waste_details.items():
                if 'ingredient' not in data:
                    continue
                ingredient = data['ingredient']
                formatted_waste[key] = {
                    'ingredient_id': ingredient.id,
                    'ingredient_name': ingredient.name,
                    'needed_quantity': float(data['quantity']),
                    'unit': data['unit'],
                    'buy_amount': float(data['buy_amount']),
                    'waste': float(data['waste']),
                    'waste_percentage': float(data.get('waste_percentage', 0)),
                    'needs_calculating': ingredient.needs_calculating
                }

            detailed_ingredients_data = []
            recipe_specific_ingredients_data = {}
            for recipe in combo_recipes:
                multiplier = Decimal(servings) / Decimal(recipe.servings)
                recipe_id_str = str(recipe.id)
                recipe_specific_ingredients_data[recipe_id_str] = []

                recipe_ingredients = []
                # pylint: disable=protected-access
                if hasattr(recipe, '_prefetched_objects_cache') and \
                   'recipe_ingredients' in recipe._prefetched_objects_cache:
                    recipe_ingredients_qs = recipe.recipe_ingredients.all()
                    current_recipe_ingredients = []
                    for ri in recipe_ingredients_qs:
                        if hasattr(ri, 'ingredient') and ri.ingredient:
                            current_recipe_ingredients.append(ri)
                        else:
                            ri_with_ingredient = RecipeIngredient.objects.select_related(  # pylint: disable=no-member
                                'ingredient').get(id=ri.id)
                            current_recipe_ingredients.append(
                                ri_with_ingredient)
                    recipe_ingredients = current_recipe_ingredients
                else:
                    recipe_ingredients = RecipeIngredient.objects.filter(  # pylint: disable=no-member
                        recipe_id=recipe.id).select_related('ingredient')

                for ri in recipe_ingredients:
                    if not hasattr(ri, 'ingredient') or not ri.ingredient:
                        continue

                    adjusted_quantity = ri.quantity * multiplier
                    ingredient_data = {
                        'id': ri.ingredient.id,
                        'name': ri.ingredient.name,
                        'quantity': float(adjusted_quantity.quantize(Decimal('0.01'))),
                        'unit': ri.unit,
                        'type': ri.ingredient.type
                    }
                    detailed_ingredients_data.append(ingredient_data)
                    recipe_specific_ingredients_data[recipe_id_str].append(
                        ingredient_data)

            recipe_names = sorted([recipe.name for recipe in combo_recipes])
            name = " + ".join(recipe_names)

            combination = Combination.objects.create(  # pylint: disable=no-member
                name=name,
                waste_score=float(waste_score),
                servings=int(servings),
                is_divisible=True,
                seasonality_months=json.dumps(
                    sorted(list(overlapping_months))),
                ingredient_waste=formatted_waste,
                dependent_ingredients=sorted(list(set(dependent_ingredients))),
                ingredients_length=ingredients_count,
                detailed_ingredients=detailed_ingredients_data,
                recipe_specific_ingredients=recipe_specific_ingredients_data,
                last_calculated=timezone.now(),
                cache_valid=True,
                generation_timestamp=generation_timestamp,
                diet=diet # Save the diet type
            )
            combination.recipes.set(combo_recipes)
            saved_combination_ids.append(combination.id)

        except IntegrityError as e:
            print(f"Database integrity error: {e}")
            traceback.print_exc()
        except (TypeError, KeyError, ValueError):
            traceback.print_exc()

    return saved_combination_ids


def _get_latest_combination_fingerprints(servings: int, diet: str) -> Tuple[Set[Tuple[int, ...]], Optional[timezone.datetime]]:
    """
    Retrieves the fingerprints and generation timestamp of the most recently generated combinations
    for a specific serving size and diet type.
    """
    # pylint: disable=no-member
    latest_combination = Combination.objects.filter(
        servings=servings, diet=diet, generation_timestamp__isnull=False
    ).order_by('-generation_timestamp').first()

    if not latest_combination:
        return set(), None

    latest_timestamp = latest_combination.generation_timestamp
    # pylint: disable=no-member
    recent_combinations = Combination.objects.filter(
        servings=servings, diet=diet, generation_timestamp=latest_timestamp
    ).prefetch_related('recipes')

    fingerprints = set()
    for combo in recent_combinations:
        recipe_ids = tuple(sorted(recipe.id for recipe in combo.recipes.all()))
        fingerprints.add(recipe_ids)

    return fingerprints, latest_timestamp


def _filter_recipes_by_diet(
    recipes_qs: Any,
    servings: int,
    diet_type: str
) -> List[Recipe]:
    """
    Filters the initial list of recipes based on the current diet type.
    """
    if diet_type == 'vegan':
        filtered_recipes = recipes_qs.filter(diet='vegan')
    elif diet_type == 'vegetarian':
        filtered_recipes = recipes_qs.filter(diet__in=['vegan', 'vegetarian'])
    elif diet_type == 'fish':
        filtered_recipes = recipes_qs.filter(diet__in=['vegan', 'vegetarian', 'fish'])
    elif diet_type == 'meat':
        filtered_recipes = recipes_qs.filter(diet__in=['vegan', 'vegetarian', 'meat'])
    else:
        # Default to all if diet_type is not recognized or 'all'
        filtered_recipes = recipes_qs

    return list(
        filtered_recipes.filter(name__contains=f"({servings})")
        .prefetch_related('recipe_ingredients__ingredient')
    )


def generate_unique_combinations(
    servings: int = 1,
    season_month: int = None,
    limit: int = 10,
    offset: int = 0,
    recipe_filter: Optional[List[int]] = None
) -> List[Combination]:
    """
    Generates new combinations for each diet type, unique from previous generations.
    """
    all_saved_combination_ids = []

    for diet_type in DIET_TYPES:
        print(f"\n--- Combinations: Generating for Diet Type: {diet_type.upper()} ---")

        # Fetch previous fingerprints for this specific diet and serving size
        previous_fingerprints, latest_generation_timestamp = _get_latest_combination_fingerprints(servings, diet_type)
        print(f"--- Combinations: Found {len(previous_fingerprints)} previous fingerprints for {diet_type} (Timestamp: {latest_generation_timestamp}). ---")

        # Get all recipes initially, then filter by diet
        if recipe_filter:
            recipes_qs = Recipe.objects.filter(id__in=recipe_filter) # pylint: disable=no-member
        else:
            recipes_qs = Recipe.objects.all() # pylint: disable=no-member

        filtered_recipes = _filter_recipes_by_diet(recipes_qs, servings, diet_type)

        if len(filtered_recipes) < NUM_RECIPES_PER_COMBINATION:
            print(f"--- Combinations: Not enough {diet_type} recipes ({len(filtered_recipes)}) to form combinations. Need at least {NUM_RECIPES_PER_COMBINATION}. ---")
            continue

        initial_combinations = generate_recipe_combinations(filtered_recipes)
        print(f"--- Combinations: Generated {len(initial_combinations)} initial {diet_type} combinations. ---")

        divisible_combinations = filter_by_divisibility(initial_combinations)
        print(f"--- Combinations: {len(divisible_combinations)} {diet_type} after divisibility filter. ---")

        seasonal_combinations = filter_by_seasonality(
            divisible_combinations, season_month)
        print(f"--- Combinations: {len(seasonal_combinations)} {diet_type} after seasonality filter. ---")

        # Apply dietary restrictions filter
        dietary_restricted_combinations = _filter_by_dietary_restrictions(seasonal_combinations, diet_type)
        print(f"--- Combinations: {len(dietary_restricted_combinations)} {diet_type} after dietary restrictions filter. ---")

        category_unique_combinations = filter_by_category_uniqueness(
            dietary_restricted_combinations)
        print(f"--- Combinations: {len(category_unique_combinations)} {diet_type} after category uniqueness filter. ---")

        # Filter out combinations that were in the previous generation for this diet
        new_candidate_combinations = []
        for combo_recipes, months in category_unique_combinations:
            current_fingerprint = tuple(sorted(recipe.id for recipe in combo_recipes))
            if current_fingerprint not in previous_fingerprints:
                new_candidate_combinations.append((combo_recipes, months))
        print(f"--- Combinations: {len(new_candidate_combinations)} new unique candidate {diet_type} combinations after fingerprint filter. ---")

        scored_combinations = []
        for combo_recipes, overlapping_months in new_candidate_combinations:
            waste_score, ingredient_waste_details = calculate_waste_score(
                combo_recipes, servings)
            scored_combinations.append({
                'score': waste_score,
                'recipes': combo_recipes,
                'months': overlapping_months,
                'details': ingredient_waste_details,
                'servings': servings,
                'diet': diet_type  # Add diet type to combo data
            })

        scored_combinations.sort(key=lambda x: x['score'])
        top_n_waste_scores = scored_combinations[:N_FOR_POOL]
        print(f"--- Combinations: {len(top_n_waste_scores)} {diet_type} combinations in top_n_waste_scores pool. ---")

        # Calculate pantry score for each combination in the top_n_waste_scores pool
        for combo_data in top_n_waste_scores:
            combo_data['pantry_score'] = _get_pantry_score_for_single_combination(
                combo_data, PANTRY_ITEM_TYPES
            )

        # Sort by pantry score, then waste score for tie-breaking
        top_n_waste_scores.sort(key=lambda x: (x['pantry_score'], x['score']))
        top_n_pool = top_n_waste_scores[:N_FOR_PANTRY_FILTER]
        print(f"--- Combinations: {len(top_n_pool)} {diet_type} combinations in final top_n_pool after pantry filter. ---")
        
        print("--- Top 5 Combinations (Waste & Pantry Scores) ---")
        for i, combo_data in enumerate(top_n_pool[:5]):
            print(f"  {i+1}. Waste Score={combo_data['score']:.2f}, Pantry Score={combo_data['pantry_score']}")

        final_combinations_to_save_data = []
        print(f"--- Combinations: Attempting to find best triplet from top {len(top_n_pool)} {diet_type} combinations. ---")
        if len(top_n_pool) >= NUM_COMBINATIONS_IN_SUPER_SET:
            best_super_set_data = _find_best_triplet_from_pool(
                top_n_pool,
                PANTRY_ITEM_TYPES,
                NUM_COMBINATIONS_IN_SUPER_SET,
                TOTAL_RECIPES_IN_SUPER_SET
            )
            if best_super_set_data:
                final_combinations_to_save_data = best_super_set_data
                print(f"--- Combinations: Found best triplet with {len(final_combinations_to_save_data)} {diet_type} combinations. ---")
            else:
                print(f"--- Combinations: No best triplet found for {diet_type}. ---")
        else:
            print(f"--- Combinations: Not enough combinations in top_n_pool ({len(top_n_pool)}) to form a triplet for {diet_type}. ---")

        if final_combinations_to_save_data:
            # Delete old combinations for this diet and serving size before saving new ones
            if latest_generation_timestamp:
                # pylint: disable=no-member
                Combination.objects.filter(
                    servings=servings,
                    diet=diet_type,
                    generation_timestamp=latest_generation_timestamp
                ).delete()
                print(f"--- Combinations: Cleared old combinations for {diet_type} (Timestamp: {latest_generation_timestamp}). ---")

            print(f"--- Combinations: Saving {len(final_combinations_to_save_data)} final {diet_type} combinations. ---")
            saved_ids = _format_and_save_combinations(
                final_combinations_to_save_data, servings, diet_type, timezone.now())
            all_saved_combination_ids.extend(saved_ids)
        else:
            print(f"--- Combinations: No final {diet_type} combinations to save. ---")

    if not all_saved_combination_ids:
        return []

    # pylint: disable=no-member
    final_queryset = Combination.objects.filter(id__in=all_saved_combination_ids)\
        .prefetch_related('recipes__recipe_ingredients__ingredient')\
        .order_by('waste_score')

    paginated_results = list(final_queryset[offset: offset + limit])
    return paginated_results
