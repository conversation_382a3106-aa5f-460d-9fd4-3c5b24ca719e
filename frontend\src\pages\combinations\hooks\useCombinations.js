import { useEffect, useCallback, useRef } from 'react';
import { useCombinationAPI } from './useCombinations/useCombinationAPI';
import { useCombinationState } from './useCombinations/useCombinationState';
import { useCombinationFilters } from '../components/CombinationControlPanel/hooks/useCombinationFilters';

export const useCombinations = () => {
  // Track if a fetch is in progress to prevent infinite loops
  const fetchInProgressRef = useRef(false);

  // Initialize individual hooks
  const {
    combinations, setCombinations,
    loading, setLoading,
    error, setError,
    totalCount, setTotalCount,
  } = useCombinationState();



  const {
    servings, month, diet, // Add diet to destructuring
    setServings: setServingsFilter, // Renamed to avoid conflict if we expose a different setServings
    setMonth: setMonthFilter,     // Renamed
    setDiet: setDietFilter, // New setter for diet
  } = useCombinationFilters(); // Default initial values are handled within useCombinationFilters


  const {
    fetchCombinationsAPI,
    findAndSaveCombinationsAPI,
    saveCombinationAPI,
  } = useCombinationAPI();
 
  // Function to fetch EXISTING combinations
  const fetchCombinations = useCallback(async (currentServings, currentMonth, currentDiet) => {
    if (fetchInProgressRef.current) {
      console.log("Fetch skipped: Already loading.");
      return;
    }

    // Prevent rapid successive calls
    const requestKey = `${currentServings}-${currentMonth}-${currentDiet}`;
    if (fetchCombinations.lastRequest === requestKey && Date.now() - fetchCombinations.lastRequestTime < 1000) {
      console.log("Fetch skipped: Too soon after last request.");
      return;
    }
    fetchCombinations.lastRequest = requestKey;
    fetchCombinations.lastRequestTime = Date.now();

    fetchInProgressRef.current = true;
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Loading combinations data...');

      const data = await fetchCombinationsAPI(currentServings, currentMonth, currentDiet);
      setCombinations(data);
      setTotalCount(data.length);

      console.log('✅ Data loaded:', data.length, 'combinations');

    } catch (err) {
      console.error('❌ Error fetching combinations:', err);
      const errorMessage = err.message || 'Unknown error during fetch';
      setError(`Failed to load combinations: ${errorMessage}`);
      setCombinations([]);
      setTotalCount(0);
    } finally {
      fetchInProgressRef.current = false;
      setLoading(false);
    }
  }, [fetchCombinationsAPI, setCombinations, setError, setLoading, setTotalCount]);

  // Fetch combinations when servings, month, or diet changes
  useEffect(() => {
    fetchCombinations(servings, month, diet);
  }, [servings, month, diet, fetchCombinations]);

  // Function to find, calculate, and save combinations
  const findAndSaveCombinations = useCallback(async (currentServings, currentMonth) => {
    setLoading(true);
    setError(null);
    console.log(`Finding and saving combinations for servings: ${currentServings}, month: ${currentMonth}`);
    try {
      // Page is reset to 1 for new searches. Ordering removed.
      const combinationsArray = await findAndSaveCombinationsAPI(currentServings, currentMonth); // Removed ordering and page 1
      setCombinations(combinationsArray);
      setTotalCount(combinationsArray.length);
      // No next/prev page URL or current page reset as pagination is removed

      if (combinationsArray.length === 0) {
        setError('No combinations found for the selected criteria. Ensure enough recipes exist.');
        setTotalCount(0);
      } else {
        setError(null); // Clear error if combinations were found
      }
    } catch (err) {
      console.error('Error finding/saving combinations:', err);
      const errorMessage = err.message || 'Unknown error during find/save';
      if (errorMessage.includes('at least 3 recipes') || errorMessage.includes('No combinations')) {
        setError('Unable to generate combinations: You need at least 3 recipes matching the criteria.');
      } else {
        setError(`Failed to process combinations: ${errorMessage}`);
      }
      setCombinations([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [findAndSaveCombinationsAPI, setLoading, setError, setCombinations, setTotalCount]); // Removed ordering dependency and pagination setters

  // Function to retry a failed operation
  const retryOperation = async (operation) => {
    setError(null);
    switch (operation) {
      case 'fetch':
        await fetchCombinations(servings); // Removed ordering and currentPage
        break;
      case 'findAndSave':
        await findAndSaveCombinations(servings, month); // Retries with current filter state
        break;
      default:
        console.error('Unknown retry operation:', operation);
    }
  };
  
  // Handlers that also reset pagination
  const handleSetServings = (newServings) => {
    setServingsFilter(newServings);
  };

  const handleSetMonth = (newMonth) => {
    setMonthFilter(newMonth);
  };

  const handleSetDiet = (newDiet) => {
    setDietFilter(newDiet);
  };
 
  const saveCombination = useCallback(async (combinationId) => {
    try {
      setLoading(true); // Or a more specific saving loading state
      setError(null);
      await saveCombinationAPI(combinationId);
      alert('Combination saved successfully!'); // Provide user feedback
      // Optionally, update the local state to reflect the saved status
      // e.g., setCombinations(prev => prev.map(c => c.id === combinationId ? { ...c, isSaved: true } : c));
    } catch (err) {
      console.error('Error saving combination:', err);
      setError(`Failed to save combination: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [saveCombinationAPI, setLoading, setError]);

  return {
    // State
    combinations,
    loading,
    error,
    totalCount,
    // Filters & Sort
    servings,
    month,
    diet, // Expose diet state
    setServings: handleSetServings,
    setMonth: handleSetMonth,
    setDiet: handleSetDiet, // Expose diet setter
    // Actions
    fetchCombinations: () => fetchCombinations(servings, month, diet), // For explicit refetch
    findAndSaveCombinations: () => findAndSaveCombinations(servings, month), // Use current filter state
    retryOperation,
    saveCombination,
  };
};
