import React, { useState } from 'react';
import '../styles/Tooltip.css';

/**
 * Simple tooltip component
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Element that triggers the tooltip
 * @param {string} props.content - Tooltip content
 * @param {string} props.position - Tooltip position (top, right, bottom, left)
 */
const Tooltip = ({ children, content, position = 'top' }) => {
  const [visible, setVisible] = useState(false);

  return (
    <div 
      className="tooltip-container"
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
    >
      {children}
      {visible && (
        <div className={`tooltip-content ${position}`}>
          {content}
        </div>
      )}
    </div>
  );
};

export default Tooltip; 