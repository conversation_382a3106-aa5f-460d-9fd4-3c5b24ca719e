import { useState } from 'react';

/**
 * Custom hook to manage serving size state
 * @returns {Object} serving size state and setters
 */
const useServingSize = (defaultSize = 1) => {
  const [activeServingSize, setActiveServingSize] = useState(defaultSize);
  
  // Simple filter function based on recipe name
  const filterRecipesBySize = (recipes) => {
    if (!recipes) return [];
    
    return recipes.filter(recipe => {
      if (activeServingSize === "original") {
        return recipe.name.includes("(original)");
      }
      
      // Check for (1), (2), (3), (4) pattern at the end of name
      return recipe.name.endsWith(`(${activeServingSize})`);
    });
  };
  
  return { activeServingSize, setActiveServingSize, filterRecipesBySize };
};

export default useServingSize; 