/* Combination Scroller Styles */
.combination-scroller-container {
  position: relative;
  width: 100%;
  height: 100%; /* Take available space instead of full viewport */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}



.combination-scroller {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  touch-action: pan-x pan-y;
}

.combination-scroller-track {
  display: flex;
  height: 100%;
  width: 100%;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.combination-scroller-item {
  flex: 0 0 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  scroll-snap-align: start;
  padding: 20px;
  box-sizing: border-box;
}

/* Desktop: Each item should take up the container width */
@media (min-width: 769px) {
  .combination-scroller-item {
    flex: 0 0 auto;
    /* Each item should be exactly the width of the scroller container */
    width: 100%;
    min-width: 100%;
  }

  .combination-scroller {
    overflow: hidden; /* Ensure only one item is visible at a time */
  }

  /* The track width will be set dynamically via JavaScript */
  .combination-scroller-track {
    /* Width set by JS: combinations.length * 100% */
    min-width: 100%;
  }
}

.combination-scroller-item .combination-card {
  width: 350px; /* Fixed width instead of max-width */
  height: 85%; /* Fixed height relative to container */
  max-height: none; /* Remove max-height constraint */
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* Desktop Navigation Controls */
.desktop-nav-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 10;
  pointer-events: none;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.nav-button {
  pointer-events: all;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #8FBC8F;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: #8FBC8F;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-button:hover:not(:disabled) {
  background: #8FBC8F;
  color: white;
  transform: scale(1.1);
}

.nav-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none;
}

.nav-button-prev {
  left: 20px;
}

.nav-button-next {
  right: 20px;
}

/* Minimalist Combination Counter */
.combination-counter {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85em;
  color: #333;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 4px;
}

.combination-counter-label {
  font-weight: 500;
  color: #666;
}

.combination-counter-value {
  font-weight: 600;
  color: #8FBC8F;
}

/* Empty State */
.combination-scroller-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #666;
  font-size: 18px;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .combination-scroller-container {
    height: 100%;
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .combination-scroller {
    touch-action: pan-y;
    height: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }

  .combination-scroller-track {
    flex-direction: column;
    width: 100%;
    height: 100%;
  }

  .combination-scroller-item {
    flex: 0 0 100%;
    width: 100%;
    padding: 10px 8px; /* Reduced padding */
    height: 100%;
    overflow: hidden;
  }

  .combination-scroller-item .combination-card {
    max-width: 95%;
    max-height: 100%;
    width: 100%;
    margin: 0 auto;
    height: fit-content;
  }

  /* Disable hover effects on mobile */
  .combination-scroller-item .combination-card:hover {
    transform: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  /* Hide all navigation controls on mobile - finger scrolling only */
  .desktop-nav-controls {
    display: none;
  }



  /* Hide counter on mobile */
  .combination-counter {
    display: none;
  }
}

/* Very small mobile devices */
@media (max-width: 480px) {
  .combination-scroller-item {
    padding: 5px;
  }

  .combination-scroller-item .combination-card {
    max-width: 98%;
    max-height: 90vh;
  }

  .combination-indicators {
    bottom: 15px;
  }

  .combination-counter {
    top: 15px;
    right: 15px;
  }
}

/* Active item highlighting */
.combination-scroller-item.active .combination-card {
  transform: scale(1.02);
  transition: transform 0.3s ease;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
}

/* Loading state */
.combination-scroller-item .combination-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Smooth transitions for all interactive elements */
.nav-button,
.indicator,
.combination-counter {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced hover effects */
.combination-scroller-item .combination-card:hover {
  transform: scale(1.05);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* Prevent text selection during swipe */
.combination-scroller-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Ensure proper stacking context */
.combination-scroller-container * {
  box-sizing: border-box;
}

/* Performance optimizations */
.combination-scroller-track {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.combination-scroller-item {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Focus styles for accessibility */
.nav-button:focus,
.indicator:focus {
  outline: 2px solid #8FBC8F;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .combination-scroller-track,
  .combination-scroller-item .combination-card,
  .nav-button,
  .indicator {
    transition: none;
  }

  .combination-scroller-item.active .combination-card {
    transform: none;
  }
}
