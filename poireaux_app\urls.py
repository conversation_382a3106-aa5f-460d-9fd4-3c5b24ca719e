from django.urls import path
from django.http import JsonResponse
from django.utils import timezone
from .api import *  # This imports all the renamed functions from the api/__init__.py
from .api.views import combination_views, saved_combination_views
from .api.views.recipe_views import process_new_recipe_backend

def health_check(_):
    """Simple health check endpoint to keep Cloud Run instance warm."""
    return JsonResponse({'status': 'healthy', 'timestamp': timezone.now().isoformat()})

def test_combinations(_):
    """Simple test endpoint to check if we can access combinations."""
    try:
        from .models import Combination
        count = Combination.objects.count()
        return JsonResponse({'status': 'success', 'combination_count': count})
    except Exception as e:
        return JsonResponse({'status': 'error', 'error': str(e)}, status=500)


urlpatterns = [
    # Health check endpoint
    path('health/', health_check, name='health-check'),
    path('test-combinations/', test_combinations, name='test-combinations'),

    path('ingredients/get/', getIngredients, name='getIngredients'),
    path('ingredients/create/', createIngredient, name='createIngredient'),
    # path('ingredients/delete/<int:pk>/', deleteIngredient, name='delete-ingredient'),
    path('ingredients/update/<int:pk>/', updateIngredient, name='update-ingredient'),

    path('recipes/get/', getRecipes, name='getRecipes'),
    path('recipes/create/', createRecipe, name='createRecipe'),
    path('recipes/delete/<int:pk>/', deleteRecipe, name='delete-recipe'),
    path('recipes/update/<int:pk>/', updateRecipe, name='update-recipe'),
    path('recipe-ingredients/update/<int:pk>/', updateRecipeIngredient, name='update-recipe-ingredient'),
    # path('recipes/process/', processRecipeFully, name='process-recipe-fully'), # Old endpoint, commented out
    path('recipes/process-new/', process_new_recipe_backend, name='process-new-recipe-backend'), # New endpoint
    
    # Combination-related URLs
    path('combinations/get/', combination_views.CombinationListView.as_view(), name='combination-list'),
    # path('combinations/fast/', combination_views.CombinationFastListView.as_view(), name='combination-fast-list'),  # Temporarily disabled
    path('combinations/generate/', combination_views.generate_combinations, name='combination-generate'),
    # New saved combination URLs
    path('saved-combinations/', saved_combination_views.SavedCombinationListCreateView.as_view(), name='saved-combination-list-create'),
    path('saved-combinations/<int:pk>/', saved_combination_views.SavedCombinationRetrieveDestroyView.as_view(), name='saved-combination-retrieve-destroy'),
    
    # Recipe divisibility URLs
    path('recipes/divisibility/check-all/', check_all_recipes_divisibility, name='check-all-recipes-divisibility'),
    path('recipes/ingredients/round/', round_ingredient_quantities, name='round-ingredient-quantities'),
    path('recipes/divisibility/check/', check_all_recipes_divisibility, name='check-all-recipes-divisibility'),
    # Recipe seasonality URLs
    path('recipes/seasonality/calculate/', calculate_recipe_seasonality_api, name='calculate-recipe-seasonality'),
    path('recipes/seasonality/update-all/', update_all_recipes_seasonality, name='update-all-recipes-seasonality'),
    path('recipes/seasonality/<int:pk>/', get_recipe_seasonality, name='get-recipe-seasonality'),
    path('recipes/seasonality/batch/', get_batch_recipe_seasonality, name='get-batch-recipe-seasonality'),
    
    # Recipe servings generation URL
    path('recipes/generate-servings/', generateServings, name='generate-servings'),
]
