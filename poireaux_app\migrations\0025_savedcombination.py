# Generated by Django 5.1.7 on 2025-06-10 20:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('poireaux_app', '0024_remove_albertheijnproduct_bought_by_amounts_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SavedCombination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('combination', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='poireaux_app.combination')),
            ],
        ),
    ]
