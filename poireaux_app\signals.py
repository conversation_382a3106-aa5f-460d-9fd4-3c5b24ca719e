from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Recipe, Ingredient, RecipeIngredient, Combination
from .services.recipes.recipe_combination_service import (
    mark_combinations_invalid_for_recipe,
    mark_combinations_invalid_for_ingredient,
    update_top_performers
)

@receiver(post_save, sender=Recipe)
def recipe_saved_handler(sender, instance, created, **kwargs):
    """
    Signal handler for when a recipe is saved.
    Marks affected combinations as invalid when a recipe is updated.
    
    Args:
        sender: The model class (Recipe)
        instance: The actual recipe instance that was saved
        created: <PERSON><PERSON><PERSON> indicating if this is a new instance
    """
    if not created:  # Only for updates, not new recipes
        # Mark all combinations containing this recipe as invalid
        affected_count = mark_combinations_invalid_for_recipe(instance)
        if affected_count > 0:
            print(f"Marked {affected_count} combinations as invalid due to recipe update: {instance.name}")

@receiver(post_delete, sender=Recipe)
def recipe_deleted_handler(sender, instance, **kwargs):
    """
    Signal handler for when a recipe is deleted.
    Marks affected combinations as invalid when a recipe is deleted.
    
    Args:
        sender: The model class (Recipe)
        instance: The actual recipe instance that was deleted
    """
    # The ManyToMany relationship will automatically remove the recipe from combinations
    # But we need to mark those combinations as invalid
    affected_count = mark_combinations_invalid_for_recipe(instance)
    if affected_count > 0:
        print(f"Marked {affected_count} combinations as invalid due to recipe deletion: {instance.name}")

@receiver(post_save, sender=Ingredient)
def ingredient_saved_handler(sender, instance, created, **kwargs):
    """
    Signal handler for when an ingredient is saved.
    Marks affected combinations as invalid when an ingredient's packaging size is updated.
    
    Args:
        sender: The model class (Ingredient)
        instance: The actual ingredient instance that was saved
        created: Boolean indicating if this is a new instance
    """
    if not created:  # Only for updates, not new ingredients
        # Check if the packaging size fields have changed, ensuring update_fields is iterable
        update_fields = kwargs.get('update_fields') # Get the value, could be None
        # Only proceed if update_fields is not None and is iterable (like a list or set)
        if update_fields is not None and hasattr(update_fields, '__iter__'):
            if 'bought_by' in update_fields or 'bought_by_amount' in update_fields:
                # Mark all combinations dependent on this ingredient as invalid
                affected_count = mark_combinations_invalid_for_ingredient(instance)
                if affected_count > 0:
                    print(f"Marked {affected_count} combinations as invalid due to ingredient packaging update: {instance.name}")
        # If update_fields is None or not iterable, do nothing (maintains original logic)

@receiver(post_delete, sender=Ingredient)
def ingredient_deleted_handler(sender, instance, **kwargs):
    """
    Signal handler for when an ingredient is deleted.
    Marks affected combinations as invalid when an ingredient is deleted.
    
    Args:
        sender: The model class (Ingredient)
        instance: The actual ingredient instance that was deleted
    """
    # Mark all combinations dependent on this ingredient as invalid
    affected_count = mark_combinations_invalid_for_ingredient(instance)
    if affected_count > 0:
        print(f"Marked {affected_count} combinations as invalid due to ingredient deletion: {instance.name}")

@receiver(post_save, sender=RecipeIngredient)
def recipe_ingredient_saved_handler(sender, instance, created, **kwargs):
    """
    Signal handler for when a recipe ingredient is saved.
    Marks affected combinations as invalid when a recipe ingredient is updated.
    
    Args:
        sender: The model class (RecipeIngredient)
        instance: The actual recipe ingredient instance that was saved
        created: Boolean indicating if this is a new instance
    """
    # Mark all combinations containing this recipe as invalid
    affected_count = mark_combinations_invalid_for_recipe(instance.recipe)
    if affected_count > 0:
        print(f"Marked {affected_count} combinations as invalid due to recipe ingredient update: {instance.recipe.name} - {instance.ingredient.name}")

@receiver(post_delete, sender=RecipeIngredient)
def recipe_ingredient_deleted_handler(sender, instance, **kwargs):
    """
    Signal handler for when a recipe ingredient is deleted.
    Marks affected combinations as invalid when a recipe ingredient is deleted.
    
    Args:
        sender: The model class (RecipeIngredient)
        instance: The actual recipe ingredient instance that was deleted
    """
    # Mark all combinations containing this recipe as invalid
    affected_count = mark_combinations_invalid_for_recipe(instance.recipe)
    if affected_count > 0:
        print(f"Marked {affected_count} combinations as invalid due to recipe ingredient deletion: {instance.recipe.name} - {instance.ingredient.name}")

@receiver(post_save, sender=Combination)
def combination_saved_handler(sender, instance, created, **kwargs):
    """
    Signal handler for when a combination is saved.
    Updates the top performers list when a combination is saved.
    
    Args:
        sender: The model class (Combination)
        instance: The actual combination instance that was saved
        created: Boolean indicating if this is a new instance
    """
    # Only update top performers if the waste score has changed
    update_fields = kwargs.get('update_fields')
    if (update_fields is not None and 'waste_score' in update_fields) or created:
        # Update top performers asynchronously
        # In a real-world scenario, this would be a background task
        update_top_performers()