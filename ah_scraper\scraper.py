import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def scrape_ah_product_data_selenium(url):
    """
    Scrapes product data (name and price) from a given Albert Heijn URL using undetected_chromedriver.
    """
    product_data = {}
    
    # Setup Chrome options (optional, but can be useful)
    chrome_options = uc.ChromeOptions()
    # chrome_options.add_argument("--headless")  # Keep disabled for visual inspection initially
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--start-maximized") # Start maximized for better view
    chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

    driver = None  # Initialize driver to None for the finally block
    try:
        print("Initializing undetected_chromedriver...")
        # undetected_chromedriver typically manages the driver binary itself.
        driver = uc.Chrome(options=chrome_options)
        print("undetected_chromedriver initialized.")
    except Exception as e:
        print(f"Error initializing undetected_chromedriver: {e}")
        print("Ensure Chrome is installed. undetected_chromedriver usually handles the driver binary.")
        return {} # Return an empty dict on initialization failure

    try:
        print(f"Navigating to {url}...")
        driver.get(url)
        print("Page loaded. Waiting for elements...")

        # --- Placeholder for actual scraping logic using Selenium ---
        # This section needs to be implemented based on the HTML structure 
        # of Albert Heijn's product pages, identified after successfully loading the page.
        # WebDriverWait should be used to ensure elements are present before interacting.
        timeout = 20 # seconds
        
        # Attempt to handle cookie/privacy consent first
        try:
            print("Looking for cookie consent button...")
            # Try a few common selectors/texts for accept buttons
            possible_accept_selectors = [
                (By.XPATH, "//button[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'alles accepteren')]"),
                (By.XPATH, "//button[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'akkoord')]"),
                (By.XPATH, "//button[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepteer')]"),
                (By.ID, "accept-cookies"), # Common ID
                (By.CLASS_NAME, "cookie-accept"), # Common class
                (By.CSS_SELECTOR, "button[data-testid='accept-all-cookies-button']"), # Example test ID
                (By.CSS_SELECTOR, "button[title*='Accepteer']"), # Button with title containing 'Accepteer'
                (By.CSS_SELECTOR, "button[aria-label*='Accepteer']"), # Button with aria-label containing 'Accepteer'
            ]
            
            accept_button = None
            for by_type, selector_value in possible_accept_selectors:
                try:
                    # Using a shorter timeout for finding the cookie button as it should appear quickly
                    accept_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((by_type, selector_value))
                    )
                    if accept_button:
                        print(f"Found cookie consent button with: {by_type} {selector_value}")
                        break
                except:
                    # print(f"Selector {by_type} {selector_value} not found or not clickable.")
                    continue # Try next selector

            if accept_button:
                driver.execute_script("arguments[0].click();", accept_button) # JavaScript click as a fallback
                # accept_button.click()
                print("Clicked cookie consent button. Waiting for page to update...")
                time.sleep(7) # Increased wait time
                print(f"Current URL after cookie click attempt and wait: {driver.current_url}")
            else:
                print("No common cookie consent button found, or it's not clickable. Proceeding...")
        except Exception as e_cookie:
            print(f"Error handling cookie consent: {e_cookie}. Proceeding anyway...")

        WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "body")) # Wait for body to be present
        )
        print("Page body present.")

        # Extract product name
        try:
            # Try a more specific H1 for product titles on AH, or a data-testid
            name_element = WebDriverWait(driver, timeout).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "h1[class*='product-title'], h1[data-testid*='product-title'], [data-testid='product-card-title']"))
            )
            product_data['name'] = name_element.text.strip()
            print(f"Found name using H1/data-testid: {product_data['name']}")
        except Exception as e:
            print(f"Could not find product name using H1/data-testid: {e}. Trying title tag.")
            try:
                # Fallback to title tag
                title_text = driver.title
                if "bestellen | Albert Heijn" in title_text: # AH specific title pattern
                    product_data['name'] = title_text.split(" bestellen | Albert Heijn")[0].strip()
                    print(f"Found name using title tag: {product_data['name']}")
                elif "|" in title_text:
                    product_data['name'] = title_text.split("|")[0].strip()
                    print(f"Found name using title tag (generic split): {product_data['name']}")
                else:
                    product_data['name'] = title_text.strip()
                    print(f"Found name using title tag (full title): {product_data['name']}")
            except Exception as e_title:
                print(f"Could not find product name using title tag either: {e_title}")

        # Extract product price
        try:
            price_container_selectors = [
                "div[class*='price-sum_root']",
                "div[class*='product-price_root']",
                "span[data-testhook*='price']", # Common testhook for price
                "div[data-testhook*='price']",
                "div[class*='price-display_root']",
                "div[class*='price-now']", # Often used for current price
                "span[class*='price-now']",
                "div[class*='product-card-hero-price_root']" # Another AH pattern
            ]
            price_text = None
            for selector in price_container_selectors:
                try:
                    price_element_container = WebDriverWait(driver, 10).until(
                        EC.visibility_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    # Get all text within the container, including children, then clean it.
                    raw_price_text = price_element_container.text.strip()
                    
                    if raw_price_text and any(char.isdigit() for char in raw_price_text):
                        print(f"Found raw price text '{raw_price_text}' using selector '{selector}'")
                        # More aggressive cleaning for price
                        cleaned_price = ''.join(filter(lambda x: x.isdigit() or x == ',' or x == '.', raw_price_text.split('\n')[0]))
                        cleaned_price = cleaned_price.replace(',', '.')
                        if cleaned_price.count('.') > 1: # e.g. 1.2.9 -> 1.29 or 12.9
                            parts = cleaned_price.split('.')
                            cleaned_price = parts[0] + '.' + ''.join(parts[1:])
                        
                        # Ensure it's a valid number-like string before assigning
                        if cleaned_price and any(char.isdigit() for char in cleaned_price):
                            product_data['price'] = cleaned_price
                            print(f"Processed price: {product_data['price']}")
                            break # Found a plausible price
                        else:
                            print(f"Price text '{raw_price_text}' found but not in expected format after cleaning with selector '{selector}'.")
                    else:
                        # print(f"Selector '{selector}' found element, but no digits in text: '{raw_price_text}'")
                        pass

                except Exception as e_price_sel:
                    # print(f"Selector '{selector}' not found or error: {e_price_sel}")
                    continue
            
            if not product_data.get('price'):
                 print("Could not find product price with common selectors.")

        except Exception as e:
            print(f"Could not find product price (general error): {e}")

        # Check for Bonus
        product_data['is_bonus'] = False # Default to False
        product_data['bonus_description'] = None
        try:
            # Look for common bonus indicators. These are guesses and need verification.
            bonus_selectors = [
                "div[class*='bonus-label']",
                "span[class*='bonus-sticker']",
                "div[data-testhook*='bonus']",
                "span[data-testhook*='bonus']",
                "div[class*='product-promo']",
                "p[class*='bonus-text']"
            ]
            bonus_found = False
            for selector in bonus_selectors:
                try:
                    bonus_element = driver.find_element(By.CSS_SELECTOR, selector) # Use find_element as it might not exist
                    if bonus_element.is_displayed():
                        product_data['is_bonus'] = True
                        product_data['bonus_description'] = bonus_element.text.strip() if bonus_element.text.strip() else "Bonus indicated"
                        print(f"Bonus found using selector '{selector}': {product_data['bonus_description']}")
                        bonus_found = True
                        break
                except:
                    continue # Selector not found or element not visible
            
            if not bonus_found:
                print("No bonus indicators found with common selectors.")
                
        except Exception as e_bonus:
            print(f"Error checking for bonus: {e_bonus}")


        if not product_data.get('name') and not product_data.get('price'): # Check if both are still missing
            print("No specific product data extracted. Placeholder selectors need to be updated.")
            print("Page source snippet (first 2000 chars for review if needed):")
            # Give the page a moment to fully render if dynamic content is still loading
            time.sleep(5) 
            print(driver.page_source[:2000])
        
    except Exception as e:
        print(f"An error occurred during scraping: {e}")
        if driver:
            print("Page source snippet at time of error (first 2000 chars):")
            print(driver.page_source[:2000])
    finally:
        if 'driver' in locals() and driver:
            print("Quitting WebDriver...")
            driver.quit()
            print("WebDriver quit.")
            
    return product_data # product_data is initialized as {} and modified, so it's always a dict

if __name__ == '__main__':
    product_urls = [
        "https://www.ah.nl/producten/product/wi4164/ah-courgette",
        "https://www.ah.nl/producten/product/wi4117/ah-paprika-rood"
    ]
    
    all_scraped_data = []

    for i, target_url in enumerate(product_urls):
        print(f"\n--- Scraping product {i+1}/{len(product_urls)}: {target_url} ---")
        scraped_data = scrape_ah_product_data_selenium(target_url)
        if scraped_data and isinstance(scraped_data, dict): # Ensure it's a dictionary
            scraped_data['url'] = target_url # Add URL to the scraped data
            all_scraped_data.append(scraped_data)
            print(f"--- Successfully scraped: {scraped_data.get('name', 'N/A')} ---")
        else:
            print(f"--- Failed to scrape or no data for: {target_url} ---")
        
        if i < len(product_urls) - 1: # Add delay if not the last product
            print("Waiting for 3 seconds before next request...")
            time.sleep(3)

    if all_scraped_data:
        print("\n\n--- Summary of Scraped Products ---")
        for data in all_scraped_data:
            print(f"- Name: {data.get('name', 'Not found')}")
            print(f"- Price: {data.get('price', 'Not found')}")
            print(f"- URL: {data.get('url', 'N/A')}")
            print(f"- Bonus: {'Yes' if data.get('is_bonus') else 'No'}")
            if data.get('is_bonus') and data.get('bonus_description'):
                print(f"- Bonus Description: {data.get('bonus_description')}")
            print("---------------------------------")
    else:
        print("\nNo data was successfully scraped for any product.")
