from rest_framework import generics, status
from rest_framework.response import Response
from ...models import SavedCombination, Combination
from ..serializers.combination_serializers import SavedCombinationSerializer, CombinationSerializer
import logging

logger = logging.getLogger(__name__)

class SavedCombinationListCreateView(generics.ListCreateAPIView):
    """
    API view to list all saved combinations or create a new one.
    """
    queryset = SavedCombination.objects.all().order_by('-created_at')
    serializer_class = SavedCombinationSerializer

    def get_queryset(self):
        try:
            queryset = super().get_queryset()
            # Eagerly load related Combination and its recipes to prevent N+1 queries
            queryset = queryset.select_related('combination').prefetch_related('combination__recipes')
            return queryset
        except Exception as e:
            logger.error(f"Error fetching saved combinations: {e}", exc_info=True)
            # Optionally, return an empty queryset or re-raise a custom exception
            # For debugging, we might want to return an empty queryset to allow the frontend to load
            return SavedCombination.objects.none()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
        except Exception as e:
            logger.error(f"Error creating saved combination: {e}", exc_info=True)
            return Response({"detail": "Internal Server Error during creation."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class SavedCombinationRetrieveDestroyView(generics.RetrieveDestroyAPIView):
    """
    API view to retrieve or delete a saved combination.
    """
    queryset = SavedCombination.objects.all()
    serializer_class = SavedCombinationSerializer
