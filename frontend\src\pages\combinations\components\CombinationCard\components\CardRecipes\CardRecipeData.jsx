import React from 'react';
import './styles/CardRecipeData.css';

// This component displays specific ingredients for the active recipe.
// It was moved from the original CardData.jsx

const CardRecipeData = ({ activeRecipeName, activeRecipeSpecificIngredients }) => {
  return (
    <div className="recipe-specific-ingredients-container">
      {/* Image rendering removed */}
      {activeRecipeSpecificIngredients && activeRecipeSpecificIngredients.length > 0 ? (
        <div className="ingredients-section">
          <h4>Ingredients for: {activeRecipeName || "Current Recipe"}</h4>
          <ul>
            {activeRecipeSpecificIngredients.map((ing, index) => (
              <li key={`specific-${ing.name}-${ing.unit}-${index}`}>
                {ing.quantity}{ing.unit} {ing.name}
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <p className="no-ingredients">
          No specific ingredients listed for {activeRecipeName || "this recipe"}.
        </p>
      )}
    </div>
  );
};

export default CardRecipeData;
