import { useState, useCallback } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../../../config/api';

export const useSavedCombinations = () => {
    const [savedCombinations, setSavedCombinations] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchSavedCombinations = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            console.log('Fetching saved combinations from:', `${API_BASE_URL}/saved-combinations/`);
            const response = await axios.get(`${API_BASE_URL}/saved-combinations/`, {
                timeout: 10000, // 10 second timeout
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                }
            });
            console.log('Saved combinations response:', response.data);
            setSavedCombinations(response.data);
        } catch (err) {
            setError(err);
            console.error("Error fetching saved combinations:", err);
            console.error("Error details:", {
                message: err.message,
                status: err.response?.status,
                statusText: err.response?.statusText,
                data: err.response?.data
            });
        } finally {
            setLoading(false);
        }
    }, []);

    const deleteSavedCombination = useCallback(async (id) => {
        setLoading(true);
        setError(null);
        try {
            await axios.delete(`${API_BASE_URL}/saved-combinations/${id}/`);
            setSavedCombinations(prev => prev.filter(c => c.id !== id));
        } catch (err) {
            setError(err);
            console.error("Error deleting saved combination:", err);
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        savedCombinations,
        loading,
        error,
        fetchSavedCombinations,
        deleteSavedCombination,
    };
};
