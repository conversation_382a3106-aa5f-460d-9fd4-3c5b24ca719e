"""
API views for handling recipe combinations.
"""
import json
import traceback

from django.db import IntegrityError
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, generics
from rest_framework.decorators import api_view
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

from ...models import Combination
from ...services.combinations.combination_service import \
    generate_unique_combinations
from ..serializers import CombinationListSerializer, CombinationSerializer
# from ..serializers import CombinationMinimalSerializer  # Temporarily disabled


# Define a standard pagination class
class StandardResultsSetPagination(PageNumberPagination):
    """
    Standard pagination settings for combination lists.
    """
    page_size = 10  # Reduced default page size for faster loading
    page_size_query_param = 'page_size'
    max_page_size = 50  # Reduced maximum to prevent large responses


class CombinationListView(generics.ListAPIView):
    """
    API view to list combinations with pagination, filtering, and sorting.

    Supports filtering by:
    - servings: Exact match (e.g., ?servings=2)
    - diet: Filter by diet type (e.g., ?diet=vegan)

    Supports sorting by:
    - waste_score: Ascending (?ordering=waste_score) or Descending (?ordering=-waste_score)
    """
    serializer_class = CombinationListSerializer
    # pagination_class = StandardResultsSetPagination  # Temporarily disabled
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['servings', 'diet']
    ordering_fields = ['waste_score', 'ingredients_length']
    ordering = ['waste_score']

    def get_queryset(self):
        """
        Simple queryset - revert to basic functionality to fix 500 errors.
        """
        try:
            # Use the original simple queryset
            queryset = Combination.objects.prefetch_related(  # pylint: disable=no-member
                'recipes__recipe_ingredients__ingredient'
            ).all()

            return queryset

        except Exception as e:
            print(f"--- Backend ERROR in CombinationListView.get_queryset: {e} ---")
            traceback.print_exc()
            # Return empty queryset to prevent 500 error
            return Combination.objects.none()  # pylint: disable=no-member



    def list(self, request, *args, **kwargs):
        """
        Override list method to add error handling.
        """
        try:
            print(f"--- Backend: CombinationListView.list called with params: {request.query_params} ---")
            result = super().list(request, *args, **kwargs)
            print(f"--- Backend: CombinationListView.list successful ---")
            return result
        except Exception as e:
            error_message = f"Error fetching combinations: {e}"
            print(f"--- Backend ERROR in CombinationListView.list: {error_message} ---")
            print(f"--- Backend ERROR details: {type(e).__name__}: {str(e)} ---")
            traceback.print_exc()
            return Response({"error": error_message}, status=500)


# Keep the old view name in urls.py pointing to CombinationListView.as_view()
# Or update urls.py to use the new class name directly.

# Temporarily disabled fast view to fix startup issues
# class CombinationFastListView(generics.ListAPIView):
#     """
#     Ultra-fast API view for initial combination loading.
#     Returns minimal data for quick page load, then full data can be loaded separately.
#     """
#     serializer_class = CombinationMinimalSerializer
#     pagination_class = StandardResultsSetPagination
#     filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
#     filterset_fields = ['servings', 'diet']
#     ordering_fields = ['waste_score']
#     ordering = ['waste_score']
#
#     def get_queryset(self):
#         """
#         Optimized queryset for fastest possible loading.
#         """
#         try:
#             # Minimal query - only select what we need
#             queryset = Combination.objects.select_related().only(  # pylint: disable=no-member
#                 'id', 'name', 'waste_score', 'servings', 'diet'
#             ).prefetch_related('recipes')
#
#             # Apply filters early
#             servings = self.request.query_params.get('servings')
#             diet = self.request.query_params.get('diet')
#
#             if servings:
#                 queryset = queryset.filter(servings=servings)
#             if diet and diet != 'all':
#                 queryset = queryset.filter(diet=diet)
#
#             # Limit for performance
#             queryset = queryset[:50]  # Even smaller limit for fast endpoint
#
#             return queryset
#
#         except Exception as e:
#             print(f"--- Backend ERROR in CombinationFastListView.get_queryset: {e} ---")
#             traceback.print_exc()
#             return Combination.objects.none()  # pylint: disable=no-member

# --- Other views remain unchanged ---

@api_view(['POST'])
def generate_combinations(request):
    """
    Generate, calculate, and save optimal recipe combinations based on provided parameters.
    Always generates combinations of exactly 3 recipes.
    Performs full calculation and saves results directly.
    Returns a paginated list of saved Combination objects.
    """
    try:
        # Extract parameters from the request
        servings = int(request.data.get('servings', 1))
        season_month = request.data.get('season_month')
        limit = int(request.data.get('limit', 10))
        offset = int(request.data.get('offset', 0))
        recipe_filter = request.data.get('recipe_filter', None)

        # Convert season_month to int if provided
        if season_month is not None:
            try:
                season_month = int(season_month)
                print(f"--- Backend: Using season month: {season_month} ---")
            except (ValueError, TypeError):
                error_message = f"Invalid season_month value: {season_month}"
                print(f"--- Backend ERROR: {error_message} ---")
                return Response({"error": error_message}, status=400)

        # Validate recipe_filter if provided
        if recipe_filter:
            try:
                if isinstance(recipe_filter, str):
                    recipe_filter = json.loads(recipe_filter)
                print(f"--- Backend: Using recipe filter: {recipe_filter} ---")

                if len(recipe_filter) < 3:
                    error_msg = (
                        "At least 3 recipes are required but only "
                        f"{len(recipe_filter)} were provided."
                    )
                    print(f"--- Backend ERROR: {error_msg} ---")
                    return Response({"error": error_msg}, status=400)

            except json.JSONDecodeError:
                error_message = f"Invalid recipe_filter format: {recipe_filter}"
                print(f"--- Backend ERROR: {error_message} ---")
                return Response({"error": "Invalid recipe_filter format"}, status=400)

        # --- Caching and force_recalculate logic removed ---
        # --- The service layer will now handle generation, calculation, and saving ---
        print(
            "--- Backend: Calling service to generate, calculate, and save combinations "
            f"with parameters: servings={servings}, month={season_month}, "
            f"limit={limit}, offset={offset} ---"
        )
        if recipe_filter:
            print(f"--- Backend: Filtering to recipes: {recipe_filter} ---")

        saved_combinations = generate_unique_combinations(
            servings=servings,
            season_month=season_month,
            offset=offset,
            recipe_filter=recipe_filter
        )

        if not saved_combinations:
            error_msg = (
                "No combinations could be generated or saved. "
                "Ensure enough recipes are available and meet criteria."
            )
            print(f"--- Backend WARNING: {error_msg} ---")
            return Response({"error": error_msg}, status=404)

        print(f"--- Backend: Service returned {len(saved_combinations)} saved combinations ---")

        serializer = CombinationSerializer(saved_combinations, many=True)
        print("--- Backend: Saved combinations serialized successfully ---")
        return Response(serializer.data)

    except (ValueError, TypeError) as e:
        error_message = f"Invalid input parameter: {e}"
        print(f"--- Backend ERROR: {error_message} ---")
        traceback.print_exc()
        return Response({"error": error_message}, status=400)
    except (IntegrityError, KeyError, AttributeError) as e:
        error_message = f"Failed during combination generation/saving process: {e}"
        print(f"--- Backend ERROR: {error_message} ---")
        traceback.print_exc()
        return Response({"error": error_message}, status=500)
