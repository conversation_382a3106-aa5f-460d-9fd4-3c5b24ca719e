/* Styles for the main .recipe-card wrapper */
.recipe-card {
  display: flex;
  flex-direction: column;
  padding: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
}

/* Collapsed state - compact height */
.recipe-card.collapsed {
  height: fit-content;
  min-height: 300px; /* Smaller height when collapsed */
}

/* Expanded state - allow full expansion */
.recipe-card.expanded {
  height: fit-content; /* Size to content for full expansion */
  min-height: 450px; /* Minimum height when expanded */
}

/* Always visible section */
.recipe-card-always-visible {
  display: flex;
  flex-direction: column;
}

/* Expandable section */
.recipe-card-expandable {
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  animation: slideDown 0.3s ease-out;
}

/* Slide down animation for expanding content */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Expand indicator styling */
.expand-indicator {
  text-align: center;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 0.85rem;
  color: #666;
  border: 1px solid #e9ecef;
}

.expand-indicator:hover {
  background-color: #e9ecef;
}

/* Ensure proper spacing between card sections */
.recipe-card > * {
  margin-bottom: 10px;
}

.recipe-card > *:last-child {
  margin-bottom: 0;
}
