import { useSeasonality as useSharedSeasonality } from '../../../hooks/useSeasonality';

/**
 * Hook for handling seasonality in combinations
 * Uses the shared seasonality hook
 */
export const useSeasonality = () => {
  // Use the shared hook for base functionality
  const {
    formatMonth,
    safelyParseSeasonalMonths,
    MONTH_NAMES,
    MONTH_ABBREVIATIONS
  } = useSharedSeasonality();
  
  return {
    formatMonth,
    safelyParseSeasonalMonths,
    MONTH_NAMES,
    MONTH_ABBREVIATIONS
  };
};

export default useSeasonality; 