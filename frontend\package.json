{"name": "frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:8000", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^0.21.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "react-swipeable": "^7.0.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "start-host": "cross-env HOST=0.0.0.0 react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "customize-cra": "^1.0.0", "https-browserify": "^1.0.0", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}}