import { renderHook, act } from '@testing-library/react-hooks';
import axios from 'axios';
import { useCombinations } from '../useCombinations';

// Mock axios
jest.mock('axios');

describe('useCombinations hook', () => {
  // Mock data
  const mockCombinations = [
    {
      id: 1,
      name: 'Combination 1',
      recipes: [
        { id: 1, name: 'Recipe 1' },
        { id: 2, name: 'Recipe 2' },
        { id: 3, name: 'Recipe 3' }
      ],
      waste_score: 25.5,
      seasonality_months: [1, 2, 3],
      servings: 2,
      cache_valid: true,
      last_calculated: '2025-04-17T10:00:00Z',
      is_top_performer: true
    },
    {
      id: 2,
      name: 'Combination 2',
      recipes: [
        { id: 1, name: 'Recipe 1' },
        { id: 4, name: '<PERSON>cipe 4' },
        { id: 5, name: 'Recipe 5' }
      ],
      waste_score: 30.0,
      seasonality_months: [4, 5, 6],
      servings: 2,
      cache_valid: true,
      last_calculated: '2025-04-17T09:00:00Z',
      is_top_performer: false
    }
  ];

  const mockCalculationStatus = {
    total_combinations: 10,
    valid_combinations: 8,
    invalid_combinations: 2,
    top_performers: 3,
    invalid_top_performers: 1,
    serving_stats: {
      '2': {
        total: 6,
        valid: 5,
        invalid: 1,
        top_performers: 2,
        invalid_top_performers: 0
      },
      '4': {
        total: 4,
        valid: 3,
        invalid: 1,
        top_performers: 1,
        invalid_top_performers: 1
      }
    }
  };

  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should fetch combinations on mount', async () => {
    // Mock axios.get to return mock combinations
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Initially, loading should be true
    expect(result.current.loading).toBe(true);

    // Wait for the hook to update
    await waitForNextUpdate();

    // Check that axios.get was called with the correct URL
    expect(axios.get).toHaveBeenCalledWith('http://localhost:8000/api/combinations/get/');
    expect(axios.get).toHaveBeenCalledWith('http://localhost:8000/api/combinations/get-calculation-status/');

    // Check that the combinations were set correctly
    expect(result.current.combinations).toEqual(mockCombinations);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.calculationStatus).toEqual(mockCalculationStatus);
    expect(result.current.lastCalculated).toBe('2025-04-17T10:00:00Z');
  });

  test('should handle error when fetching combinations', async () => {
    // Mock axios.get to throw an error
    const errorMessage = 'Failed to fetch combinations';
    axios.get.mockRejectedValueOnce({ 
      message: errorMessage,
      response: { data: { error: errorMessage } }
    });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Wait for the hook to update
    await waitForNextUpdate();

    // Check that the error was set correctly
    expect(result.current.error).toBe(`Failed to load combinations: ${errorMessage}`);
    expect(result.current.combinations).toEqual([]);
    expect(result.current.loading).toBe(false);
  });

  test('should generate combinations', async () => {
    // Mock axios.get for initial fetch
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Mock axios.post for generate combinations
    const generatedCombinations = [...mockCombinations, {
      id: 3,
      name: 'Combination 3',
      recipes: [
        { id: 2, name: 'Recipe 2' },
        { id: 3, name: 'Recipe 3' },
        { id: 6, name: 'Recipe 6' }
      ],
      waste_score: 15.0,
      seasonality_months: [7, 8, 9],
      servings: 2,
      cache_valid: true,
      last_calculated: '2025-04-17T11:00:00Z',
      is_top_performer: true
    }];
    axios.post.mockResolvedValueOnce({ data: generatedCombinations });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Wait for the initial fetch to complete
    await waitForNextUpdate();

    // Generate combinations
    act(() => {
      result.current.setServings(2);
      result.current.setMonth(7);
    });
 
    act(() => {
      result.current.generateCombinations();
    });

    // Check that generating is true
    expect(result.current.generating).toBe(true);

    // Mock axios.post to resolve
    await waitForNextUpdate();

    // Check that axios.post was called with the correct parameters
    expect(axios.post).toHaveBeenCalledWith(
      'http://localhost:8000/api/combinations/generate/',
      {
        servings: 2,
        limit: 10,
        save_results: false,
        season_month: 7
      }
    );

    // Check that the combinations were updated
    expect(result.current.combinations).toEqual(generatedCombinations);
    expect(result.current.generating).toBe(false);
    expect(result.current.error).toBe(null);
  });

  test('should trigger background recalculation', async () => {
    // Mock axios.get for initial fetch
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Mock axios.post for trigger recalculation
    const recalculationResponse = {
      message: 'Recalculation triggered for 2 combinations',
      recalculated_count: 2
    };
    axios.post.mockResolvedValueOnce({ data: recalculationResponse });

    // Mock axios.get for status update
    const updatedStatus = {
      ...mockCalculationStatus,
      invalid_combinations: 0
    };
    axios.get.mockResolvedValueOnce({ data: updatedStatus });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Wait for the initial fetch to complete
    await waitForNextUpdate();

    // Trigger recalculation
    act(() => {
      result.current.triggerBackgroundRecalculation([1, 2]);
    });

    // Check that recalculating is true
    expect(result.current.recalculating).toBe(true);
    expect(result.current.pollingActive).toBe(true);

    // Wait for the recalculation to complete
    await waitForNextUpdate();

    // Check that axios.post was called with the correct parameters
    expect(axios.post).toHaveBeenCalledWith(
      'http://localhost:8000/api/combinations/trigger-recalculation/',
      {
        all_top_performers: false,
        servings: 1,
        combination_ids: [1, 2]
      }
    );

    // Check that axios.get was called to fetch the status
    expect(axios.get).toHaveBeenCalledWith('http://localhost:8000/api/combinations/get-calculation-status/');

    // Check that the status was updated
    expect(result.current.calculationStatus).toEqual(updatedStatus);
  });

  test('should save a combination', async () => {
    // Mock axios.get for initial fetch
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Mock axios.get for waste score calculation
    const wasteScoreResponse = {
      recipe_ids: [1, 2, 3],
      servings: 2,
      waste_score: 25.5,
      ingredient_waste: {}
    };
    axios.get.mockResolvedValueOnce({ data: wasteScoreResponse });

    // Mock axios.post for save combination
    axios.post.mockResolvedValueOnce({ data: mockCombinations });

    // Mock axios.get for refresh combinations
    axios.get.mockResolvedValueOnce({ data: mockCombinations });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Wait for the initial fetch to complete
    await waitForNextUpdate();

    // Save a combination
    act(() => {
      result.current.saveCombination(mockCombinations[0]);
    });

    // Wait for the save to complete
    await waitForNextUpdate();

    // Check that axios.get was called to calculate waste score
    expect(axios.get).toHaveBeenCalledWith(
      'http://localhost:8000/api/waste-score/calculate/',
      {
        params: {
          'recipe_ids': [1, 2, 3],
          'servings': 2
        },
        paramsSerializer: expect.any(Function)
      }
    );

    // Check that axios.post was called to save the combination
    expect(axios.post).toHaveBeenCalledWith(
      'http://localhost:8000/api/combinations/generate/',
      {
        servings: 2,
        limit: 1,
        save_results: true,
        recipe_filter: [1, 2, 3]
      }
    );

    // Check that axios.get was called to refresh combinations
    expect(axios.get).toHaveBeenCalledWith('http://localhost:8000/api/combinations/get/');
  });

  test('should apply filters and sorting', async () => {
    // Mock axios.get for initial fetch
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Wait for the initial fetch to complete
    await waitForNextUpdate();

    // Apply a filter
    act(() => {
      result.current.updateFilter({ minWasteScore: 30 });
    });

    // Check that the filter was applied
    expect(result.current.combinations).toEqual([mockCombinations[1]]);

    // Apply a different filter
    act(() => {
      result.current.updateFilter({ minWasteScore: 0, searchTerm: 'Recipe 4' });
    });

    // Check that the filter was applied
    expect(result.current.combinations).toEqual([mockCombinations[1]]);

    // Apply a sort
    act(() => {
      result.current.updateSort('waste_score');
    });

    // Check that the sort was applied (already sorted by waste_score asc)
    expect(result.current.combinations).toEqual([mockCombinations[1]]);

    // Change sort direction
    act(() => {
      result.current.updateSort('waste_score');  // Toggle direction
    });

    // Check that the sort direction was changed
    expect(result.current.sortConfig).toEqual({ key: 'waste_score', direction: 'desc' });

    // Clear filters
    act(() => {
      result.current.updateFilter({ minWasteScore: 0, searchTerm: '' });
    });

    // Check that all combinations are shown again, but sorted by waste_score desc
    expect(result.current.combinations).toEqual([mockCombinations[1], mockCombinations[0]]);
  });

  test('should retry operations', async () => {
    // Mock axios.get for initial fetch
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useCombinations());

    // Wait for the initial fetch to complete
    await waitForNextUpdate();

    // Mock axios.get for retry fetch
    axios.get.mockResolvedValueOnce({ data: mockCombinations });
    axios.get.mockResolvedValueOnce({ data: mockCalculationStatus });

    // Retry fetch
    act(() => {
      result.current.retryOperation('fetch');
    });

    // Wait for the retry to complete
    await waitForNextUpdate();

    // Check that axios.get was called again
    expect(axios.get).toHaveBeenCalledWith('http://localhost:8000/api/combinations/get/');
  });
});