import { useState, useCallback, useRef, useEffect } from 'react';
import { useSeasonality as useSharedSeasonality } from '../../../hooks/useSeasonality';
import logger from '../../../utils/logger';
import { ENDPOINTS } from '../../../config/api';

// Cache version for local storage
const CACHE_VERSION = 'v1';
const CACHE_KEY = 'recipe_seasonality_cache';
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Custom hook for managing recipe seasonality calculations with batch loading
 * 
 * This hook provides efficient batch loading of seasonality data for recipes.
 * Key features:
 * - Maintains a localStorage cache to persist data between sessions
 * - Uses batch API requests instead of individual calls for better performance
 * - Provides a synchronous interface that returns cached data immediately
 * - Automatically queues recipes for loading when not in cache
 * 
 * @returns {Object} Recipe seasonality functions and state
 * @property {boolean} isLoading - Whether a batch request is currently in progress
 * @property {Error|null} error - Any error that occurred during loading
 * @property {Function} formatSeasonality - Synchronous function to get seasonality for a recipe
 * @property {Function} preloadSeasonality - Preload seasonality data for a list of recipes
 * @property {Function} resetCache - Clear all cached seasonality data
 */
export const useRecipeSeasonality = (recipes) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Cache for formatted seasonality data by recipe ID
  const [seasonalityCache, setSeasonalityCache] = useState({});
  
  // Track which recipes need loading in a more efficient way
  const [recipesToLoad, setRecipesToLoad] = useState([]);
  
  // Flag to track if we've initialized the cache from localStorage
  const cacheInitialized = useRef(false);
  
  // Use the shared seasonality hook for common functionality
  const {
    // formatSeasonalityData, // Removed as it's not used
    clearSeasonalityCache,
    fetchBatchSeasonality
  } = useSharedSeasonality();
  
  // Initialize cache from localStorage on component mount
  useEffect(() => {
    if (cacheInitialized.current) return;
    
    try {
      const storedCache = localStorage.getItem(CACHE_KEY);
      if (storedCache) {
        const { version, data, timestamp } = JSON.parse(storedCache);
        
        // Check if cache is valid and not expired
        const isValid = version === CACHE_VERSION;
        const isExpired = Date.now() - timestamp > CACHE_EXPIRY;
        
        if (isValid && !isExpired) {
          logger.debug('Loading seasonality cache from localStorage', Object.keys(data).length);
          setSeasonalityCache(data);
        } else {
          logger.debug('Seasonality cache expired or invalid, clearing');
          localStorage.removeItem(CACHE_KEY);
        }
      }
    } catch (err) {
      logger.error('Error loading seasonality cache from localStorage:', err);
      localStorage.removeItem(CACHE_KEY);
    }
    
    cacheInitialized.current = true;
  }, []);
  
  /**
   * Save the current cache to localStorage
   * This is called whenever the cache is updated
   */
  const saveCache = useCallback(() => {
    try {
      const cacheData = {
        version: CACHE_VERSION,
        data: seasonalityCache, // Use state variable
        timestamp: Date.now()
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
    } catch (err) {
      logger.error('Error saving seasonality cache to localStorage:', err);
    }
  }, [seasonalityCache]);

  /**
   * Centralized function to handle batch loading of seasonality data.
   * Fetches data for given recipe IDs, updates cache, and saves to localStorage.
   * @param {string[]} recipeIdsToFetch - Array of recipe IDs to load.
   * @param {string} logContext - Context for logging (e.g., 'Batch seasonality load', 'Preload seasonality').
   */
  const handleBatchLoad = useCallback(async (recipeIdsToFetch, logContext = 'Batch seasonality load') => {
    if (!recipeIdsToFetch || recipeIdsToFetch.length === 0) {
      logger.debug(`No recipes to load for ${logContext}`);
      return;
    }

    setIsLoading(true);
    setError(null);
    let batchResults = {};

    try {
      logger.debug(`${logContext}: Loading seasonality data for ${recipeIdsToFetch.length} recipes`);
      batchResults = await logger.measure(logContext, () =>
        fetchBatchSeasonality(recipeIdsToFetch)
      );

      if (Object.keys(batchResults).length > 0) {
        setSeasonalityCache(prevCache => ({
          ...prevCache,
          ...batchResults
        }));
        // saveCache will be called in the effect that watches seasonalityCache
      }
      logger.debug(`${logContext}: Loaded seasonality data for ${Object.keys(batchResults).length} recipes`);
    } catch (err) {
      logger.error(`Error in ${logContext}:`, err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
    return batchResults; // Return results for potential further processing if needed
  }, [fetchBatchSeasonality, setIsLoading, setError, setSeasonalityCache]);

  // Effect to save cache whenever it changes
  useEffect(() => {
    if (cacheInitialized.current && Object.keys(seasonalityCache).length > 0) {
      saveCache();
    }
  }, [seasonalityCache, saveCache]);
  
  // Load pending recipes from recipesToLoad queue
  useEffect(() => {
    if (recipesToLoad.length > 0) {
      const uniqueRecipeIdsToLoad = [...new Set(recipesToLoad.map(recipe => recipe.id))];
      if (uniqueRecipeIdsToLoad.length > 0) {
        handleBatchLoad(uniqueRecipeIdsToLoad, 'Queued seasonality load').then(() => {
          setRecipesToLoad([]); // Clear the queue after attempting to load
        });
      } else {
        setRecipesToLoad([]); // Clear if no valid IDs
      }
    }
  }, [recipesToLoad, handleBatchLoad, setRecipesToLoad]);
  
  /**
   * Get formatted seasonality data for a recipe
   * This is the main function that components will use
   * 
   * It's synchronous and will:
   * 1. Return cached data immediately if available
   * 2. Queue the recipe for loading if not cached
   * 3. Return default data while loading
   * 
   * @param {Object} recipe - The recipe object to get seasonality for
   * @returns {Object} Formatted seasonality data
   */
  const formatSeasonality = useCallback((recipe) => {
    // If no recipe or no ID, return default data
    if (!recipe || !recipe.id) {
      return {
        hasSeasonality: false,
        data: [],
        seasonalMonths: []
      };
    }
    
    const recipeId = recipe.id;
    
    // If we already have this recipe in cache, return it immediately
    if (seasonalityCache[recipeId]) { // Read from state
      // Data in seasonalityCache is already processed, return directly
      return seasonalityCache[recipeId];
    }
    
    // If this recipe isn't already in the loading queue, add it
    if (!recipesToLoad.some(r => r.id === recipeId)) {
      // Schedule update outside of render cycle
      setTimeout(() => {
        setRecipesToLoad(prev => [...prev, recipe]);
      }, 0);
    }
    
    // Return default data while loading
    return {
      hasSeasonality: false,
      data: [],
      seasonalMonths: []
    };
  }, [recipesToLoad, seasonalityCache, setRecipesToLoad]);
  
  /**
   * Preload seasonality data for a list of recipes
   * Useful for parent components to preload data before rendering
   *
   * @param {Array<Object>} recipesToPreload - Array of recipe objects to preload
   * @returns {Promise<void>}
   */
  const preloadSeasonality = useCallback(async (recipesToPreload) => {
    if (!recipesToPreload || recipesToPreload.length === 0) return;
    
    const recipeIdsToFetch = recipesToPreload
      .filter(recipe => recipe && recipe.id && !seasonalityCache[recipe.id])
      .map(recipe => recipe.id);
      
    if (recipeIdsToFetch.length === 0) {
      logger.debug('No recipes need preloading, all in cache');
      return;
    }
    
    // The actual saving to localStorage is now handled by the useEffect watching seasonalityCache
    await handleBatchLoad(recipeIdsToFetch, 'Preload seasonality');

  }, [seasonalityCache, handleBatchLoad]);
  
  /**
   * Reset the hook's cache and state
   * Use this when data on the server has been updated and local cache needs to be invalidated
   */
  const resetCache = useCallback(() => {
    logger.debug('Resetting seasonality cache');
    setSeasonalityCache({});
    clearSeasonalityCache();
    localStorage.removeItem(CACHE_KEY);
  }, [clearSeasonalityCache, setSeasonalityCache]); // Add setSeasonalityCache

  // Preload seasonality data for recipes when the recipes list changes
  useEffect(() => {
    if (recipes && recipes.length > 0) {
      // Ensure preloadSeasonality is defined and callable
      if (typeof preloadSeasonality === 'function') {
        // Pass the full recipe objects as expected by the refactored preloadSeasonality
        preloadSeasonality(recipes);
      } else {
        logger.warn('preloadSeasonality is not yet available or not a function');
      }
    }
  }, [recipes, preloadSeasonality]);
  
  /**
   * Update seasonality data for all recipes in the database
   * Makes an API call to trigger the backend update_all_recipe_seasonality function
   *
   * @returns {Promise<number>} Number of recipes updated
   */
  const updateAllRecipeSeasonality = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(ENDPOINTS.RECIPES.SEASONALITY.UPDATE_ALL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      
      // Clear the cache since data has been updated
      resetCache();
      
      // Return the number of recipes updated
      return Object.keys(result).length;
    } catch (err) {
      logger.error("Error updating all recipe seasonality:", err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [resetCache]);

  return {
    isLoading,
    error,
    formatSeasonality,
    preloadSeasonality,
    resetCache,
    updateAllRecipeSeasonality
  };
};

export default useRecipeSeasonality; 
