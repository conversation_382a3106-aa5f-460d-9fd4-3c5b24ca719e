import React from 'react';
import '../styles/ServingsInput.css';

const ServingsInput = ({ servings, onServingsChange }) => {
  return (
    <div className="control-group">
      <label htmlFor="servings">Servings:</label>
      <select
        id="servings"
        value={servings}
        onChange={(e) => onServingsChange(Number(e.target.value))}
      >
        <option value={1}>1 person</option>
        <option value={2}>2 people</option>
        <option value={3}>3 people</option>
        <option value={4}>4 people</option>
      </select>
    </div>
  );
};

export default ServingsInput;