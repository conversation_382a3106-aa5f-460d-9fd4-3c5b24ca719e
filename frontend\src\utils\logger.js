/**
 * Central logging utility for the application
 * Provides consistent logging with environment-based behavior
 */

// Whether we're in production mode - used to control debug logging
const isProduction = process.env.NODE_ENV === 'production';

/**
 * Logger utility with different log levels that respect environment settings
 */
export const logger = {
  /**
   * Log debug information - only appears in development
   * @param {string} message - The message to log
   * @param {...any} args - Additional arguments to log
   */
  debug: (message, ...args) => {
    if (!isProduction) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  },

  /**
   * Log info messages - only appears in development
   * @param {string} message - The message to log
   * @param {...any} args - Additional arguments to log
   */
  info: (message, ...args) => {
    if (!isProduction) {
      console.info(`[INFO] ${message}`, ...args);
    }
  },

  /**
   * Log warnings - appears in all environments
   * @param {string} message - The message to log
   * @param {...any} args - Additional arguments to log
   */
  warn: (message, ...args) => {
    console.warn(`[WARN] ${message}`, ...args);
  },

  /**
   * Log errors - appears in all environments
   * @param {string} message - The message to log
   * @param {...any} args - Additional arguments to log
   */
  error: (message, ...args) => {
    console.error(`[ERROR] ${message}`, ...args);
  },

  /**
   * Log performance measurements - only appears in development
   * @param {string} label - The label for the measurement
   * @param {Function} fn - The function to measure
   * @returns {any} The result of the measured function
   */
  measure: async (label, fn) => {
    if (isProduction) return fn();
    
    const start = performance.now();
    try {
      return await fn();
    } finally {
      const end = performance.now();
      console.info(`[PERFORMANCE] ${label}: ${(end - start).toFixed(2)}ms`);
    }
  }
};

export default logger; 