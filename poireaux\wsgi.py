"""
WSGI config for poireaux project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/wsgi/
"""

import os
from dotenv import load_dotenv

from django.core.wsgi import get_wsgi_application

# Load environment variables from .env file
load_dotenv()

# Determine which settings file to use based on an environment variable
# Default to development settings if DJANGO_ENV is not set
DJANGO_ENV = os.environ.get('DJANGO_ENV', 'development')

if DJANGO_ENV == 'production':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'poireaux.settings.production')
else:  # Default to development
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'poireaux.settings.development')

application = get_wsgi_application()
