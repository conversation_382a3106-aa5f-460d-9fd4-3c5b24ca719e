import React from 'react';
import './RecipeCard.css';

const RecipeCard = ({ recipe, combination, isActive = false }) => {
  if (!recipe) {
    return (
      <div className="recipe-card-loading">
        <div className="recipe-card-placeholder">
          <div className="recipe-card-placeholder-image"></div>
          <div className="recipe-card-placeholder-content">
            <div className="recipe-card-placeholder-title"></div>
            <div className="recipe-card-placeholder-text"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <div className={`recipe-card ${isActive ? 'active' : ''}`}>
        <div className="recipe-card-image-container">
          {recipe.image ? (
            <img
              src={recipe.image}
              alt={`Image of ${recipe.name}`}
              className="recipe-card-image"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />
          ) : (
            <div className="recipe-card-image-placeholder" style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span>No image available</span>
            </div>
          )}
        </div>
      </div>

      {/* Recipe name below the card */}
      {recipe.name && (
        <div style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '4px',
          padding: '8px 12px',
          marginTop: '8px',
          textAlign: 'center',
          fontSize: '0.9em',
          fontWeight: '500',
          color: '#333'
        }}>
          {recipe.name}
        </div>
      )}
    </div>
  );
};

export default RecipeCard;
