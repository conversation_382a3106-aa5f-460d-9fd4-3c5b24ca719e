import { useState, useEffect } from 'react';
import { API_BASE_URL } from '../../../../config/api';
import { useIngredients } from '../../../../hooks/useIngredients';
import { MONTHS, MEASUREMENT_UNITS } from '../../../ingredient/components/IngredientAdder/IngredientAdder.js';

export const RECIPE_TYPES = ['main', 'side', 'salad', 'spread'];
export const RECIPE_DIETS = ['fish', 'vegan', 'meat', 'vegetarian'];
export const RECIPE_CATEGORIES = ['legumes', 'vegetable', 'pasta', 'linzen', 'bulgur'];

export const useRecipeAdder = () => {
  // Recipe form state
  const [name, setName] = useState('');
  const [instructions, setInstructions] = useState('');
  const [image, setImage] = useState(null); // For file input
  const [imagePreview, setImagePreview] = useState('');
  const [months, setMonths] = useState(
    MONTHS.reduce((acc, month) => ({ ...acc, [month]: false }), {})
  );
  const [type, setType] = useState(RECIPE_TYPES[0]);
  const [diet, setDiet] = useState(RECIPE_DIETS[0]);
  const [servings, setServings] = useState(4);
  const [category, setCategory] = useState(RECIPE_CATEGORIES[0]);
  const [divisible, setDivisible] = useState(false);

  // Ingredient selection state
  const [selectedIngredients, setSelectedIngredients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [ingredientAmountToAdd, setIngredientAmountToAdd] = useState(''); // Initialize as empty string for placeholder
  const [filteredIngredients, setFilteredIngredients] = useState([]);

  // Feedback and loading state
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch all ingredients
  const { ingredients: allIngredients, isLoading: ingredientsLoading, error: ingredientsError } = useIngredients(true); // autoFetch true

  // Effect to filter ingredients based on search term
  useEffect(() => {
    if (searchTerm === '') {
      setFilteredIngredients(allIngredients);
    } else {
      setFilteredIngredients(
        allIngredients.filter(ingredient =>
          ingredient.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
  }, [searchTerm, allIngredients]);

  const handleInputChange = (event) => {
    const { name, value, checked, files } = event.target;
    if (name === 'name') setName(value);
    else if (name === 'instructions') setInstructions(value);
    else if (name === 'type') setType(value);
    else if (name === 'diet') setDiet(value);
    else if (name === 'servings') setServings(parseInt(value, 10) || 0);
    else if (name === 'category') setCategory(value);
    else if (name === 'divisible') setDivisible(checked);
    else if (name === 'image' && files && files[0]) {
      setImage(files[0]);
      setImagePreview(URL.createObjectURL(files[0]));
    }
  };

  const handleSeasonChange = (month) => {
    setMonths(prevMonths => ({
      ...prevMonths,
      [month]: !prevMonths[month]
    }));
  };

  const handleIngredientSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleIngredientAmountChange = (event) => {
    setIngredientAmountToAdd(event.target.value); // Keep as string for input binding
  };

  const handleAddIngredient = (ingredient, amountString) => {
    console.log('Ingredient object received by handleAddIngredient:', ingredient);
    console.log('divisible_by_measurement for this ingredient:', ingredient.divisible_by_measurement);

    let quantityToAdd = parseFloat(amountString);

    // If parsing fails, is not a number, or is less than 0.1, default to 1.
    // Also default to 1 if the input string was empty.
    if (isNaN(quantityToAdd) || quantityToAdd < 0.1 || (typeof amountString === 'string' && amountString.trim() === '')) {
      quantityToAdd = 1;
    }

    if (!selectedIngredients.find(selected => selected.ingredientId === ingredient.id)) {
      setSelectedIngredients(prev => [
        ...prev,
        {
          ingredientId: ingredient.id,
          name: ingredient.name, // Store name for display purposes
          quantity: quantityToAdd,
          unit: ingredient.divisible_by_measurement,
          isDivisible: false // Default to false
        }
      ]);
      setSearchTerm(''); // Reset search term
      setIngredientAmountToAdd(''); // Reset amount input to empty string for placeholder
    }
  };

  const handleRemoveIngredient = (ingredientId) => {
    setSelectedIngredients(prev => prev.filter(ing => ing.ingredientId !== ingredientId));
  };

  const handleIngredientDetailChange = (ingredientId, field, value) => {
    setSelectedIngredients(prev =>
      prev.map(ing =>
        ing.ingredientId === ingredientId ? { ...ing, [field]: value } : ing
      )
    );
  };
  
  const handleIngredientQuantityChange = (ingredientId, quantity) => {
    handleIngredientDetailChange(ingredientId, 'quantity', parseFloat(quantity) || 0);
  };

  const handleIngredientUnitChange = (ingredientId, unit) => {
    handleIngredientDetailChange(ingredientId, 'unit', unit);
  };

  const handleIngredientDivisibleChange = (ingredientId, isDivisible) => {
    handleIngredientDetailChange(ingredientId, 'isDivisible', isDivisible);
  };

  const handleNameBlur = () => {
    setName(currentName => {
      if (currentName.trim() && !currentName.endsWith(' (original)')) {
        return `${currentName.trim()} (original)`;
      }
      return currentName;
    });
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsSubmitting(true);
    setFeedbackMessage('');

    if (!name.trim()) {
      setFeedbackMessage('Recipe name is required.');
      setIsSubmitting(false);
      return;
    }
    if (selectedIngredients.length === 0) {
        setFeedbackMessage('At least one ingredient must be selected.');
        setIsSubmitting(false);
        return;
    }

    const formData = new FormData();
    formData.append('name', name.trim());
    formData.append('instructions', instructions.trim());
    formData.append('months', JSON.stringify(Object.entries(months).filter(([, value]) => value).map(([key]) => key)));
    formData.append('type', type);
    formData.append('diet', diet);
    formData.append('servings', servings);
    formData.append('category', category);
    formData.append('divisible', divisible);
    if (image) {
      formData.append('image', image);
    }
    // Append selected ingredients as a JSON string
    formData.append('ingredients', JSON.stringify(selectedIngredients.map(ing => ({
      ingredient: ing.ingredientId, // Backend expects 'ingredient' not 'ingredientId'
      quantity: ing.quantity,
      unit: ing.unit,
      is_divisible: ing.isDivisible,
    }))));

    try {
      // Step 1: Create the recipe with nested ingredients
      const recipeResponse = await fetch(`${API_BASE_URL}/recipes/create/`, {
        method: 'POST',
        body: formData, // Use FormData
      });

      if (!recipeResponse.ok) {
        const errorData = await recipeResponse.json();
        throw new Error(`Recipe creation failed: ${errorData.detail || recipeResponse.statusText}`);
      }

      const createdRecipe = await recipeResponse.json();
      
      // New Step: Call the backend processing endpoint
      const processResponse = await fetch(`${API_BASE_URL}/recipes/process-new/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ original_recipe_id: createdRecipe.id }),
      });

      if (!processResponse.ok) {
        const errorData = await processResponse.json().catch(() => ({ detail: 'Unknown error during processing' })); // Add a catch for non-JSON responses
        throw new Error(`Recipe processing failed: ${errorData.detail || processResponse.statusText}`);
      }

      const processResult = await processResponse.json();
      console.log('Recipe processing successful:', processResult);
      
      setFeedbackMessage('Recipe created and processed successfully!');
      // Reset form
      setName('');
      setInstructions('');
      setImage(null);
      setImagePreview('');
      setMonths(MONTHS.reduce((acc, month) => ({ ...acc, [month]: false }), {}));
      setType(RECIPE_TYPES[0]);
      setDiet(RECIPE_DIETS[0]);
      setServings(4);
      setCategory(RECIPE_CATEGORIES[0]);
      setDivisible(false);
      setSelectedIngredients([]);
      setSearchTerm('');
    } catch (error) {
      console.error('Error creating recipe:', error);
      setFeedbackMessage(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    name,
    instructions,
    image,
    imagePreview,
    months,
    type,
    diet,
    servings,
    category,
    divisible,
    selectedIngredients,
    searchTerm,
    ingredientAmountToAdd,
    filteredIngredients,
    feedbackMessage,
    isSubmitting,
    allIngredients,
    ingredientsLoading,
    ingredientsError,
    handleInputChange,
    handleNameBlur,
    handleSeasonChange,
    handleIngredientSearch,
    handleIngredientAmountChange,
    handleAddIngredient,
    handleRemoveIngredient,
    handleIngredientQuantityChange,
    handleIngredientUnitChange,
    handleIngredientDivisibleChange,
    handleSubmit,
    // Constants for dropdowns/selections
    RECIPE_TYPES,
    RECIPE_DIETS,
    RECIPE_CATEGORIES,
    MONTHS,
    MEASUREMENT_UNITS,
  };
};
