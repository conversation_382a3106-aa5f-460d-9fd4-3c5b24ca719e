.combination-pop-up-overlay {
  position: fixed; /* Use fixed to cover the entire viewport */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Darker overlay for better focus */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 40px; /* Add some padding so the card doesn't touch the edges */
  box-sizing: border-box;
}

/* Mobile overlay with reduced padding */
@media (max-width: 768px) {
  .combination-pop-up-overlay {
    padding: 10px; /* Reduced padding for mobile */
  }
}

.combination-pop-up-card {
  position: relative; /* Ensure the card is a positioning context for the button */
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Mobile card with reduced padding */
@media (max-width: 768px) {
  .combination-pop-up-card {
    padding: 10px; /* Reduced padding for mobile */
  }
}

.combination-pop-up-content {
  display: flex;
  flex: 1;
  gap: 20px;
  align-items: stretch;
  overflow: hidden;
  padding-top: 20px;
  padding-bottom: 20px;
  box-sizing: border-box;
}

/* Add responsive layout for mobile - Two column layout */
@media (max-width: 768px) {
  .combination-pop-up-content {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    grid-template-rows: auto auto;
    gap: 10px; /* Reduced gap for mobile */
    grid-template-areas:
      "image ingredients"
      "image instructions";
  }

  .combination-pop-up-nav-button-container {
    display: none; /* Hide side navigation buttons on mobile */
  }

  /* Left column - Image */
  .combination-pop-up-left {
    grid-area: image;
    padding: 5px; /* Reduced padding */
    margin-bottom: 0;
  }

  /* Right column top - Ingredients */
  .combination-pop-up-middle {
    grid-area: ingredients;
    padding: 8px; /* Reduced padding */
    margin-bottom: 0;
  }

  /* Right column bottom - Instructions */
  .combination-pop-up-right {
    grid-area: instructions;
    padding: 8px; /* Reduced padding */
    margin-bottom: 0;
  }

  /* Add mobile navigation controls at the bottom */
  .mobile-nav-controls {
    display: flex;
    justify-content: center;
    gap: 15px; /* Reduced gap */
    margin-top: 5px;
    margin-bottom: 5px;
  }
}

/* Hide mobile controls on desktop */
.mobile-nav-controls {
  display: none;
}

.combination-pop-up-nav-button-container {
  flex: 0 0 50px; /* Do not grow, do not shrink, base width of 50px */
  display: flex;
  justify-content: center;
  align-items: center;
}

.combination-pop-up-nav-button {
  background: none;
  border: 1px solid #ccc;
  cursor: pointer;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.combination-pop-up-nav-button:hover {
  background-color: #f0f0f0;
}

.combination-pop-up-left {
  flex: 1;
  padding: 10px;
  border: 1px solid #e0e0e0; /* Lighter border */
  border-radius: 8px; /* Rounded corners for the image container */
  overflow: hidden; /* Ensure the image respects the border radius */
  box-shadow: 0 2px 4px rgba(0,0,0,0.05); /* Subtle shadow for depth */
}

.combination-pop-up-middle,
.combination-pop-up-right {
  flex: 1;
  padding: 20px;
  border: 1px solid #e0e0e0; /* Lighter border */
  border-radius: 8px;
  overflow-y: auto; /* Allow scrolling within columns if needed */
  box-shadow: 0 2px 4px rgba(0,0,0,0.05); /* Subtle shadow for depth */
}

.combination-pop-up-card h2 {
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.instructions-container {
  white-space: pre-wrap; /* Preserve whitespace and wrap text */
  word-wrap: break-word;
  padding: 10px;
  font-size: 14px;
  line-height: 1.6;
}

/* Mobile-specific styling for smaller text and images */
@media (max-width: 768px) {
  /* Smaller image size on mobile */
  .combination-pop-up-left .card-recipe-image-img,
  .combination-pop-up-left img {
    height: 150px !important; /* Reduced from default 250px */
    max-height: 150px;
  }

  .combination-pop-up-left .recipe-image-container {
    height: 150px !important;
  }

  .combination-pop-up-left .card-recipe-image-img-placeholder {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }

  /* Smaller text sizes for ingredients */
  .combination-pop-up-middle .ingredients-section h4 {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .combination-pop-up-middle .ingredients-section li {
    font-size: 12px;
    padding: 6px 0;
  }

  .combination-pop-up-middle .no-ingredients {
    font-size: 12px;
    padding: 15px;
  }

  /* Smaller text sizes for instructions */
  .instructions-container {
    font-size: 12px;
    line-height: 1.5;
    padding: 8px;
  }

  .instructions-container p {
    margin: 0;
    font-size: 12px;
  }

  /* Smaller navigation buttons on mobile */
  .mobile-nav-controls .combination-pop-up-nav-button {
    width: 35px;
    height: 35px;
    font-size: 18px;
  }

  /* Ensure proper spacing and sizing for mobile grid */
  .combination-pop-up-middle,
  .combination-pop-up-right {
    min-height: 0; /* Allow grid items to shrink */
    overflow-y: auto; /* Enable scrolling if content is too long */
  }

  /* Adjust footer for mobile */
  .combination-pop-up-footer {
    padding: 8px 10px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .popup-button {
    padding: 6px 12px;
    font-size: 12px;
    margin-left: 0;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .combination-pop-up-overlay {
    padding: 5px;
  }

  .combination-pop-up-card {
    padding: 8px;
  }

  .combination-pop-up-content {
    gap: 8px;
  }

  /* Even smaller image for very small screens */
  .combination-pop-up-left .card-recipe-image-img,
  .combination-pop-up-left img {
    height: 120px !important;
  }

  .combination-pop-up-left .recipe-image-container {
    height: 120px !important;
  }

  /* Even smaller text for very small screens */
  .combination-pop-up-middle .ingredients-section h4 {
    font-size: 13px;
  }

  .combination-pop-up-middle .ingredients-section li {
    font-size: 11px;
  }

  .instructions-container {
    font-size: 11px;
  }
}

.combination-pop-up-close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #888;
}

.combination-pop-up-close-button:hover {
  color: #000;
}

.combination-pop-up-footer {
  padding: 10px 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
}

.popup-button {
  margin-left: 10px;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.save-button {
  background-color: #4CAF50;
  color: white;
}

.save-button:hover {
  background-color: #45a049;
}

.delete-button {
  background-color: #f44336;
  color: white;
}

.delete-button:hover {
  background-color: #da190b;
}
