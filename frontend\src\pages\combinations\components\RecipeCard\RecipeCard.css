/* Recipe Card Styles */
.recipe-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  max-width: 400px;
  margin: 0;
  padding: 0;
  touch-action: manipulation; /* Ensure touch events work properly */
  pointer-events: auto; /* Ensure pointer events are enabled */
}

.recipe-card.active {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.recipe-card-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.recipe-card-title {
  font-size: 1.4em;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.3;
}



.recipe-card-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
  flex: 1;
}

.recipe-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  margin: 0;
  padding: 0;
  border: none;
}

.recipe-card:hover .recipe-card-image {
  transform: scale(1.05);
}

.recipe-card-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 0.9em;
  border: none;
  margin: 0;
  padding: 0;
}

/* Content section removed - image now fills entire card */

.recipe-card-section-title {
  font-size: 1em;
  font-weight: 600;
  color: #34495e;
  margin: 0 0 12px 0;
  border-bottom: 2px solid #3498db;
  padding-bottom: 4px;
  display: inline-block;
}

.recipe-card-ingredients {
  margin-bottom: 20px;
}

.recipe-card-ingredients-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recipe-card-ingredient-item {
  padding: 6px 0;
  color: #555;
  font-size: 0.9em;
  border-bottom: 1px solid #f8f9fa;
  position: relative;
  padding-left: 16px;
}

.recipe-card-ingredient-item:before {
  content: '•';
  color: #3498db;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.recipe-card-ingredient-item.more {
  color: #7f8c8d;
  font-style: italic;
  font-weight: 500;
}

.recipe-card-ingredient-item:last-child {
  border-bottom: none;
}





/* Footer section removed - image now fills entire card */

/* Loading placeholder styles */
.recipe-card-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recipe-card-placeholder {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.recipe-card-placeholder-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.recipe-card-placeholder-content {
  padding: 20px;
  flex: 1;
}

.recipe-card-placeholder-title {
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 16px;
}

.recipe-card-placeholder-text {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 80%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .recipe-card {
    max-width: 100%;
    margin: 0;
  }

  .recipe-card-image-container {
    height: 100%;
  }
}
