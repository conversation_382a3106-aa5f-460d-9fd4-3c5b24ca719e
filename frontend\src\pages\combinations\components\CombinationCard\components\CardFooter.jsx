import React from 'react';
import '../styles/CardFooter.css';

const CardFooter = ({ combination, onSave }) => {
  return (
    <div className="card-footer">
      <div className="serving-info">
        For {combination.servings} person{combination.servings !== 1 ? 's' : ''}
      </div>
      
      <div className="card-actions">
        {!combination.id && (
          <button
            className="save-btn"
            onClick={() => onSave(combination)}
          >
            Save
          </button>
        )}
      </div>
    </div>
  );
};

export default CardFooter;