import React from 'react';
import { useSwipeable } from 'react-swipeable';
import './styles/CardRecipeImage.css';

const CardRecipeImage = ({ recipes, imageUrl, activeRecipe, goToPreviousRecipe, goToNextRecipe, showNavigation = true }) => {
  const handlers = useSwipeable({
    onSwipedLeft: () => showControls && goToNextRecipe(),
    onSwipedRight: () => showControls && goToPreviousRecipe(),
    preventScrollOnSwipe: true,
    trackMouse: true // Allows swiping with mouse for mousepad-like behavior
  });

  if (!recipes || recipes.length === 0) {
    // Display a placeholder if there are no recipes
    return (
      <div className="card-recipe-image-no-recipes-placeholder-container">
        <div className="card-recipe-image-no-recipes-placeholder-content">
          No recipes available
        </div>
      </div>
    );
  }

  const totalRecipes = recipes.length;
  const showControls = totalRecipes > 1 && showNavigation;

  // Determine the current index from activeRecipe to manage UI state
  const currentIndex = activeRecipe ? recipes.findIndex(recipe => recipe.id === activeRecipe.id) : -1;

  const recipeImageWithControlsClassName = `recipe-image-with-controls ${ (showControls || totalRecipes > 0) ? 'has-margin-bottom' : ''}`;

  return (
    <div className="recipe-image-display">
      <div
        {...handlers}
        className={recipeImageWithControlsClassName}
        style={{ touchAction: 'pan-y', position: 'relative' }} // Added position relative for button positioning
      >
        {showControls && (
          <button
            onClick={(e) => { e.stopPropagation(); goToPreviousRecipe(); }}
            className="card-recipe-image-nav-button card-recipe-image-prev-button"
            aria-label="Previous Recipe"
          >
            {'<'}
          </button>
        )}

        {/* Image container - full card coverage */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flex: 1,
          margin: '0',
          height: '100%',
          width: '100%'
        }}>
          {imageUrl ? (
            <img
              src={imageUrl}
              alt={activeRecipe?.name ? `Image of ${activeRecipe.name}` : 'Current Recipe Image'}
              className="card-recipe-image-img"
              style={{
                userSelect: 'none',
                WebkitUserDrag: 'none',
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />
          ) : (
            <div className="card-recipe-image-img-placeholder" style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {activeRecipe?.name ? `No image for ${activeRecipe.name}` : 'Image not available'}
            </div>
          )}
        </div>

        {showControls && (
          <button
            onClick={(e) => { e.stopPropagation(); goToNextRecipe(); }}
            className="card-recipe-image-nav-button card-recipe-image-next-button"
            aria-label="Next Recipe"
          >
            {'>'}
          </button>
        )}
        {/* Navigation indicators (dots) could be added here if desired */}
      </div>

      {/* Recipe name below the card */}
      {activeRecipe?.name && (
        <div style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '4px',
          padding: '8px 12px',
          marginTop: '8px',
          textAlign: 'center',
          fontSize: '0.9em',
          fontWeight: '500',
          color: '#495057'
        }}>
          {activeRecipe.name}
        </div>
      )}
    </div>
  );
};

export default CardRecipeImage;
