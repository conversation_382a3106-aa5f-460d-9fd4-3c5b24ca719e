#!/bin/bash

# Setup Google Cloud Scheduler to keep Cloud Run instance warm
# Run this script after deploying your application

# Set your project variables
PROJECT_ID="poireaux-d79c7"  # Your Firebase project ID
SERVICE_URL="https://poireaux-749928798411.europe-west1.run.app"
REGION="europe-west1"  # Your Cloud Run region

echo "Setting up Cloud Scheduler to keep Cloud Run instance warm..."

# Create the scheduler job
gcloud scheduler jobs create http poireaux-keepalive \
    --location=$REGION \
    --schedule="*/5 * * * *" \
    --uri="$SERVICE_URL/api/health/" \
    --http-method=GET \
    --description="Keep Poireaux Cloud Run instance warm" \
    --project=$PROJECT_ID

echo "Cloud Scheduler job 'poireaux-keepalive' created successfully!"
echo "The job will ping $SERVICE_URL/api/health/ every 5 minutes."
echo ""
echo "To view the job:"
echo "gcloud scheduler jobs describe poireaux-keepalive --location=$REGION"
echo ""
echo "To manually trigger the job:"
echo "gcloud scheduler jobs run poireaux-keepalive --location=$REGION"
