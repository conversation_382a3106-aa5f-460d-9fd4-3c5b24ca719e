import json
from typing import List,Optional
from django.utils import timezone
from ...models import Recipe, Ingredient, RecipeIngredient, Combination # Ensure RecipeIngredient is imported

def mark_combinations_invalid_for_recipe(recipe: Recipe) -> int:
    """
    Mark all combinations containing a specific recipe as invalid.
    
    Args:
        recipe: Recipe that was updated
        
    Returns:
        Number of combinations marked as invalid
    """
    # Get all combinations that include this recipe
    affected_combinations = Combination.objects.filter(recipes=recipe, cache_valid=True)
    
    # Mark them as invalid
    count = affected_combinations.count()
    affected_combinations.update(cache_valid=False)
    
    return count

def mark_combinations_invalid_for_ingredient(ingredient: Ingredient) -> int:
    """
    Mark all combinations dependent on a specific ingredient as invalid.
    
    Args:
        ingredient: Ingredient that was updated
        
    Returns:
        Number of combinations marked as invalid
    """
    # Find all combinations that depend on this ingredient
    # This requires checking the dependent_ingredients field
    affected_combinations = Combination.objects.filter(
        cache_valid=True,
        dependent_ingredients__contains=str(ingredient.id)
    )
    
    # Mark them as invalid
    count = affected_combinations.count()
    affected_combinations.update(cache_valid=False)
    
    return count

def mark_top_performers_for_recalculation(servings: int = None) -> int:
    """
    Mark only top-performing combinations for recalculation.
    
    Args:
        servings: Optional filter for specific servings
        
    Returns:
        Number of combinations marked for recalculation
    """
    # Start with combinations that are invalid and are top performers
    query = Combination.objects.filter(cache_valid=False, is_top_performer=True)
    
    # Filter by servings if specified
    if servings is not None:
        query = query.filter(servings=servings)
    
    # Count and return the number of affected combinations
    return query.count()

def identify_affected_combinations(recipe_id: int = None, ingredient_id: int = None) -> List[int]:
    """
    Identify combinations affected by a recipe or ingredient update.
    
    Args:
        recipe_id: ID of the updated recipe (optional)
        ingredient_id: ID of the updated ingredient (optional)
        
    Returns:
        List of affected combination IDs
    """
    affected_ids = []
    
    if recipe_id:
        # Find combinations containing this recipe
        affected_ids.extend(
            Combination.objects.filter(recipes__id=recipe_id)
            .values_list('id', flat=True)
        )
    
    if ingredient_id:
        # Find combinations dependent on this ingredient
        affected_ids.extend(
            Combination.objects.filter(dependent_ingredients__contains=str(ingredient_id))
            .values_list('id', flat=True)
        )
    
    return list(set(affected_ids))  # Remove duplicates

def recalculate_combination(combination_id: int) -> bool:
    """
    Recalculate a specific combination.
    
    Args:
        combination_id: ID of the combination to recalculate
        
    Returns:
        True if recalculation was successful, False otherwise
    """
    try:
        combination = Combination.objects.get(id=combination_id)
        print(f"--- Recalculating Combination ID: {combination_id} ---") # ADDED LOG
        
        # Get the recipes in this combination
        recipes = list(combination.recipes.all())

        print(f"Combination {combination_id}: ingredients_length BEFORE check = {combination.ingredients_length}") # ADDED LOG

        # Calculate ingredients_length only if it hasn't been set (is 0)
        if combination.ingredients_length == 0:
            print(f"Combination {combination_id}: Condition (ingredients_length == 0) MET.") # ADDED LOG
            # --- START: Calculate unique ingredients length ---
            unique_ingredient_ids = set()
            recipe_ids = [recipe.id for recipe in recipes]
            recipe_ingredients = RecipeIngredient.objects.filter(recipe_id__in=recipe_ids).select_related('ingredient')
            for ri in recipe_ingredients:
                unique_ingredient_ids.add(ri.ingredient_id)
            ingredients_count = len(unique_ingredient_ids)
            print(f"Combination {combination_id}: Calculated ingredients_count = {ingredients_count}") # ADDED LOG
            combination.ingredients_length = ingredients_count # Assign the calculated count
            print(f"Combination {combination_id}: ingredients_length AFTER assignment = {combination.ingredients_length}") # ADDED LOG
            # --- END: Calculate unique ingredients length ---
        else: # ADDED LOG
            print(f"Combination {combination_id}: Condition (ingredients_length == 0) NOT MET.") # ADDED LOG
        
        # Import here to avoid circular imports
        from ...services.combinations.combination_service import calculate_waste_score, check_seasonality_overlap
        
        # Recalculate waste score
        waste_score, ingredient_waste = calculate_waste_score(recipes, combination.servings)
        
        # Recalculate seasonality
        has_overlap, overlapping_months = check_seasonality_overlap(recipes)
        
        # Format ingredient waste for JSON serialization
        formatted_waste = {}
        for key, data in ingredient_waste.items():
            if 'ingredient' not in data:
                continue
                
            ingredient = data['ingredient']
            formatted_waste[key] = {
                'ingredient_id': ingredient.id,
                'ingredient_name': ingredient.name,
                'needed_quantity': float(data['quantity']),
                'unit': data['unit'],
                'buy_amount': float(data['buy_amount']),
                'waste': float(data['waste']),
                'waste_percentage': float(data.get('waste_percentage', 0)),
                'needs_calculating': ingredient.needs_calculating
            }
        
        # Update the combination
        combination.waste_score = waste_score
        combination.ingredient_waste = formatted_waste
        combination.seasonality_months = json.dumps(list(overlapping_months))
        combination.last_calculated = timezone.now()
        combination.cache_valid = True
        # ingredients_length is assigned inside the if block above

        # Extract dependent ingredients from the ingredient waste
        dependent_ingredients = []
        for key in ingredient_waste.keys():
            # Extract ingredient ID from the key (format: "{ingredient_id}_{unit}")
            ingredient_id = key.split('_')[0]
            dependent_ingredients.append(ingredient_id)
        
        combination.dependent_ingredients = dependent_ingredients
        print(f"Combination {combination_id}: Saving combination...") # ADDED LOG
        combination.save(update_fields=[
            'waste_score',
            'ingredient_waste',
            'seasonality_months',
            'last_calculated',
            'cache_valid',
            'dependent_ingredients',
            'ingredients_length' # Explicitly include ingredients_length
        ])
        print(f"Combination {combination_id}: Save complete.") # ADDED LOG
        
        return True
    except Exception as e:
        print(f"Error recalculating combination {combination_id}: {str(e)}")
        return False

def recalculate_marked_combinations(limit: int = 100) -> int:
    """
    Background task function to recalculate marked combinations.
    
    Args:
        limit: Maximum number of combinations to recalculate in one batch
        
    Returns:
        Number of combinations recalculated
    """
    # Get combinations that need recalculation, prioritizing top performers
    combinations = Combination.objects.filter(cache_valid=False).order_by('-is_top_performer')[:limit]
    
    count = 0
    for combination in combinations:
        success = recalculate_combination(combination.id)
        if success:
            count += 1
    
    return count

def update_top_performers() -> int:
    """
    Update the is_top_performer flag for combinations based on waste score.
    
    Returns:
        Number of combinations marked as top performers
    """
    # For each serving size, mark the top 10% of combinations as top performers
    serving_sizes = Combination.objects.values_list('servings', flat=True).distinct()
    
    total_marked = 0
    for servings in serving_sizes:
        # Get all combinations for this serving size
        combinations = Combination.objects.filter(servings=servings).order_by('waste_score')
        
        # Calculate how many to mark as top performers (10%)
        count = combinations.count()
        top_count = max(1, int(count * 0.1))  # At least 1
        
        # Mark the top performers
        for combination in combinations[:top_count]:
            if not combination.is_top_performer:
                combination.is_top_performer = True
                combination.save(update_fields=['is_top_performer'])
                total_marked += 1
        
        # Unmark the rest
        for combination in combinations[top_count:]:
            if combination.is_top_performer:
                combination.is_top_performer = False
                combination.save(update_fields=['is_top_performer'])
    
    return total_marked
