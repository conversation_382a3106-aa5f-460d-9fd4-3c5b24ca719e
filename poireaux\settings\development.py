"""Development settings for the poireaux project."""
# poireaux/settings/development.py
import os  # noqa: F401
from .base import *  # pylint: disable=wildcard-import, unused-wildcard-import

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# SECURITY WARNING: keep the secret key used in production secret!
# You can use a simple, non-secret key for development.
SECRET_KEY = 'django-insecure-your-development-secret-key-here'

ALLOWED_HOSTS = ['127.0.0.1', 'localhost']

# Database configuration for production (e.g., Supabase or other cloud provider)
# Ensure these environment variables are set in your production environment.
DB_NAME = os.environ.get('DB_NAME')
DB_USER = os.environ.get('DB_USER')
DB_PASSWORD = os.environ.get('DB_PASSWORD')
DB_HOST = os.environ.get('DB_HOST')
DB_PORT = os.environ.get('DB_PORT', '5432') # Default to 5432 if not set

if not all([<PERSON>_NAME, DB_USER, DB_PASSWORD, DB_HOST]):
    raise ValueError(
        "One or more database environment variables (DB_NAME, DB_USER, "
        "DB_PASSWORD, DB_HOST) are not set for production."
    )

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': DB_NAME,
        'USER': DB_USER,
        'PASSWORD': DB_PASSWORD,
        'HOST': DB_HOST,
        'PORT': DB_PORT,
        # Optional: Add SSL settings if required by your production database
        # 'OPTIONS': {
        #     'sslmode': 'require',
        # },
        'ATOMIC_REQUESTS': True, # Ensures each request is wrapped in a transaction
    }
}

# Email backend for development (prints emails to console)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Static files (CSS, JavaScript, Images)
# In development, Django's runserver can serve static files automatically if DEBUG is True
# and 'django.contrib.staticfiles' is in INSTALLED_APPS.
# STATICFILES_DIRS can be used to tell Django where to find additional static files
# not tied to a specific app.
# STATICFILES_DIRS = [
#     BASE_DIR / "static", # Project-level static files
# ]

# CORS settings for development
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # For local React dev server (npm start)
    "http://localhost:5000",  # For local Firebase Hosting Emulator
    "http://127.0.0.1:3000",  # Alternative localhost
    "http://127.0.0.1:5000",  # Alternative localhost
]

# Add FRONTEND_URL if set
production_frontend_url = os.getenv('FRONTEND_URL')
if production_frontend_url:
    CORS_ALLOWED_ORIGINS.append(production_frontend_url.rstrip('/'))

# For very permissive local development (use with caution):
# CORS_ALLOW_ALL_ORIGINS = True

# Logging configuration for development
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG', # Show DEBUG level messages and above in development
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'), # Django specific logs
            'propagate': False,
        },
        'django.db.backends': { # Add this for database query logging
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Any other development-specific settings can go here.
