{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "alwaysAllow": ["list_projects", "execute_sql"]}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "browser-tools": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true}}}