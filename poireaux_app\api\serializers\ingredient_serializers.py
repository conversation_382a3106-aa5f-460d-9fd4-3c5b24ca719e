from rest_framework import serializers
from ...models import Ingredient

class IngredientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ingredient
        fields = '__all__'

    def validate_bought_by_amount(self, value):
        """Validate that bought_by_amount is a list of positive integers"""
        if not isinstance(value, list):
            raise serializers.ValidationError("bought_by_amount must be a list")

        for amount in value:
            if not isinstance(amount, int) or amount <= 0:
                raise serializers.ValidationError("All amounts must be positive integers")

        return value

# Add a more lightweight serializer for lists of ingredients
class IngredientListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ingredient
        # Include only the most commonly needed fields in list views
        fields = ['id', 'name', 'has_seasonality', 'type', 'divisible_by_measurement', 
                 'divisible_by_int', 'bought_by', 'bought_by_amount'] 
