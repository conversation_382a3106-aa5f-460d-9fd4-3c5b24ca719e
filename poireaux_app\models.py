from django.db import models

class Ingredient(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100)
    has_seasonality = models.BooleanField(default=True)
    type = models.CharField(max_length=100, default='vegetable')
    divisible_by_measurement = models.CharField(max_length=100, default='unit')
    divisible_by_int = models.CharField(max_length=100, default='[1]')
    bought_by = models.CharField(max_length=100, default='unit')
    bought_by_amount = models.JSONField(default=list, blank=True, help_text="List of purchase amounts (e.g., [250, 400] for different can sizes)")
    needs_calculating = models.BooleanField(default=True)
    # Store months as booleans
    january = models.BooleanField(default=False)
    february = models.BooleanField(default=False)
    march = models.BooleanField(default=False)
    april = models.BooleanField(default=False)
    may = models.BooleanField(default=False)
    june = models.BooleanField(default=False)
    july = models.BooleanField(default=False)
    august = models.BooleanField(default=False)
    september = models.BooleanField(default=False)
    october = models.BooleanField(default=False)
    november = models.BooleanField(default=False)
    december = models.BooleanField(default=False)

    def __str__(self):
        return self.name

class Recipe(models.Model):
    name = models.CharField(max_length=100)
    instructions = models.TextField()
    image = models.ImageField(upload_to='recipe_images/', null=True, blank=True)
    months = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=100, default='main course')
    diet = models.CharField(max_length=100, default='omnivore')
    servings = models.IntegerField(default=1)
    ingredients = models.ManyToManyField(Ingredient, through='RecipeIngredient')
    divisible = models.BooleanField(default=True)
    category = models.CharField(max_length=100, default='general')

    def __str__(self):
        return self.name

class RecipeIngredient(models.Model):
    recipe = models.ForeignKey(Recipe, related_name='recipe_ingredients', on_delete=models.CASCADE)
    ingredient = models.ForeignKey(Ingredient, on_delete=models.CASCADE)
    quantity = models.DecimalField(max_digits=8, decimal_places=2)
    unit = models.CharField(max_length=100, default='unit')
    is_divisible = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.quantity} {self.unit} of {self.ingredient.name} for {self.recipe.name}"

class Combination(models.Model):
    name = models.CharField(max_length=200, blank=True)
    recipes = models.ManyToManyField(Recipe, related_name='combinations')
    waste_score = models.FloatField(default=0.0)
    servings = models.IntegerField(default=1)
    is_divisible = models.BooleanField(default=True)
    seasonality_months = models.TextField(blank=True, null=True)  # JSON array of months when the combination is in season
    created_at = models.DateTimeField(auto_now_add=True)
    # New fields for optimization strategy
    last_calculated = models.DateTimeField(null=True, blank=True)
    ingredient_waste = models.JSONField(default=dict, blank=True)  # Detailed waste information for each ingredient
    is_top_performer = models.BooleanField(default=False)  # Flag for combinations in the top 10% by waste score
    cache_valid = models.BooleanField(default=False)  # Flag to indicate if the cached combination is still valid
    dependent_ingredients = models.JSONField(default=list, blank=True)  # IDs of ingredients that affect this combination
    ingredients_length = models.IntegerField(default=0)  # Number of unique ingredients in the combination
    detailed_ingredients = models.JSONField(default=list, blank=True)  # Flat list of all ingredients with quantities
    recipe_specific_ingredients = models.JSONField(default=dict, blank=True)  # Ingredients grouped by recipe ID
    generation_timestamp = models.DateTimeField(null=True, blank=True)
    diet = models.CharField(max_length=20, default='') # New field for diet type
    
    class Meta:
        indexes = [
            models.Index(fields=['servings']),
            models.Index(fields=['waste_score']),
            models.Index(fields=['is_top_performer']),
            models.Index(fields=['cache_valid']),
            models.Index(fields=['diet']), # New index for diet field
        ]
    
    def __str__(self):
        return self.name or f"Combination #{self.id}"

class AlbertHeijnProduct(models.Model):
    name = models.CharField(max_length=255)  # Max length increased for potentially long product names
    ah_id = models.CharField(max_length=100, unique=True, help_text="Unique identifier from Albert Heijn's website/API")
    url = models.URLField(max_length=500, blank=True, null=True, help_text="Direct URL to the product page on Albert Heijn")
    bought_by_amount = models.IntegerField(default=1, help_text="Amount in which the product is sold (e.g., 1 for unit, 500 for grams)")
    unit_of_measure = models.CharField(max_length=50, blank=True, help_text="e.g., kg, stuk, liter")
    current_price = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    is_on_bonus = models.BooleanField(default=False)
    bonus_price = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Price during bonus period")
    bonus_description = models.CharField(max_length=255, blank=True, null=True)
    last_updated = models.DateTimeField(auto_now_add=True, help_text="Timestamp of the last successful scrape and update")

    def __str__(self):
        return f"{self.name} ({self.ah_id})"

class IngredientAHMapping(models.Model):
    ingredient = models.ForeignKey(Ingredient, on_delete=models.CASCADE, related_name='ah_mappings')
    ah_product = models.ForeignKey(AlbertHeijnProduct, on_delete=models.CASCADE, related_name='ingredient_mappings')
    conversion_factor = models.DecimalField(max_digits=10, decimal_places=4, default=1.0, help_text="Factor to convert AH product unit to recipe ingredient unit (e.g., 1000 if recipe uses grams, AH sells kg)")
    # Optional: Add a priority if an ingredient can map to multiple AH products
    # priority = models.IntegerField(default=1, help_text="Lower number means higher priority")

    class Meta:
        unique_together = ('ingredient', 'ah_product') # Ensures an ingredient can only be mapped to a specific AH product once

    def __str__(self):
        return f"{self.ingredient.name} -> {self.ah_product.name}"

class SavedCombination(models.Model):
    combination = models.ForeignKey(Combination, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Saved: {self.combination.name or f'Combination #{self.combination.id}'}"
