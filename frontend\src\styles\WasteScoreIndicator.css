.waste-score-indicator {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.waste-score-indicator.small {
  font-size: 0.8rem;
}

.waste-score-indicator.medium {
  font-size: 1rem;
}

.waste-score-indicator.large {
  font-size: 1.2rem;
}

.waste-score-header {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin-bottom: 10px;
}

.waste-score-label {
  font-weight: 600;
  color: #555;
  margin-right: 5px;
  white-space: nowrap;
}

.waste-score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.waste-score-circle {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waste-score-indicator.small .waste-score-circle {
  width: 10px;
  height: 10px;
}

.waste-score-indicator.large .waste-score-circle {
  width: 20px;
  height: 20px;
}

/* Outdated indicator styling */
.waste-score-circle.outdated {
  border: 2px solid #ffc107;
  box-shadow: 0 0 0 1px rgba(255, 193, 7, 0.5);
}

.outdated-indicator {
  color: #000;
  font-weight: bold;
  font-size: 12px;
}

.waste-score-indicator.small .outdated-indicator {
  font-size: 10px;
}

.waste-score-indicator.large .outdated-indicator {
  font-size: 14px;
}

.outdated-label {
  color: #ffc107;
  font-style: italic;
  margin-left: 4px;
  font-size: 0.85em;
}

.waste-score-circle.green {
  background-color: #28a745;
  box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.waste-score-circle.lightgreen {
  background-color: #8bc34a;
  box-shadow: 0 0 4px rgba(139, 195, 74, 0.5);
}

.waste-score-circle.yellow {
  background-color: #ffc107;
  box-shadow: 0 0 4px rgba(255, 193, 7, 0.5);
}

.waste-score-circle.orange {
  background-color: #fd7e14;
  box-shadow: 0 0 4px rgba(253, 126, 20, 0.5);
}

.waste-score-circle.red {
  background-color: #dc3545;
  box-shadow: 0 0 4px rgba(220, 53, 69, 0.5);
}

.waste-score-text {
  display: flex;
  flex-direction: column;
}

.waste-score-value {
  font-weight: 600;
  line-height: 1.2;
}

.waste-score-description {
  font-size: 0.8em;
  color: #666;
}

.toggle-btn {
  margin-left: auto;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85em;
  transition: background-color 0.2s;
}

.toggle-btn:hover {
  background-color: #e0e0e0;
}

/* Waste breakdown details */
.waste-breakdown-details {
  margin-top: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.waste-explanation {
  margin-bottom: 15px;
  font-size: 0.9em;
  color: #555;
}

.waste-explanation .calculation-note {
  display: block;
  margin-top: 5px;
  font-style: italic;
  color: #777;
  font-size: 0.9em;
}

.ingredient-waste-list {
  margin-bottom: 15px;
}

.ingredient-waste-list h5 {
  margin-bottom: 8px;
  color: #444;
}

.ingredient-waste-item {
  margin-bottom: 12px;
}

.ingredient-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.ingredient-name {
  font-weight: 500;
}

.ingredient-waste-percentage {
  color: #666;
}

.waste-bar-container {
  margin-top: 5px;
}

.waste-bar-background {
  height: 12px;
  width: 100%;
  background-color: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

.waste-bar-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.waste-bar-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8em;
  color: #666;
  margin-top: 3px;
}

.total-waste-summary {
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
  font-size: 0.9em;
}

.info-icon {
  display: inline-block;
  margin-left: 5px;
  cursor: help;
  color: #6c757d;
  font-size: 0.9em;
}

/* Adjustments for small screens */
@media (max-width: 768px) {
  .waste-score-header {
    flex-wrap: wrap;
  }
  
  .toggle-btn {
    margin-top: 8px;
    margin-left: 0;
    width: 100%;
  }
} 