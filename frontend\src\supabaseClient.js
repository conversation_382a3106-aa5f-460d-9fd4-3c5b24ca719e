import { createClient } from '@supabase/supabase-js';

// IMPORTANT: Replace these with your actual Supabase URL and Anon Key
// It's best practice to store these in environment variables
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  console.error('Supabase URL is not defined. Please set REACT_APP_SUPABASE_URL environment variable.');
}
if (!supabaseAnonKey) {
  console.error('Supabase Anon Key is not defined. Please set REACT_APP_SUPABASE_ANON_KEY environment variable.');
}

// Initialize the Supabase client
// The export will be undefined if the variables are not set,
// which will cause errors downstream, serving as a reminder.
export const supabase = supabaseUrl && supabaseAnonKey ? createClient(supabaseUrl, supabaseAnonKey) : undefined;

if (!supabase) {
  console.error('Failed to initialize Supabase client. Check your environment variables.');
}