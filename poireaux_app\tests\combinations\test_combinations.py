"""
Test script for combination generation algorithm.
Run with: python manage.py shell < poireaux_app/test_combinations.py
"""

import sys
import json
from poireaux_app.models import Recipe, Ingredient, RecipeIngredient, Combination
from poireaux_app.services.combinations.combination_service import (
    generate_recipe_combinations,
    check_recipe_divisibility,
    filter_by_divisibility,
    get_recipe_months,
    check_seasonality_overlap,
    filter_by_seasonality,
    calculate_ingredient_waste,
    calculate_waste_score,
    generate_optimal_combinations
)

# Force stdout to flush after each print statement
def force_print(msg):
    print(msg)
    sys.stdout.flush()

# Test the various functions
def test_algorithm():
    # Get all recipes
    recipes = Recipe.objects.all()
    force_print(f"Found {len(recipes)} recipes")
    
    if len(recipes) < 2:
        force_print("Not enough recipes to test combination generation (need at least 2)")
        return
    
    # Test combination generation
    force_print("\n--- Testing combination generation ---")
    combinations = generate_recipe_combinations(recipes, min_recipes=2, max_recipes=2)
    force_print(f"Generated {len(combinations)} combinations (with 2 recipes)")
    
    # Test divisibility check
    force_print("\n--- Testing divisibility check ---")
    servings = 2
    divisible_combinations = filter_by_divisibility(combinations, servings)
    force_print(f"{len(divisible_combinations)} out of {len(combinations)} combinations are divisible for {servings} servings")
    
    # Test seasonality checks
    force_print("\n--- Testing seasonality check ---")
    for recipe in recipes[:3]:  # Just check the first few recipes
        months = get_recipe_months(recipe)
        force_print(f"Recipe '{recipe.name}' is in season during months: {sorted(months)}")
    
    seasonal_combinations = filter_by_seasonality(divisible_combinations)
    force_print(f"{len(seasonal_combinations)} combinations have seasonality overlap")
    
    # Test waste score calculation
    if seasonal_combinations:
        force_print("\n--- Testing waste score calculation ---")
        combo, months = seasonal_combinations[0]
        waste_score, ingredient_waste = calculate_waste_score(combo, servings)
        force_print(f"Waste score for {[r.name for r in combo]}: {waste_score:.2f}")
        force_print(f"Overlapping months: {sorted(months)}")
        
        # Print waste details for each ingredient
        force_print("\nIngredient waste details:")
        for key, data in ingredient_waste.items():
            ingredient = data['ingredient']
            quantity = data['quantity']
            waste = data.get('waste', 0)
            waste_percent = data.get('waste_percentage', 0)
            force_print(f"  {ingredient.name}: needed={float(quantity):.2f}, waste={float(waste):.2f} ({float(waste_percent):.1f}%)")
    
    # Test full algorithm
    force_print("\n--- Testing complete algorithm ---")
    optimal_combinations = generate_optimal_combinations(
        min_recipes=2, 
        max_recipes=2,
        servings=servings,
        limit=3
    )
    
    force_print(f"Generated {len(optimal_combinations)} optimal combinations")
    
    # Print top 3 combinations
    for i, combo_data in enumerate(optimal_combinations):
        recipe_names = [r.name for r in combo_data['recipes']]
        waste_score = combo_data['waste_score']
        months = combo_data['overlapping_months']
        
        force_print(f"\nCombination #{i+1}:")
        force_print(f"  Recipes: {recipe_names}")
        force_print(f"  Waste Score: {waste_score:.2f}")
        force_print(f"  Seasonal Months: {sorted(months)}")
    
    # Test saving a combination
    if optimal_combinations:
        force_print("\n--- Testing combination saving ---")
        combo_data = optimal_combinations[0]
        # saved_combo = save_combination(combo_data) # Function removed, commenting out usage
        force_print(f"Saved combination with ID {saved_combo.id}: {saved_combo.name}")
        force_print(f"  Waste Score: {saved_combo.waste_score:.2f}")
        force_print(f"  Recipes: {', '.join(r.name for r in saved_combo.recipes.all())}")
        
        # Clean up the test data
        saved_combo.delete()
        force_print("Deleted test combination")

if __name__ == "__main__":
    force_print("Starting combination algorithm tests...")
    test_algorithm()
    force_print("\nTests completed!") 