import React from 'react';
import Tooltip from '../../../../../components/Tooltip';
import '../styles/FindCombinationsButton.css';

const FindCombinationsButton = ({ loading, onFindAndSave, servings, month }) => {
  return (
    <button
      className="find-save-btn"
      onClick={() => onFindAndSave(servings, month, 1)}
      disabled={loading}
    >
      {loading ? 'Processing...' : 'Find & Save Optimal Combinations'}
      <Tooltip content="Generates, calculates waste scores, and saves the best combinations based on selected servings and month.">
        <span className="info-icon">ⓘ</span>
      </Tooltip>
    </button>
  );
};

export default FindCombinationsButton;