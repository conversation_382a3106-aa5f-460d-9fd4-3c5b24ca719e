import React from 'react';
import '../styles/SkeletonRecipeCard.css';

/**
 * SkeletonRecipeCard - Displays a loading placeholder for recipe cards
 * @param {Object} props
 * @param {Object} props.recipe - Original recipe object (used for debugging)
 */
const SkeletonRecipeCard = ({ recipe }) => {
  return (
    <div className="recipe-card skeleton" data-id={recipe?.id}>
      <div className="recipe-header skeleton-header">
        <div className="skeleton-title"></div>
        <div className="skeleton-meta">
          <div className="skeleton-badge"></div>
          <div className="skeleton-badge"></div>
          <div className="skeleton-badge"></div>
        </div>
      </div>
      <div className="recipe-body">
        <div className="skeleton-seasonality"></div>
        <div className="skeleton-content"></div>
      </div>
    </div>
  );
};

export default SkeletonRecipeCard; 