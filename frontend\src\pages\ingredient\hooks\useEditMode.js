import { useState } from 'react';
// No longer needed: import { ENDPOINTS } from '../../../config/api';

export const useEditMode = (ingredients, setIngredients) => {
  const [editMode, setEditMode] = useState({});
  const [editValues, setEditValues] = useState({});
  const [saveStatus, setSaveStatus] = useState({});

  // Toggle edit mode for a specific cell
  const toggleEditMode = (ingredientId, field) => {
    const key = `${ingredientId}-${field}`;
    
    // If we're entering edit mode, initialize the edit value
    if (!editMode[key]) {
      const ingredient = ingredients.find(item => item.id === ingredientId);
      let value = ingredient[field];
      
      // Special handling for divisible_by_int field
      if (field === 'divisible_by_int') {
        try {
          if (typeof value === 'string') {
            value = JSON.parse(value).join(', ');
          } else if (Array.isArray(value)) {
            value = value.join(', ');
          }
        } catch (e) {
          value = '';
        }
      } else if (field === 'needs_calculating') {
        // Convert boolean to string for select input
        value = value ? 'true' : 'false';
      }
      
      // Set the initial edit value
      setEditValues(prev => ({
        ...prev,
        [key]: value === null ? '' : value
      }));
    }
    
    // Toggle the edit mode
    setEditMode(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
    
    // Clear save status when toggling edit mode
    setSaveStatus(prev => ({
      ...prev,
      [key]: null
    }));
  };
  
  // Handle input change for editable cells
  const handleInputChange = (ingredientId, field, value) => {
    const key = `${ingredientId}-${field}`;
    setEditValues(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // Save changes to the database
  const saveChanges = async (ingredientId, field) => {
    const key = `${ingredientId}-${field}`;
    setSaveStatus(prev => ({
      ...prev,
      [key]: 'saving'
    }));
    
    try {
      // Prepare the update data
      let updateData = {};
      
      // Special handling for divisible_by_int field
      if (field === 'divisible_by_int') {
        try {
          // Convert comma-separated string to array of numbers
          const values = editValues[key].split(',').map(x => parseInt(x.trim()));
          if (values.some(isNaN)) {
            throw new Error('Invalid number format');
          }
          updateData[field] = values;
        } catch (e) {
          setSaveStatus(prev => ({
            ...prev,
            [key]: 'error'
          }));
          console.error(`Error processing divisible_by_int: ${e.message}`);
          return;
        }
      } else if (field === 'needs_calculating') {
        // Log the value from editValues before conversion
        const currentValue = editValues[key];
        // Convert boolean or string 'true'/'false' to boolean
        updateData[field] = String(currentValue).trim().toLowerCase() === 'true';
      } else {
        updateData[field] = editValues[key];
      }
      
      // Send update request
      const response = await fetch(`http://localhost:8000/api/ingredients/update/${ingredientId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}`);
      }
      
      // Get updated ingredient data
      const updatedIngredient = await response.json();
      
      // Update the ingredients state with the updated ingredient
      setIngredients(prevIngredients =>
        prevIngredients.map(ingredient => 
          ingredient.id === ingredientId ? updatedIngredient : ingredient
        )
      );
      
      // Exit edit mode immediately
      setEditMode(prev => ({
        ...prev,
        [key]: false
      }));
      setSaveStatus(prev => ({ // Clear save status as we exit edit mode
        ...prev,
        [key]: null
      }));
      
      // Set success status (though it will be immediately cleared by the above)
      // Consider if 'success' status needs to be visible or handled differently if exiting immediately.
      // For now, the main goal is to see if immediate exit fixes the stale display.
      setSaveStatus(prev => ({
        ...prev,
        [key]: 'success'
      }));
      
      return updatedIngredient;
      
    } catch (err) {
      console.error(`Error updating ingredient ${ingredientId}:`, err);
      setSaveStatus(prev => ({
        ...prev,
        [key]: 'error'
      }));
    }
  };
  
  // Cancel editing
  const cancelEdit = (ingredientId, field) => {
    const key = `${ingredientId}-${field}`;
    setEditMode(prev => ({
      ...prev,
      [key]: false
    }));
    setEditValues(prev => ({
      ...prev,
      [key]: undefined
    }));
  };

  return {
    editMode,
    editValues,
    saveStatus,
    toggleEditMode,
    handleInputChange,
    saveChanges,
    cancelEdit
  };
}; 
