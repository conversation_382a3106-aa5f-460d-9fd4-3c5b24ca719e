import React from 'react';

const ServingSizeSelector = ({
  activeServingSize,
  setActiveServingSize,
  isRefreshing,
  handleProcessRecipe
}) => {
  return (
    <div className="serving-size-buttons">
      {[1, 2, 3, 4, "original"].map(servingSize => (
        <button
          key={servingSize}
          className={`serving-button ${activeServingSize === servingSize ? 'active' : ''}`}
          onClick={() => setActiveServingSize(servingSize)}
        >
          {servingSize === "original" ? "Original" : `${servingSize} Serving${servingSize > 1 ? 's' : ''}`}
        </button>
      ))}
      <button
        className="process-recipe-button"
        onClick={handleProcessRecipe}
        disabled={isRefreshing}
      >
        {isRefreshing ? "Processing..." : "Process Recipe"}
      </button>
    </div>
  );
};

export default ServingSizeSelector; 