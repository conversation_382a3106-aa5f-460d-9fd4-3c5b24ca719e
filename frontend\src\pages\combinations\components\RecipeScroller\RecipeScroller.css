/* Recipe <PERSON>er Styles */
.recipe-scroller-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.recipe-scroller {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  touch-action: pan-x pan-y;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.recipe-wheel {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1200px;
}

.recipe-wheel-item {
  position: absolute;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  will-change: transform, opacity;
}

/* Center/Active recipe - slightly smaller for better side card visibility */
.recipe-wheel-item.active {
  z-index: 3;
  opacity: 1;
  transform: translateX(0) scale(0.95); /* Slightly smaller from scale(1) */
  width: 380px; /* Reduced from 400px */
  height: 82%; /* Reduced from 85% */
}

/* Left recipe - closer and more visible */
.recipe-wheel-item.position--1 {
  z-index: 2;
  opacity: 0.75; /* Increased from 0.6 */
  transform: translateX(-250px) scale(0.85); /* Closer and larger - was -300px scale(0.8) */
  width: 340px; /* Increased from 320px */
  height: 70%; /* Increased from 68% */
}

/* Right recipe - closer and more visible */
.recipe-wheel-item.position-1 {
  z-index: 2;
  opacity: 0.75; /* Increased from 0.6 */
  transform: translateX(250px) scale(0.85); /* Closer and larger - was 300px scale(0.8) */
  width: 340px; /* Increased from 320px */
  height: 70%; /* Increased from 68% */
}

/* Two recipes layout - closer positioning */
.recipe-wheel-item.position--0\.5 {
  z-index: 2;
  opacity: 0.8; /* Increased from 0.7 */
  transform: translateX(-180px) scale(0.92); /* Closer and slightly larger */
  width: 370px; /* Increased from 360px */
  height: 78%; /* Increased from 76% */
}

.recipe-wheel-item.position-0\.5 {
  z-index: 2;
  opacity: 0.8; /* Increased from 0.7 */
  transform: translateX(180px) scale(0.92); /* Closer and slightly larger */
  width: 370px; /* Increased from 360px */
  height: 78%; /* Increased from 76% */
}

/* Hover effects */
.recipe-wheel-item:hover:not(.active) {
  opacity: 0.8;
  transform: translateX(-300px) scale(0.85);
}

.recipe-wheel-item.position-1:hover:not(.active) {
  transform: translateX(300px) scale(0.85);
}

.recipe-wheel-item.position--0\.5:hover:not(.active) {
  transform: translateX(-200px) scale(0.95);
}

.recipe-wheel-item.position-0\.5:hover:not(.active) {
  transform: translateX(200px) scale(0.95);
}

/* Minimalist Recipe Navigation Controls */
.recipe-nav-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 4;
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 2px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.recipe-nav-button {
  background: transparent;
  border: 1px solid #e8e8e8;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.8rem;
  cursor: pointer;
  color: #888;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.recipe-nav-button:hover:not(:disabled) {
  background: #f8f8f8;
  color: #555;
  border-color: #ddd;
}

.recipe-nav-button:disabled {
  opacity: 0.2;
  cursor: not-allowed;
}

/* Minimalist Recipe Counter */
.recipe-counter {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.85);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7em;
  color: #666;
  z-index: 4;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 3px;
}

.recipe-counter-label {
  font-weight: 400;
}

.recipe-counter-value {
  font-weight: 500;
  color: #555;
}

/* Empty state */
.recipe-scroller-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-size: 1.1em;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .recipe-scroller-container {
    height: 100%;
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .recipe-scroller {
    touch-action: pan-x;
    height: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile wheel adjustments - reduced front card size, increased side card visibility */
  .recipe-wheel-item.active {
    width: 85%; /* Reduced from 95% */
    height: 75%; /* Reduced from 80% */
    max-width: 320px; /* Reduced from 350px */
  }

  .recipe-wheel-item.position--1,
  .recipe-wheel-item.position-1 {
    width: 70%; /* Reduced from 75% */
    height: 60%; /* Reduced from 64% */
    max-width: 260px; /* Reduced from 280px */
    opacity: 0.7; /* Increased from 0.5 for better visibility */
  }

  .recipe-wheel-item.position--1 {
    transform: translateX(-150px) scale(0.8); /* Closer and larger - was -200px scale(0.75) */
  }

  .recipe-wheel-item.position-1 {
    transform: translateX(150px) scale(0.8); /* Closer and larger - was 200px scale(0.75) */
  }

  .recipe-wheel-item.position--0\.5,
  .recipe-wheel-item.position-0\.5 {
    width: 78%; /* Reduced from 85% */
    height: 68%; /* Reduced from 72% */
    max-width: 290px; /* Reduced from 315px */
    opacity: 0.8; /* Increased from 0.6 for better visibility */
  }

  .recipe-wheel-item.position--0\.5 {
    transform: translateX(-150px) scale(0.8);
  }

  .recipe-wheel-item.position-0\.5 {
    transform: translateX(150px) scale(0.8);
  }

  /* Hide all recipe navigation controls on mobile - finger scrolling only */
  .recipe-nav-controls {
    display: none;
  }

  /* Adjust recipe counter for mobile */
  .recipe-counter {
    bottom: 15px;
    right: 15px;
    padding: 3px 6px;
    font-size: 0.65em;
  }
}
