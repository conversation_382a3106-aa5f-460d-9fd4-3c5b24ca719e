/* combinations.css */

/* Prevent pull-to-refresh and make page completely rigid on mobile */
html, body {
  overscroll-behavior: none; /* Prevent pull-to-refresh */
  touch-action: pan-x pan-y; /* Allow only necessary touch actions */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Prevent any unwanted scrolling behaviors */
.combinations-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px; /* Further reduced from 10px */
  box-sizing: border-box;
  height: calc(100vh - 60px); /* Account for navbar height */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent scrolling */
  position: fixed; /* Make completely rigid */
  top: 60px; /* Account for navbar */
  left: 0;
  right: 0;
  bottom: 0;
}

/* Header styles to match ingredient page */

/* Hide the original title styling since we're using the component */
.combinations-container > h1,
.combinations-container > .subtitle {
  display: none;
}


/* Controls section - Fixed height ratio */
.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 6px; /* Further reduced from 8px */
  margin-bottom: 6px; /* Further reduced from 8px */
  padding: 6px; /* Further reduced from 8px */
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0; /* Don't shrink the controls */
  height: 15%; /* Much smaller - reduced from 25% */
  overflow-y: auto; /* Allow scrolling within controls if needed */
}

/* Control group styles moved to CombinationControlPanel.css to avoid conflicts */

.generate-btn {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  align-self: flex-end;
  margin-left: auto;
  transition: background-color 0.2s ease;
}

.generate-btn:hover {
  background-color: #3e8e41;
}

.generate-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Combinations list - Fixed height ratio */
.combinations-list {
  width: 100%;
  padding: 0;
  margin: 0; /* Removed top margin */
  box-sizing: border-box;
  height: 85%; /* Much larger - increased from 75% */
  overflow: hidden; /* Prevent scrolling */
  flex-shrink: 0; /* Don't shrink the combination list */
}

.combinations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #ecf0f1;
  padding-bottom: 10px;
}

.combinations-header h2 {
  margin: 0;
  text-align: left;
}





.combination-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.combination-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
  /* background-color: #fff0f0; */ /* Removed conflicting background color */
  padding: 10px 15px; /* Adjusted padding for consistency */
  /* border-bottom: 1px solid #e0e0e0; */ /* Removed conflicting border */
}

.card-header h4 { /* Changed selector to h4 for consistency */
  margin: 0;
  font-size: 1.1em; /* Adjusted font size for consistency */
  color: #343a40; /* Adjusted color for consistency */
  font-weight: 500; /* Added font-weight for consistency */
}

.card-body {
  padding: 15px;
}

.waste-score-container {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.recipes-list {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.recipes-list h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1rem;
  color: #333;
}

.recipes-list ul {
  margin: 0;
  padding-left: 20px;
}

.recipes-list li {
  margin-bottom: 5px;
}

.seasonality {
  margin-bottom: 10px;
}

.seasonality h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1rem;
  color: #333;
}

.months-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.month-tag {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 0.8rem;
  font-weight: 500;
}

.no-seasonality {
  color: #666;
  font-style: italic;
  font-size: 0.9rem;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f0f7ff;
  border-top: 1px solid #e0e0e0;
}

.serving-info {
  font-size: 0.9rem;
  color: #666;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.save-btn, .delete-btn {
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.save-btn {
  background-color: #4caf50;
  color: white;
}

.save-btn:hover {
  background-color: #388e3c;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Additional mobile-specific rules to prevent any movement */
  html, body {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: auto; /* Disable momentum scrolling */
  }

  /* Make the page completely rigid on mobile */
  .combinations-container {
    position: fixed;
    top: 60px; /* Account for navbar */
    left: 0;
    right: 0;
    bottom: 20px; /* Add bottom margin to account for mobile browser UI */
    height: calc(100vh - 80px); /* Account for navbar and bottom margin */
    padding: 8px; /* Smaller padding */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    overscroll-behavior: none;
    touch-action: pan-y; /* Allow vertical scrolling only within child elements */
  }

  .controls {
    display: none; /* Hide regular controls on mobile when using side filter panel */
  }

  /* Control group styles handled by CombinationControlPanel.css */

  .generate-btn {
    width: 100%;
    margin-top: 8px; /* Smaller margin */
    padding: 6px 12px; /* Smaller button */
  }

  /* Make the combinations list take full space on mobile */
  .combinations-list {
    height: 100%; /* Take full height since controls are hidden */
    overflow: hidden;
    margin: 0;
    padding-bottom: 10px; /* Add bottom padding to prevent content cutoff */
    flex-shrink: 0; /* Don't shrink */
    overscroll-behavior: contain; /* Prevent overscroll from bubbling up */
    touch-action: pan-y; /* Allow vertical scrolling for TikTok-style navigation */
  }
}
