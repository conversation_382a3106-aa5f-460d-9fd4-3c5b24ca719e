import { useState, useEffect, useMemo } from 'react';

const useRecipeManagement = (combination) => {
  const [currentRecipeIndex, setCurrentRecipeIndex] = useState(0);

  const recipes = useMemo(() => {
    let constructedRecipes = [];
    // Prioritize the new structure: combination.recipes is an array of objects
    if (
      combination?.recipes && Array.isArray(combination.recipes) &&
      combination.recipes.length > 0 &&
      typeof combination.recipes[0] === 'object' &&
      combination.recipes[0].hasOwnProperty('id') && // Check for expected properties
      combination.recipes[0].hasOwnProperty('name') &&
      combination?.recipe_specific_ingredients
    ) {
      // console.log('useRecipeManagement - Constructing recipes from combination.recipes (array of objects)');
      constructedRecipes = combination.recipes.map((recipeData) => {
        const recipeId = recipeData.id;
        const name = recipeData.name;
        let ingredients = [];
        const idStr = recipeId.toString();
        const idKeyPrefixed = `Recipe ID: ${recipeId}`; // Match existing key format for ingredients

        if (combination.recipe_specific_ingredients[idStr]) {
          ingredients = combination.recipe_specific_ingredients[idStr];
        } else if (combination.recipe_specific_ingredients[idKeyPrefixed]) {
          ingredients = combination.recipe_specific_ingredients[idKeyPrefixed];
        } else {
          // console.warn(`No specific ingredients found for recipe ID ${recipeId} (name: "${name}") using keys "${idStr}" or "${idKeyPrefixed}".`);
        }
        return {
          id: recipeId,
          name: name || `Unnamed Recipe (ID: ${recipeId})`,
          specific_ingredients: ingredients || [],
          image: recipeData.image, // API now provides the correct HTTPS Supabase URL in 'image'
          instructions: recipeData.instructions, // Add instructions here
        };
      });
    } else if ( // Fallback to the old recipes_data structure if present
      combination?.recipes_data && Array.isArray(combination.recipes_data) &&
      combination?.recipe_specific_ingredients
    ) {
      // console.log('useRecipeManagement - Constructing recipes from combination.recipes_data');
      constructedRecipes = combination.recipes_data.map((recipeData) => {
        const recipeId = recipeData.id;
        const name = recipeData.name;
        let ingredients = [];
        const idStr = recipeId.toString();
        const idKeyPrefixed = `Recipe ID: ${recipeId}`;

        if (combination.recipe_specific_ingredients[idStr]) {
          ingredients = combination.recipe_specific_ingredients[idStr];
        } else if (combination.recipe_specific_ingredients[idKeyPrefixed]) {
          ingredients = combination.recipe_specific_ingredients[idKeyPrefixed];
        } else {
          // console.warn(`No specific ingredients found for recipe ID ${recipeId} (name: "${name}") using keys "${idStr}" or "${idKeyPrefixed}".`);
        }
        return {
          id: recipeId,
          name: name || `Unnamed Recipe (ID: ${recipeId})`,
          specific_ingredients: ingredients || [],
          image: recipeData.image, // API now provides the correct HTTPS Supabase URL in 'image'
          instructions: recipeData.instructions, // Add instructions here
        };
      });
    }
    // The old path relying on combination.recipe_names is now obsolete and removed.
    // If neither of the above conditions is met, constructedRecipes will remain empty.
    // console.log('useRecipeManagement - combination prop:', combination);
    // console.log('useRecipeManagement - constructed/derived recipes array:', constructedRecipes);
    return constructedRecipes;
  }, [combination]);

  useEffect(() => {
    setCurrentRecipeIndex(0);
    // console.log('useRecipeManagement - useEffect combination:', combination);
  }, [combination]); // Only re-run if the combination object itself changes

  const goToNextRecipe = () => {
    if (recipes.length > 0) {
      setCurrentRecipeIndex(prevIndex => (prevIndex + 1) % recipes.length);
    }
  };

  const goToPreviousRecipe = () => {
    if (recipes.length > 0) {
      setCurrentRecipeIndex(prevIndex => (prevIndex - 1 + recipes.length) % recipes.length);
    }
  };

  const activeRecipe = useMemo(() => {
    return recipes.length > 0 ? recipes[currentRecipeIndex] : null;
  }, [recipes, currentRecipeIndex]);

  // console.log('useRecipeManagement - currentRecipeIndex:', currentRecipeIndex);
  // console.log('useRecipeManagement - activeRecipe:', activeRecipe);

  return {
    recipes,
    currentRecipeIndex,
    activeRecipe,
    goToNextRecipe,
    goToPreviousRecipe,
  };
};

export default useRecipeManagement;
