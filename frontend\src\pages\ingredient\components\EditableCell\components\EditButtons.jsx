import React from 'react';

const EditButtons = ({ ingredient, field, saveChanges, cancelEdit, saveState }) => {
  return (
    <div className="edit-buttons">
      <button
        className="save-button"
        onClick={() => saveChanges(ingredient.id, field)}
        disabled={saveState === 'saving'}
      >
        ✓
      </button>
      <button
        className="cancel-button"
        onClick={() => cancelEdit(ingredient.id, field)}
        disabled={saveState === 'saving'}
      >
        ✗
      </button>
    </div>
  );
};

export default EditButtons;