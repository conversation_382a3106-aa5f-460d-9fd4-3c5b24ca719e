import { useState } from 'react';

export const useCombinationFilters = (initialServings = 1, initialMonth = null, initialDiet = 'vegan') => {
  const [servings, setServingsState] = useState(initialServings);
  const [month, setMonthState] = useState(initialMonth);
  const [diet, setDietState] = useState(initialDiet); // New state for diet

  const setServings = (newServings) => {
    setServingsState(newServings);
    // Note: Resetting currentPage to 1 will be handled by the orchestrator hook
    // when 'servings' changes.
  };
  
  const setMonth = (newMonth) => {
    setMonthState(newMonth);
    // Note: Resetting currentPage to 1 might be needed if month is a primary filter
    // for fetching. This will be handled by the orchestrator.
  };

  const setDiet = (newDiet) => {
    setDietState(newDiet);
  };
 
  return {
    servings,
    month,
    diet, // Return diet state
    setServings,
    setMonth,
    setDiet, // Return diet setter
  };
};
