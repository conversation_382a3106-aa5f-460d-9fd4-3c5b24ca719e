.seasonality-toggle {
    margin: 15px 0;
  }
  
  .month-selection {
    margin: 20px 0;
  }
  
  .months-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 10px;
  }
  
.month-selection {
    margin-top: 15px;
    width: 100%;
    box-sizing: border-box;
}

.month-selection h3 {
    margin-top: 0;
    font-size: 16px;
}

.months-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Change from 4 to 3 columns */
    gap: 8px;
    width: 100%;
    max-width: 100%;
    margin-bottom: 15px;
}

/* Handle smaller screens with fewer columns */
@media (max-width: 460px) {
    .months-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.month-checkbox {
    display: flex;
    align-items: center;
    background-color: #34a75c;
    padding: 6px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
    box-sizing: border-box;
    white-space: nowrap; /* Prevent month names from wrapping */
    overflow: hidden;
    text-overflow: ellipsis; /* Add ellipsis for long month names */
}

.month-checkbox:hover {
    background-color: #e8e8e8;
}

.month-checkbox input {
    margin-right: 4px;
    flex-shrink: 0;
}

.seasonality-toggle {
    margin: 15px 0;
}

.seasonality-toggle label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.seasonality-toggle input {
    margin-right: 8px;
}
  
  .month-checkbox.selected {
    background-color: #d1f0d1;
    border: 1px solid #4CAF50;
  }

.season-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2px;
}

.month-indicator-container {
  position: relative;
  display: inline-block;
}

.month-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #f2f2f2;
  border: 1px solid #ddd;
}

.month-tooltip {
  display: none;
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 10px;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.month-indicator-container:hover .month-tooltip {
  display: block;
}

/* Month-specific colors */
.month-active-1 { background-color: #7FDBFF; border-color: #0074D9; } /* Jan */
.month-active-2 { background-color: #7FDBFF; border-color: #0074D9; } /* Feb */
.month-active-3 { background-color: #2ECC40; border-color: #01FF70; } /* Mar */
.month-active-4 { background-color: #2ECC40; border-color: #01FF70; } /* Apr */
.month-active-5 { background-color: #2ECC40; border-color: #01FF70; } /* May */
.month-active-6 { background-color: #FFDC00; border-color: #FF851B; } /* Jun */
.month-active-7 { background-color: #FFDC00; border-color: #FF851B; } /* Jul */
.month-active-8 { background-color: #FFDC00; border-color: #FF851B; } /* Aug */
.month-active-9 { background-color: #FF4136; border-color: #85144b; } /* Sep */
.month-active-10 { background-color: #FF4136; border-color: #85144b; } /* Oct */
.month-active-11 { background-color: #FF4136; border-color: #85144b; } /* Nov */
.month-active-12 { background-color: #7FDBFF; border-color: #0074D9; } /* Dec */