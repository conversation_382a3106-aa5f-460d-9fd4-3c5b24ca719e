import React, { useState, useRef, useEffect } from 'react';
import { useSwipeable } from 'react-swipeable';
import RecipeScroller from '../RecipeScroller/RecipeScroller';
import './CombinationScroller.css';

const CombinationScroller = ({
  combinations,
  onCardClick
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollerRef = useRef(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Touch handling state for finger-following scrolling
  const [touchState, setTouchState] = useState({
    startY: 0,
    startX: 0,
    currentY: 0,
    currentX: 0,
    isDragging: false,
    startIndex: 0
  });

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'ArrowLeft') {
        goToPrevious();
      } else if (event.key === 'ArrowRight') {
        goToNext();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentIndex, combinations.length]);

  const goToNext = () => {
    if (isTransitioning || combinations.length === 0) return;

    setIsTransitioning(true);
    // Circular scrolling: if at the end, go to the beginning
    setCurrentIndex(prev => (prev + 1) % combinations.length);

    setTimeout(() => setIsTransitioning(false), 300);
  };

  const goToPrevious = () => {
    if (isTransitioning || combinations.length === 0) return;

    setIsTransitioning(true);
    // Circular scrolling: if at the beginning, go to the end
    setCurrentIndex(prev => (prev - 1 + combinations.length) % combinations.length);

    setTimeout(() => setIsTransitioning(false), 300);
  };

  // Touch handlers for finger-following scrolling
  const handleTouchStart = (e) => {
    if (window.innerWidth > 768) return; // Only on mobile

    const touch = e.touches[0];
    setTouchState({
      startY: touch.clientY,
      startX: touch.clientX,
      currentY: touch.clientY,
      currentX: touch.clientX,
      isDragging: true,
      startIndex: currentIndex
    });
    setIsTransitioning(false);
  };

  const handleTouchMove = (e) => {
    if (window.innerWidth > 768 || !touchState.isDragging) return;

    e.preventDefault();
    const touch = e.touches[0];
    const deltaY = touch.clientY - touchState.startY;
    const deltaX = touch.clientX - touchState.startX;

    // Determine if this is a vertical or horizontal swipe
    if (Math.abs(deltaY) > Math.abs(deltaX)) {
      // Vertical swipe for combinations
      setTouchState(prev => ({
        ...prev,
        currentY: touch.clientY
      }));

      // Update transform in real-time
      updateTransformDuringTouch(deltaY);
    }
  };

  const handleTouchEnd = (e) => {
    if (window.innerWidth > 768 || !touchState.isDragging) return;

    const deltaY = touchState.currentY - touchState.startY;
    const threshold = 50; // Minimum distance to trigger change

    setTouchState(prev => ({ ...prev, isDragging: false }));

    if (Math.abs(deltaY) > threshold) {
      if (deltaY > 0) {
        // Swiped down - go to previous
        goToPrevious();
      } else {
        // Swiped up - go to next
        goToNext();
      }
    } else {
      // Snap back to current position
      setIsTransitioning(true);
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  const updateTransformDuringTouch = (deltaY) => {
    if (!scrollerRef.current) return;

    const track = scrollerRef.current.querySelector('.combination-scroller-track');
    if (!track) return;

    const containerHeight = scrollerRef.current.offsetHeight;
    const baseTranslateY = -currentIndex * 100;
    const dragPercentage = (deltaY / containerHeight) * 100;
    const newTranslateY = baseTranslateY + dragPercentage;

    track.style.transition = 'none';
    track.style.transform = `translateY(${newTranslateY}%)`;
  };



  // Reset to first item if current index is out of bounds
  useEffect(() => {
    if (combinations.length > 0 && currentIndex >= combinations.length) {
      setCurrentIndex(0);
    }
  }, [combinations.length, currentIndex]);

  // Swipe handlers for mobile and desktop
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      // Desktop: left swipe goes to next (horizontal)
      // Mobile: left swipe goes to next
      goToNext();
    },
    onSwipedRight: () => {
      // Desktop: right swipe goes to previous (horizontal)
      // Mobile: right swipe goes to previous
      goToPrevious();
    },
    onSwipedUp: () => {
      // Mobile only: up swipe goes to next (vertical)
      if (window.innerWidth <= 768) {
        goToNext();
      }
    },
    onSwipedDown: () => {
      // Mobile only: down swipe goes to previous (vertical)
      if (window.innerWidth <= 768) {
        goToPrevious();
      }
    },
    preventScrollOnSwipe: true,
    trackMouse: true, // Enable mouse tracking for desktop swipe
    delta: window.innerWidth <= 768 ? 80 : 50, // Higher threshold for mobile
    velocity: window.innerWidth <= 768 ? 0.5 : 0.3, // Higher velocity threshold for mobile
    touchEventOptions: { passive: false }, // Prevent default touch behavior
  });

  // Update scroll position when index changes
  useEffect(() => {
    if (scrollerRef.current && !touchState.isDragging) {
      const track = scrollerRef.current.querySelector('.combination-scroller-track');
      if (track) {
        if (window.innerWidth > 768) {
          // Desktop: set track width and horizontal transform
          const containerWidth = scrollerRef.current.offsetWidth;
          const trackWidth = `${combinations.length * 100}%`;
          const translateXPixels = `-${currentIndex * containerWidth}px`;
          track.style.width = trackWidth;
          track.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
          track.style.transform = `translateX(${translateXPixels})`;
          console.log(`Desktop transform: translateX(${translateXPixels}), currentIndex: ${currentIndex}, containerWidth: ${containerWidth}`);
          console.log(`Track width: ${trackWidth}`);
        } else {
          // Mobile: reset width and use vertical transform
          const translateY = `translateY(-${currentIndex * 100}%)`;
          track.style.width = '100%';
          track.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
          track.style.transform = translateY;
        }
      }
    }
  }, [currentIndex, combinations.length, touchState.isDragging]);

  // Handle window resize to update layout
  useEffect(() => {
    const handleResize = () => {
      // Force re-render on resize to update layout
      if (scrollerRef.current) {
        const track = scrollerRef.current.querySelector('.combination-scroller-track');
        if (track) {
          if (window.innerWidth > 768) {
            // Desktop: set track width and horizontal transform
            const containerWidth = scrollerRef.current.offsetWidth;
            const trackWidth = `${combinations.length * 100}%`;
            const translateXPixels = `-${currentIndex * containerWidth}px`;
            track.style.width = trackWidth;
            track.style.transform = `translateX(${translateXPixels})`;
          } else {
            // Mobile: reset width and use vertical transform
            const translateY = `translateY(-${currentIndex * 100}%)`;
            track.style.width = '100%';
            track.style.transform = translateY;
          }
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentIndex, combinations.length]);

  // Use a different approach: apply transform after DOM is ready
  React.useEffect(() => {
    console.log('useEffect: Attempting to apply transform');

    // Use requestAnimationFrame to ensure DOM is fully rendered
    const applyTransform = () => {
      console.log('requestAnimationFrame: Checking for elements');
      const scrollerElement = document.querySelector('.combination-scroller');
      const trackElement = document.querySelector('.combination-scroller-track');

      console.log('Found scroller element:', !!scrollerElement);
      console.log('Found track element:', !!trackElement);

      if (scrollerElement && trackElement && combinations && combinations.length > 0) {
        console.log('All elements found, applying transform');
        console.log('Window width:', window.innerWidth);

        if (window.innerWidth > 768) {
          const containerWidth = scrollerElement.offsetWidth;
          const trackWidth = `${combinations.length * 100}%`;
          const translateXPixels = `-${currentIndex * containerWidth}px`;

          // Set track width
          trackElement.style.width = trackWidth;
          trackElement.style.transform = `translateX(${translateXPixels})`;

          // Also set each item width to ensure they're exactly the container width
          const items = trackElement.querySelectorAll('.combination-scroller-item');
          items.forEach((item, index) => {
            item.style.width = `${containerWidth}px`;
            item.style.minWidth = `${containerWidth}px`;
            item.style.flexShrink = '0';
            console.log(`Set item ${index} width to ${containerWidth}px`);
          });

          console.log(`✅ DOM query desktop transform: translateX(${translateXPixels}), containerWidth: ${containerWidth}`);
          console.log(`Track width: ${trackWidth}, Items count: ${items.length}`);
        } else {
          const translateY = `translateY(-${currentIndex * 100}%)`;
          trackElement.style.width = '100%';
          trackElement.style.transform = translateY;
          console.log(`✅ DOM query mobile transform: ${translateY}`);
        }
      } else {
        console.log('❌ Elements not found or no combinations');
        console.log('scrollerElement:', !!scrollerElement);
        console.log('trackElement:', !!trackElement);
        console.log('combinations:', !!combinations);
        console.log('combinations.length:', combinations?.length);
      }
    };

    requestAnimationFrame(applyTransform);
  }, [combinations, currentIndex]);

  if (!combinations || combinations.length === 0) {
    return (
      <div className="combination-scroller-empty">
        <p>No combinations available</p>
      </div>
    );
  }

  // Debug: Log combination data
  console.log('Combinations array:', combinations);
  console.log('Current index:', currentIndex);
  console.log('Current combination:', combinations[currentIndex]);

  return (
    <div className="combination-scroller-container">


      {/* Desktop Navigation Buttons */}
      <div className="desktop-nav-controls">
        <button
          className="nav-button nav-button-prev"
          onClick={goToPrevious}
          disabled={isTransitioning}
          aria-label="Previous combination"
        >
          &#8249;
        </button>

        <button
          className="nav-button nav-button-next"
          onClick={goToNext}
          disabled={isTransitioning}
          aria-label="Next combination"
        >
          &#8250;
        </button>
      </div>

      {/* Main Scroller */}
      <div
        className="combination-scroller"
        ref={scrollerRef}
        {...(window.innerWidth > 768 ? swipeHandlers : {})}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className="combination-scroller-track">
          {combinations.map((combination, index) => {
            console.log(`Rendering item ${index}:`, combination.id, combination.name);
            return (
              <div
                key={combination.id || index}
                className={`combination-scroller-item ${index === currentIndex ? 'active' : ''}`}
              >
                <RecipeScroller
                  combination={combination}
                  onCardClick={onCardClick}
                />
              </div>
            );
          })}
        </div>
      </div>



      {/* Combination Counter */}
      <div className="combination-counter">
        <span className="combination-counter-label">Combination:</span>
        <span className="combination-counter-value">{currentIndex + 1} / {combinations.length}</span>
      </div>
    </div>
  );
};

export default CombinationScroller;
