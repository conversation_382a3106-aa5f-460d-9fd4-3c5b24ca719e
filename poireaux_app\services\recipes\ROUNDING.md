# Recipe Rounding Implementation

This document explains the recipe ingredient rounding logic, functionality, and implementation details.

## Overview

The recipe rounding system provides automated adjustment of ingredient quantities to more practical and usable measurements. This ensures recipes are easier to follow and ingredients can be measured with standard kitchen tools.

## Data Structure

### Ingredient Measurement Configuration

- Each `Ingredient` model has fields that control how it can be divided:
  - `divisible_by_measurement`: Type of measurement (e.g., 'unit', 'g', 'ml', 'tsp', 'tbsp')
  - `divisible_by_int`: JSON string array of divisors that the ingredient can be divided by (e.g., '[1, 2, 4]')

### Indivisible Ingredients

- Some ingredients like 'bay leaves' and 'Sardines' are marked as inherently indivisible
- These are always rounded to whole numbers with a minimum of 1 if quantity > 0

## Core Functions

### `round_ingredient_quantity(recipe_ingredient)`

The main function that determines the rounded quantity for a single ingredient.

**Input**: 
- `recipe_ingredient`: Either a RecipeIngredient model instance or a dictionary with ingredient data

**Output**:
- Rounded quantity value or None if no rounding needed

**Algorithm**:
1. Extract ingredient data based on input type
2. Check if ingredient is in the indivisible list (bay leaves, etc.)
3. For indivisible ingredients, round to nearest whole number, minimum 1
4. For other ingredients, apply measurement-type specific rounding logic:
   - For 'g'/'ml': Round to nearest multiple of base unit from divisible_by_int
   - For 'unit'/'tsp'/'tbsp'/etc.: Round to nearest valid fraction based on divisible_by_int
5. Return the rounded value only if it differs significantly from the original

### `round_recipe_ingredients(recipe_data)`

Processes all ingredients in a recipe and determines which ones need rounding.

**Input**:
- `recipe_data`: Recipe data dictionary with recipe_ingredients

**Output**:
- List of dictionaries with ingredient IDs and rounded quantities

### `round_all_recipe_ingredients(recipes)`

Processes multiple recipes, skipping those with "(original)" in their name.

**Input**:
- `recipes`: List of recipe dictionaries

**Output**:
- Dictionary mapping recipe IDs to lists of ingredient updates

## API Endpoint

### `POST /api/recipes/ingredients/round/`

Rounds all ingredient quantities across all recipes based on their measurement types.

**Implementation**:
- Defined in `divisibility_views.py` as `round_ingredient_quantities`
- Fetches all recipes if none provided in request 