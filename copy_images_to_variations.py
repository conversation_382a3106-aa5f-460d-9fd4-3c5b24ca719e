#!/usr/bin/env python
"""
<PERSON>ript to copy images from original recipes to their serving variations.
Run this once to update existing recipes that don't have images on their variations.
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'poireaux.settings')
django.setup()

from poireaux_app.models import Recipe

def copy_images_to_variations():
    """Copy images from original recipes to their serving variations"""
    
    # Find all original recipes (those with "(original)" in the name)
    original_recipes = Recipe.objects.filter(name__contains="(original)")
    
    updated_count = 0
    
    for original in original_recipes:
        if original.image:  # Only if the original has an image
            # Get the base name without "(original)"
            base_name = original.name.replace(" (original)", "")
            
            # Find all variations for this recipe
            variations = Recipe.objects.filter(
                name__startswith=base_name,
                name__contains="("
            ).exclude(name__contains="(original)")
            
            # Copy image to each variation
            for variation in variations:
                if not variation.image:  # Only update if variation doesn't have an image
                    variation.image = original.image
                    variation.save()
                    print(f"Copied image to: {variation.name}")
                    updated_count += 1
                else:
                    print(f"Skipped (already has image): {variation.name}")
    
    print(f"\nUpdated {updated_count} recipe variations with images!")

if __name__ == "__main__":
    copy_images_to_variations()
