import React from 'react';
import EditInput from './components/EditInput';
import EditButtons from './components/EditButtons';
import SaveStatusDisplay, { getSaveStatusInfo } from './components/SaveStatusDisplay';
import CellDisplay from './components/CellDisplay';
import './styles/EditableCell.css';

const EditableCell = ({
  ingredient,
  field,
  content,
  typeOptions,
  measurementOptions,
  editMode,
  editValues,
  saveStatus,
  toggleEditMode,
  handleInputChange,
  saveChanges,
  cancelEdit
}) => {
  const key = `${ingredient.id}-${field}`;
  const isEditing = editMode[key];
  const saveState = saveStatus[key];
  
  const { statusClass } = getSaveStatusInfo(saveState);
  
  return (
    <td 
      className={`editable-cell ${isEditing ? 'editing' : ''} ${statusClass}`}
      onClick={() => !isEditing && toggleEditMode(ingredient.id, field)}
    >
      {isEditing ? (
        <div className="edit-controls">
          <EditInput
            ingredient={ingredient}
            field={field}
            editValues={editValues}
            handleInputChange={handleInputChange}
            typeOptions={typeOptions}
            measurementOptions={measurementOptions}
          />
          <EditButtons
            ingredient={ingredient}
            field={field}
            saveChanges={saveChanges}
            cancelEdit={cancelEdit}
            saveState={saveState}
          />
          <SaveStatusDisplay saveState={saveState} />
        </div>
      ) : (
        <CellDisplay content={content} />
      )}
    </td>
  );
};

export default EditableCell;