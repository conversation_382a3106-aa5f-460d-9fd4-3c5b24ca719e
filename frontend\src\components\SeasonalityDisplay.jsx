import React, { useMemo } from 'react';
import '../styles/SeasonalityDisplay.css';

/**
 * Component that displays a visual representation of recipe seasonality by month
 * 
 * @param {Object} props
 * @param {Array} props.seasonalMonths - Array of month numbers (1-12) when the item is in season
 * @param {string} props.size - Size of the display ('small', 'medium', 'large')
 * @param {boolean} props.highlightCurrent - Whether to highlight the current month
 * @param {string} props.label - Optional label for the seasonality display
 */
function SeasonalityDisplay({ 
  seasonalMonths = [], 
  size = 'medium', 
  highlightCurrent = true,
  label = 'Seasonal in:'
}) {
  // Enhanced error handling - ensure seasonalMonths is always an array before converting to Set
  const safeSeasonalMonths = useMemo(() => {
    // Handle null or undefined
    if (!seasonalMonths) return [];
    
    // Handle if it's already an array
    if (Array.isArray(seasonalMonths)) return seasonalMonths;
    
    // Handle if it's a string (potentially JSON)
    if (typeof seasonalMonths === 'string') {
      try {
        const parsed = JSON.parse(seasonalMonths);
        return Array.isArray(parsed) ? parsed : [];
      } catch (err) {
        console.error('Error parsing seasonal months string:', err);
        return [];
      }
    }
    
    // For any other type, return empty array
    console.warn('SeasonalityDisplay received invalid seasonalMonths:', seasonalMonths);
    return [];
  }, [seasonalMonths]);
  
  // Convert seasonalMonths to a Set for faster lookup
  const seasonalMonthsSet = useMemo(() => new Set(safeSeasonalMonths), [safeSeasonalMonths]);
  
  // Get current month (1-12)
  const currentMonth = useMemo(() => new Date().getMonth() + 1, []);
  
  // Month data for display
  const months = useMemo(() => [
    { num: 1, name: 'Jan', fullName: 'January' },
    { num: 2, name: 'Feb', fullName: 'February' },
    { num: 3, name: 'Mar', fullName: 'March' },
    { num: 4, name: 'Apr', fullName: 'April' },
    { num: 5, name: 'May', fullName: 'May' },
    { num: 6, name: 'Jun', fullName: 'June' },
    { num: 7, name: 'Jul', fullName: 'July' },
    { num: 8, name: 'Aug', fullName: 'August' },
    { num: 9, name: 'Sep', fullName: 'September' },
    { num: 10, name: 'Oct', fullName: 'October' },
    { num: 11, name: 'Nov', fullName: 'November' },
    { num: 12, name: 'Dec', fullName: 'December' },
  ], []);
  
  // Group months into seasons
  const seasons = useMemo(() => [
    { name: 'Winter', months: [12, 1, 2] },
    { name: 'Spring', months: [3, 4, 5] },
    { name: 'Summer', months: [6, 7, 8] },
    { name: 'Fall', months: [9, 10, 11] }
  ], []);
  
  return (
    <div className={`seasonality-display ${size}`}>
      {label && <div className="seasonality-label">{label}</div>}
      
      <div className="seasonality-grid">
        {months.map((month) => {
          const isInSeason = seasonalMonthsSet.has(month.num);
          const isCurrent = highlightCurrent && month.num === currentMonth;
          
          // Determine season for color
          const season = seasons.find(s => s.months.includes(month.num));
          const seasonClass = season ? season.name.toLowerCase() : '';
          
          return (
            <div 
              key={month.num}
              className={`month-box ${isInSeason ? 'in-season' : 'off-season'} ${seasonClass} ${isCurrent ? 'current' : ''}`}
              title={`${month.fullName}${isInSeason ? ' - In Season' : ' - Not in Season'}`}
            >
              <span className="month-abbr">{month.name}</span>
            </div>
          );
        })}
      </div>
      
      <div className="season-legend">
        {seasons.map(season => (
          <div key={season.name} className="season-legend-item">
            <div className={`season-color-box ${season.name.toLowerCase()}`}></div>
            <span>{season.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default SeasonalityDisplay; 