import React from 'react';
import './styles/CardRecipeScroller.css';

const CardRecipeScroller = ({ recipes, currentRecipeIndex, goToPreviousRecipe, goToNextRecipe }) => {
  if (!recipes || recipes.length <= 1) {
    return null; // Don't render if there's 0 or 1 recipe
  }

  return (
    <div className="recipe-navigation">
      <button onClick={goToPreviousRecipe} disabled={recipes.length <= 1}>
        {'< Previous Recipe'}
      </button>
      <span className="recipe-navigation-status">
        Viewing Recipe {currentRecipeIndex + 1} of {recipes.length}
      </span>
      <button onClick={goToNextRecipe} disabled={recipes.length <= 1}>
        {'Next Recipe >'}
      </button>
    </div>
  );
};

export default CardRecipeScroller;
