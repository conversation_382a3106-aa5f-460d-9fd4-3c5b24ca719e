/**
 * Feature flags for the application
 * Use these to enable/disable features during development and deployment
 */

export const FEATURES = {
  // Fast loading endpoint - disable until backend is deployed
  FAST_LOADING: false, // Set to true after backend deployment
  
  // Other feature flags can be added here
  BACKGROUND_LOADING_INDICATOR: true,
  PROGRESSIVE_ENHANCEMENT: false, // Will be enabled when FAST_LOADING is true
  
  // Debug flags
  VERBOSE_LOGGING: process.env.NODE_ENV === 'development',
  PERFORMANCE_MONITORING: true,
};

// Helper function to check if a feature is enabled
export const isFeatureEnabled = (featureName) => {
  return FEATURES[featureName] === true;
};

// Helper function to enable fast loading after backend deployment
export const enableFastLoading = () => {
  FEATURES.FAST_LOADING = true;
  FEATURES.PROGRESSIVE_ENHANCEMENT = true;
  console.log('🚀 Fast loading enabled!');
};

export default FEATURES;
