import { useState, useEffect, useCallback } from 'react';
import { ENDPOINTS } from '../config/api';

/**
 * Shared hook for fetching ingredients data from the API
 * @param {boolean} autoFetch - Whether to fetch ingredients automatically on mount
 * @returns {Object} Ingredients data and fetch function
 */
export const useIngredients = (autoFetch = true) => {
  const [ingredients, setIngredients] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(null);
  
  // Cache duration in milliseconds (5 minutes)
  const CACHE_DURATION = 5 * 60 * 1000;
  
  // Check if cached data is still valid
  const isCacheValid = useCallback(() => {
    if (!lastFetchTime) return false;
    const now = new Date().getTime();
    return (now - lastFetchTime) < CACHE_DURATION;
  }, [lastFetchTime, CACHE_DURATION]);
  
  // Get cached ingredients from localStorage
  const getCachedIngredients = useCallback((cacheKey) => {
    try {
      const cachedData = localStorage.getItem(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
    } catch (err) {
      console.error('Failed to parse cached ingredients:', err);
    }
    return null;
  }, []);
  
  // Save ingredients to localStorage cache
  const cacheIngredients = useCallback((data, cacheKey) => {
    try {
      localStorage.setItem(cacheKey, JSON.stringify(data));
      setLastFetchTime(new Date().getTime());
    } catch (err) {
      console.error('Failed to cache ingredients:', err);
    }
  }, []);
  
  // Fetch ingredients from the API
  const fetchIngredients = useCallback(async (forceRefresh = false, options = {}) => {
    const { detailed = false, type = null } = options;
    
    // Form cache key based on options
    const cacheKey = `ingredientsCache_${detailed ? 'detailed' : 'basic'}_${type || 'all'}`;
    
    // Return cached data if it's valid and we're not forcing a refresh
    if (!forceRefresh && isCacheValid()) {
      const cachedData = getCachedIngredients(cacheKey);
      if (cachedData && cachedData.length > 0) {
        console.log(`Using cached ingredients data (${cacheKey})`);
        setIngredients(cachedData);
        return cachedData;
      }
    }
    
    setIsLoading(true);
    setError(null);
    
    // Log fetch start time
    const startTime = performance.now();
    
    // Build URL with query parameters
    let url = ENDPOINTS.INGREDIENTS.GET;
    const params = new URLSearchParams();
    
    if (detailed) {
      params.append('detailed', 'true');
    }
    
    if (type) {
      params.append('type', type);
    }
    
    // Add params to URL if any were set
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ingredients: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Log fetch end time and calculate duration
      const endTime = performance.now();
      console.log(`Ingredients fetched in ${(endTime - startTime).toFixed(2)}ms`);
      
      setIngredients(data);
      cacheIngredients(data, cacheKey);
      return data;
    } catch (err) {
      console.error("Failed to fetch ingredients:", err);
      setError(err.message);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [isCacheValid, getCachedIngredients, cacheIngredients]);
  
  // Fetch ingredients on component mount if autoFetch is true
  useEffect(() => {
    if (autoFetch) {
      // Define the default cache key
      const defaultCacheKey = 'ingredientsCache_basic_all';
      
      // Try to load from cache first
      const cachedData = getCachedIngredients(defaultCacheKey);
      if (cachedData && cachedData.length > 0 && isCacheValid()) {
        console.log('Using cached ingredients data on mount');
        setIngredients(cachedData);
      } else {
        // Fetch fresh data if cache is invalid or empty
        fetchIngredients(false);
      }
    }
  }, [autoFetch, fetchIngredients, getCachedIngredients, isCacheValid]);
  
  return {
    ingredients,
    setIngredients,
    isLoading,
    error,
    fetchIngredients
  };
};

export default useIngredients;
