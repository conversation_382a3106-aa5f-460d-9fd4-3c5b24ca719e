import React from 'react';
import { useIngredientAdder, MONTH<PERSON>, MEASUREMENT_UNITS, INGREDIENT_TYPES, BOUGHT_BY_UNITS } from './IngredientAdder.js';
import './IngredientAdder.css';
import '../../../../styles/MonthSelection.css'; // Keep if still used for other elements or globally
import '../../../ingredient/styles/index.css';   // Keep if still used for other elements or globally


const IngredientAdder = () => {
  const {
    name,
    setName,
    seasons,
    handleSeasonChange,
    divisibleByInteger,
    setDivisibleByInteger,
    divisibleByMeasurement,
    setDivisibleByMeasurement,
    type,
    setType,
    needsCalculating,
    setNeedsCalculating,
    boughtByAmount,
    setBoughtByAmount,
    boughtBy,
    setBoughtBy,
    feedbackMessage,
    handleSubmit,
  } = useIngredientAdder();

  return (
    <div className="ingredient-adder-container">
      <h3>Add New Ingredient</h3>
      {feedbackMessage && <p className={feedbackMessage.startsWith('Error') || feedbackMessage.startsWith('Failed') ? 'error-message' : 'success-message'}>{feedbackMessage}</p>}
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="name">Name:</label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
          />
        </div>

        <div>
          <label>Seasons:</label>
          <div className="month-buttons-grid">
            {MONTHS.map(month => (
              <button
                type="button"
                key={month}
                className={`season-toggle-button ${seasons[month] ? 'selected' : ''}`}
                onClick={() => handleSeasonChange(month)}
              >
                {month.charAt(0).toUpperCase() + month.slice(1)}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label htmlFor="divisibleByInteger">Divisible By Integer (e.g., "1,2,4"):</label>
          <input
            type="text"
            id="divisibleByInteger"
            value={divisibleByInteger}
            onChange={(e) => setDivisibleByInteger(e.target.value)}
          />
        </div>

        <div>
          <label htmlFor="divisibleByMeasurement">Divisible By Measurement:</label>
          <select
            id="divisibleByMeasurement"
            value={divisibleByMeasurement}
            onChange={(e) => setDivisibleByMeasurement(e.target.value)}
          >
            {MEASUREMENT_UNITS.map(unit => (
              <option key={unit} value={unit}>{unit}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="type">Type:</label>
          <select
            id="type"
            value={type}
            onChange={(e) => setType(e.target.value)}
          >
            {INGREDIENT_TYPES.map(ingType => (
              <option key={ingType} value={ingType}>{ingType.charAt(0).toUpperCase() + ingType.slice(1)}</option>
            ))}
          </select>
        </div>

        <div className="needs-calculating-container">
          <label htmlFor="needsCalculatingToggle">Needs Calculating:</label>
          <button
            type="button"
            id="needsCalculatingToggle"
            className={`needs-calculating-toggle-button ${needsCalculating ? 'selected' : ''}`}
            onClick={() => setNeedsCalculating(!needsCalculating)}
          >
            {needsCalculating ? 'Yes' : 'No'}
          </button>
        </div>

        {needsCalculating && (
          <>
            <div>
              <label htmlFor="boughtByAmount">Bought By Amount (comma-separated for multiple sizes):</label>
              <input
                type="text"
                id="boughtByAmount"
                value={boughtByAmount}
                onChange={(e) => setBoughtByAmount(e.target.value)}
                placeholder="Enter amounts (e.g., 250,400 for different can sizes)"
              />
            </div>

            <div>
              <label htmlFor="boughtBy">Bought By Unit:</label>
              <select
                id="boughtBy"
                value={boughtBy}
                onChange={(e) => setBoughtBy(e.target.value)}
              >
                {BOUGHT_BY_UNITS.map(unit => (
                  <option key={unit} value={unit}>{unit.charAt(0).toUpperCase() + unit.slice(1)}</option>
                ))}
              </select>
            </div>
          </>
        )}

        <button type="submit">Add Ingredient</button>
      </form>
    </div>
  );
};

export default IngredientAdder;
