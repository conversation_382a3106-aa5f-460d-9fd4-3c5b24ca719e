# Generated by Django 5.1.7 on 2025-05-28 13:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('poireaux_app', '0021_recipe_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlbertHeijnProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('ah_id', models.CharField(help_text="Unique identifier from <PERSON>'s website/API", max_length=100, unique=True)),
                ('url', models.URLField(blank=True, help_text='Direct URL to the product page on <PERSON>', max_length=500, null=True)),
                ('unit_of_measure', models.CharField(blank=True, help_text='e.g., kg, stuk, liter', max_length=50)),
                ('current_price', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('is_on_bonus', models.<PERSON>olean<PERSON>ield(default=False)),
                ('bonus_description', models.CharField(blank=True, max_length=255, null=True)),
                ('last_updated', models.DateTimeField(auto_now_add=True, help_text='Timestamp of the last successful scrape and update')),
            ],
        ),
        migrations.CreateModel(
            name='IngredientAHMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conversion_factor', models.DecimalField(decimal_places=4, default=1.0, help_text='Factor to convert AH product unit to recipe ingredient unit (e.g., 1000 if recipe uses grams, AH sells kg)', max_digits=10)),
                ('ah_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ingredient_mappings', to='poireaux_app.albertheijnproduct')),
                ('ingredient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ah_mappings', to='poireaux_app.ingredient')),
            ],
            options={
                'unique_together': {('ingredient', 'ah_product')},
            },
        ),
    ]
