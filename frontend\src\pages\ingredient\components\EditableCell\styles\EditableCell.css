/* Editable cell styles */
.editable-cell {
  position: relative;
  cursor: pointer;
}

.editable-cell:hover .edit-icon {
  opacity: 1;
}

.edit-icon {
  opacity: 0;
  margin-left: 5px;
  color: #999;
  transition: opacity 0.2s;
  font-size: 12px;
}

.editable-cell.editing {
  padding: 0;
}

.edit-controls {
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.edit-controls input,
.edit-controls select {
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

.edit-buttons {
  display: flex;
  gap: 5px;
}

.save-button,
.cancel-button {
  padding: 2px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.save-button {
  background-color: #4CAF50;
  color: white;
}

.cancel-button {
  background-color: #f44336;
  color: white;
}

.save-status {
  font-size: 12px;
  margin-top: 5px;
  text-align: center;
}

.status-saving {
  background-color: #FFF9C4;
}

.status-success {
  background-color: #E8F5E9;
}

.status-error {
  background-color: #FFEBEE;
}