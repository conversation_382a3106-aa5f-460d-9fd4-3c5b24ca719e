.env 
.venv/
.venv/Lib/
__pycache__/
poireaux_app/__pycache__/
# Dependencies
frontend/node_modules/
frontend/.pnp
frontend/.pnp.js

# Testing
frontend/coverage

# Production
frontend/build

# Misc
frontend/.DS_Store
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/ 
