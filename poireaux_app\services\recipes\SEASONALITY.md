# Recipe Seasonality Implementation

This document explains the recipe seasonality functionality, components, and implementation details.

## Overview

The recipe seasonality system determines when recipes are in season based on the seasonality of their ingredients. This helps users find recipes that use ingredients that are currently in season, promoting sustainable cooking practices and better-tasting meals.

## Data Structure

### Ingredient Seasonality

- Each `Ingredient` model has fields that control its seasonality:
  - `has_seasonality`: Boolean flag indicating if the ingredient has seasonal availability
  - Monthly fields: `january`, `february`, `march`, etc. - Boolean flags for each month indicating if the ingredient is in season

### Recipe Seasonality

- Recipe seasonality is derived from its ingredients' seasonality
- The calculated seasonality is stored in the `months` field of the `Recipe` model as a JSON string
- A recipe is considered in season for a month if all its seasonal ingredients are in season for that month

## Components

### Backend Components

1. **Recipe Seasonality Service** (`recipe_seasonality_service.py`):
   - Core functions for calculating and updating recipe seasonality
   - Handles batch processing for efficiency

2. **API Endpoints** (`seasonality_views.py`):
   - REST endpoints for accessing and updating seasonality data
   - Supports both individual and batch operations

### Frontend Components

1. **Shared Seasonality Hook** (`useSeasonality.js`):
   - Provides utility functions for handling seasonality data
   - Formats data for display and handles month conversions

2. **Recipe Seasonality Hook** (`useRecipeSeasonality.js`):
   - Manages recipe seasonality with efficient batch loading
   - Maintains a localStorage cache for better performance
   - Provides synchronous interface for components

3. **Generate Servings Handler** (`handleGenerateServings.js`):
   - Integrates with the seasonality system when generating serving variations
   - Calls `updateAllRecipeSeasonality` after creating new recipe variations

## Core Functions

### Backend Functions

#### `calculate_recipe_seasonality(ingredients, recipe_id, recipe_name)`

The main function that determines a recipe's seasonality based on its ingredients.

**Input**: 
- `ingredients`: List of ingredient dictionaries with seasonality data
- `recipe_id`: Optional recipe ID for logging
- `recipe_name`: Optional recipe name for logging

**Output**:
- Dictionary mapping month names to boolean availability

**Algorithm**:
1. Start with all months set to `True` (in season)
2. For each ingredient with seasonality data:
   - Check if the ingredient has any months marked as in season
   - If yes, perform a logical AND with the recipe's seasonality for each month
3. Return the resulting seasonality map

#### `format_seasonality(seasonality_data)`

Formats raw seasonality data for frontend display.

**Input**:
- `seasonality_data`: Dictionary mapping month names to boolean availability

**Output**:
- Formatted object with:
  - `hasSeasonality`: Boolean indicating if the recipe has any seasonal months
  - `data`: Array of month objects with name, number, and active status
  - `seasonalMonths`: Array of month numbers (1-12) that are in season

#### `calculate_and_update_recipe_seasonality(recipe, update_db)`

Calculates and optionally updates seasonality for a specific recipe.

**Input**:
- `recipe`: Recipe object
- `update_db`: Boolean flag to control database updates

**Output**:
- Tuple of (raw_seasonality_data, formatted_seasonality_data)

#### `update_all_recipe_seasonality()`

Calculates and updates seasonality for all recipes in the database.

**Input**: None

**Output**:
- Dictionary mapping recipe IDs to their updated seasonality data

### Frontend Functions

#### `useRecipeSeasonality()`

Custom hook for managing recipe seasonality with batch loading and caching.

**Returns**:
- `isLoading`: Boolean indicating if a batch request is in progress
- `error`: Any error that occurred during loading
- `formatSeasonality`: Function to get seasonality for a recipe (synchronous)
- `preloadSeasonality`: Function to preload seasonality for multiple recipes
- `resetCache`: Function to clear the seasonality cache
- `updateAllRecipeSeasonality`: Function to update seasonality for all recipes

#### `updateAllRecipeSeasonality()`

The newly added function that triggers a backend update of all recipe seasonality.

**Algorithm**:
1. Make a POST request to the `UPDATE_ALL` endpoint
2. Process the response to get the number of recipes updated
3. Clear the local cache since data has been updated
4. Return the number of recipes updated

## API Endpoints

### `POST /recipes/seasonality/calculate/`

Calculate a recipe's seasonality based on provided ingredients.

**Request Body**:
- `ingredients`: List of ingredient objects with seasonality data
- `recipe_id`: (Optional) Recipe ID for database updates
- `update_db`: (Optional) Boolean to control database updates

**Response**:
- `raw`: Raw seasonality data (month name to boolean mapping)
- `formatted`: Formatted seasonality data for frontend display

### `POST /recipes/seasonality/update-all/`

Calculate and update seasonality for all recipes in the database.

**Response**:
- `updated_count`: Number of recipes updated
- `message`: Success message

### `GET /recipes/seasonality/{recipe_id}/`

Get the seasonality data for a specific recipe.

**Response**:
- Formatted seasonality data for the recipe

### `POST /recipes/seasonality/batch/`

Get seasonality data for multiple recipes in a single request.

**Request Body**:
- `recipe_ids`: List of recipe IDs to fetch seasonality data for

**Response**:
- Dictionary mapping recipe IDs to their seasonality data

## Data Flow

1. **Ingredient Seasonality**: Each ingredient has monthly seasonality flags set in the database
2. **Recipe Seasonality Calculation**: 
   - When a recipe is created or ingredients are updated, the seasonality is calculated
   - A recipe is in season for a month if all its seasonal ingredients are in season for that month
3. **Storage**: The calculated seasonality is stored as a JSON string in the `months` field
4. **Frontend Access**:
   - The frontend fetches seasonality data using the API endpoints
   - Data is cached in localStorage for better performance
   - Components use the `useRecipeSeasonality` hook to access this data

## Generate Serving Variations Integration

The "Generate Serving Variations" button functionality now includes updating seasonality:

1. When the button is clicked, it calls the backend to generate serving variations
2. After successful creation of variations, it calls `updateAllRecipeSeasonality()`
3. This ensures all recipes, including the newly created variations, have up-to-date seasonality data
4. The user is notified of both the creation of variations and the seasonality update

## Performance Considerations

1. **Batch Processing**: Both frontend and backend use batch processing for better performance
2. **Caching**: 
   - Frontend maintains a localStorage cache with version control and expiry
   - API responses are cached in memory during a session
3. **Efficient Loading**: 
   - The `useRecipeSeasonality` hook provides a synchronous interface that returns cached data immediately
   - It automatically queues recipes for loading when not in cache