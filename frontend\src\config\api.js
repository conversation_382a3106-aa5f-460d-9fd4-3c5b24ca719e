/**
 * API configuration constants
 * Central location for all API-related constants
 */

let activeApiBaseUrl;

// Check if running on localhost or 127.0.0.1 (covers Firebase emulator and local dev server)
const isLocalEnvironment = typeof window !== 'undefined' && (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1");

if (isLocalEnvironment) {
  // Use the local API URL defined in .env.development or fallback
  activeApiBaseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';
} else {
  // Production environment - use the Cloud Run URL
  activeApiBaseUrl = 'https://poireaux-749928798411.europe-west1.run.app/api';
}

export const API_BASE_URL = activeApiBaseUrl;

// Common API endpoints
export const ENDPOINTS = {
  // Ingredients
  INGREDIENTS: {
    GET: `${API_BASE_URL}/ingredients/get/`,
    UPDATE: (id) => `${API_BASE_URL}/ingredients/update/${id}/`
  },
  
  // Recipes
  RECIPES: {
    GET: `${API_BASE_URL}/recipes/get/`,
    DELETE: (id) => `${API_BASE_URL}/recipes/delete/${id}/`,
    GENERATE_SERVINGS: `${API_BASE_URL}/recipes/generate-servings/`,
    SEASONALITY: {
      CALCULATE: `${API_BASE_URL}/recipes/seasonality/calculate/`,
      GET: (id) => `${API_BASE_URL}/recipes/seasonality/${id}/`,
      UPDATE_ALL: `${API_BASE_URL}/recipes/seasonality/update-all/`,
      BATCH: `${API_BASE_URL}/recipes/seasonality/batch/`
    },
    DIVISIBILITY: {
      CHECK_ALL: `${API_BASE_URL}/recipes/divisibility/check-all/`
    },
    PROCESS: {
      CHECK_ALL: `${API_BASE_URL}/recipes/process/`
    }
  },
  
  // Combinations
  COMBINATIONS: {
    GET: `${API_BASE_URL}/combinations/get/`,
    GENERATE: `${API_BASE_URL}/combinations/generate/`,
    DELETE: (id) => `${API_BASE_URL}/combinations/delete/${id}/`
  },

  // Saved Combinations
  SAVED_COMBINATIONS: {
    GET_ALL: `${API_BASE_URL}/saved-combinations/`,
    SAVE: `${API_BASE_URL}/saved-combinations/`,
    DELETE: (id) => `${API_BASE_URL}/saved-combinations/${id}/`
  },
  
  // Waste Score
  WASTE_SCORE: {
    CALCULATE: `${API_BASE_URL}/waste-score/calculate/`
  }
};

const apiConfig = {
  API_BASE_URL,
  ENDPOINTS
};

export default apiConfig;
