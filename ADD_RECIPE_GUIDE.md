# Adding Recipes to the Poireaux Database

This document provides a detailed guide for adding recipes to the Poireaux database using the Supabase MCP (Model Context Protocol). Follow these steps carefully to ensure consistency and accuracy in the database while minimizing unnecessary API calls.

## Overview

When adding a recipe to the database, you will:

1.  **Analyze the complete recipe first** to identify all ingredients needed.
2.  Check which ingredients already exist in the database and which need to be added.
3.  Add all missing ingredients at once to minimize API calls.
4.  Add the recipe to the `poireaux_app_recipe` table.
5.  Add its ingredients to the `poireaux_app_recipeingredient` table.
6.  Use the Supabase MCP to interact with the database.
7.  Ensure all ingredient measurements align with the database's `divisible_by_measurement` field.

## Important Database Information

### Correct Table Names
Always use the full table names with prefixes:
- `poireaux_app_ingredient` (NOT just "ingredient")
- `poireaux_app_recipe` (NOT just "recipe")
- `poireaux_app_recipeingredient` (NOT just "recipe_ingredients")

Using incorrect table names will result in errors and wasted API calls.

### Database Schema Information

#### Ingredient Table (`poireaux_app_ingredient`)
Required fields:
- `name`: The name of the ingredient
- `divisible_by_measurement`: The unit of measurement (e.g., 'g', 'tsp', 'unit')
- `divisible_by_int`: Array of allowed integer divisions (e.g., '[1, 2, 4]')
- `type`: Type of ingredient (must be one of: 'meat', 'preserve', 'vegetable', 'grain', 'herb', 'dairy', 'spice', 'oil')
- `bought_by`: How the ingredient is purchased (e.g., 'unit', 'jar', 'can', 'bottle')
- `bought_by_amount`: Amount in the purchased form
- `has_seasonality`: Boolean indicating if the ingredient has seasonality
- Monthly fields (`january` through `december`): Boolean fields for each month

#### Recipe Table (`poireaux_app_recipe`)
Required fields:
- `name`: The name of the recipe
- `instructions`: The preparation instructions
- `months`: Comma-separated list of months when the recipe is suitable (e.g., '1,2,3,4,5,6,7,8,9,10,11,12')
- `type`: Type of recipe (e.g., 'pasta', 'soup', 'salad')
- `diet`: Diet category (e.g., 'omnivore', 'vegetarian', 'vegan')
- `servings`: Number of servings
- `divisible`: Boolean indicating if the recipe can be divided

Note: The fields `description` and `cook_time` mentioned in previous versions of this guide do not exist in the current schema.

#### Recipe Ingredient Table (`poireaux_app_recipeingredient`)
Required fields:
- `recipe_id`: ID of the recipe
- `ingredient_id`: ID of the ingredient
- `quantity`: Amount of the ingredient
- `unit`: Unit of measurement (must match the ingredient's `divisible_by_measurement`)
- `is_divisible`: Boolean indicating if the ingredient quantity can be divided

## Step-by-Step Guide

### 1. Complete Recipe Analysis

Before making any database changes, thoroughly analyze the recipe to create a complete list of all ingredients with their quantities and measurements. This prevents premature task completion and reduces unnecessary API calls.

**Example Recipe Analysis:**
```
Recipe: Pasta with Tomato Sauce
Ingredients:
1. 500g pasta
2. 400g canned tomatoes
3. 2 tbsp olive oil
4. 1 onion
5. 2 cloves garlic
6. 1 tsp salt
7. 1 tsp sugar
8. Fresh basil
```

### 2. Checking All Existing Ingredients At Once

Retrieve the complete list of ingredients from the database in a single query. Since the ingredient database isn't very large, fetching all ingredients at once is more efficient than individual searches.

**Example Query to Get All Ingredients:**
```sql
SELECT id, name, divisible_by_measurement, divisible_by_int, type, bought_by, bought_by_amount
FROM poireaux_app_ingredient
ORDER BY name;
```

Store this information for quick reference when processing ingredients. This upfront fetching approach is much more efficient than making individual queries for each ingredient.

### 3. Planning Required Additions

After comparing your recipe ingredient list with existing database ingredients, create a clear plan of what needs to be added. **Crucially, check if a sufficiently similar ingredient already exists before deciding to add a new one (see "Common Mistakes").**

**Example Planning Table:**
```
Ingredient       | Exists in DB? | ID  | Action Required
----------------|--------------|-----|----------------
Pasta           | Yes          | 11  | None
Canned Tomatoes | Yes          | 7   | None
Olive Oil       | Yes          | 3   | None
Onion           | Yes          | 4   | None
Garlic          | Yes          | 2   | None
Salt            | Yes          | 90  | None
Sugar           | Yes          | 10  | None
Fresh Basil     | Yes          | 8   | None
```

If any ingredients are missing, plan to add them all at once:

**Example Missing Ingredients Plan:**
```
Missing Ingredients:
1. Rose Harissa - Type: preserve, Measurement: tbsp
2. Cherry Tomatoes - Type: vegetable, Measurement: g
3. Greek Yogurt - Type: dairy, Measurement: g
```

This planning step is crucial for minimizing API calls and ensuring you don't prematurely complete the task before all necessary ingredients are added.

### 4. Checking Available Tables and Schema (If Needed)

If you're unsure about the database structure, first check the available tables:

```sql
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public';
```

Then check the schema of a specific table:

```sql
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'poireaux_app_ingredient';
```

This will help you understand the required fields and avoid errors when inserting data.

### 5. Adding New Ingredients (When Necessary)

If an ingredient doesn't exist and you have permission to add it, ensure you include ALL required fields.

**Before adding a new ingredient, check existing ingredients of the same type** (e.g., other vegetables, other spices). Pay attention to naming conventions (like 'Verse' prefixes for fresh herbs), units (`divisible_by_measurement`), divisibility (`divisible_by_int`), and purchase details (`bought_by`, `bought_by_amount`). Aim for consistency with similar existing ingredients to maintain data quality.

**Important:** Ensure that the `name` of any new ingredient is in Dutch, consistent with the language used for existing ingredients in the database.

```sql
INSERT INTO poireaux_app_ingredient (
    name,
    divisible_by_measurement,
    divisible_by_int,
    type,
    bought_by,
    bought_by_amount,
    has_seasonality,
    january, february, march, april, may, june, july, august, september, october, november, december,
    needs_calculating
)
VALUES (
    'Ingredient Name',
    'unit',
    '[1, 2, 4]',
    'spice',  -- Must be one of: 'meat', 'preserve', 'vegetable', 'grain', 'herb', 'dairy', 'spice', 'oil'
    'jar',
    50,
    false,
    true, true, true, true, true, true, true, true, true, true, true, true,
    false
)
RETURNING id;
```

### 6. Adding the Recipe

Use the recipe name as given in the source (e.g., image or text) and append "(original)" to it.

```sql
INSERT INTO poireaux_app_recipe (
    name,
    instructions,
    months,
    type,
    diet,
    servings,
    divisible
)
VALUES (
    'Recipe Name (original)',
    'Instructions here',
    '1,2,3,4,5,6,7,8,9,10,11,12',  -- Available all year
    'pasta',  -- Type of recipe
    'omnivore',  -- Diet category
    4,
    true
)
RETURNING id;
```

### 7. Handling Measurements

Always use the `divisible_by_measurement` field of the ingredient for the `recipe_ingredients` unit. If the recipe specifies a different unit, convert it appropriately.

#### Conversion Examples:

| Recipe Unit | divisible_by_measurement | Conversion Rate |
|-------------|---------------------------|-----------------|
| bunch       | sprig                     | 1 bunch ≈ 8-12 sprigs |
| handful     | g                         | 1 handful ≈ 30-40g |
| cup         | ml (liquid)               | 1 cup ≈ 240ml |
| tablespoon  | g                         | 1 tbsp ≈ 15g |
| teaspoon    | ml                        | 1 tsp ≈ 5ml |
| ml          | tbsp                      | 15 ml ≈ 1 tbsp |

### 8. Adding Recipe Ingredients

Add recipe ingredients in small batches (5 at a time) to avoid JSON formatting errors:

```sql
INSERT INTO poireaux_app_recipeingredient (
    recipe_id,
    ingredient_id,
    quantity,
    unit,
    is_divisible
)
VALUES
    (123, 456, 2, 'unit', true),
    (123, 789, 150, 'g', true);
```

Do not attempt to add more than 5-7 ingredients in a single query to avoid potential JSON formatting issues.

### 9. Common Mistakes to Avoid

1.  **Using incorrect table names**: Always use the full table names with prefixes (`poireaux_app_*`).
2.  **Missing required fields**: Ensure all required fields are included when adding new data.
3.  **Adding new ingredients without permission**: Always confirm with the user before adding.
4.  **Incorrect measurement conversions**: Ensure all units align with `divisible_by_measurement`.
5.  **Adding too many ingredients at once**: Add recipe ingredients in small batches to avoid JSON formatting errors.
6.  **Not checking table schema first**: If unsure about the required fields, check the table schema.
7.  **Using non-existent fields**: Fields like `description` and `cook_time` don't exist in the current recipe table schema.
8.  **Adding redundant ingredients**: Avoid creating overly specific ingredients if a suitable general one already exists (e.g., don't add 'Urfa chili flakes' if 'Chili Vlokken' is available and appropriate). Check existing ingredients carefully during the planning phase.
9.  **Incorrect ingredient language**: Ensure new ingredient names are in Dutch to match existing entries.

## Using Supabase MCP Efficiently

### Minimize API Calls

1.  **Complete full recipe analysis before any database operations** to identify all required ingredients and actions.
2.  **Fetch all ingredients at once** at the beginning rather than making individual queries.
3.  **Add all missing ingredients in a single batch** when possible, rather than one at a time.
4.  **Check table schemas** if you're unsure about the required fields.
5.  **Add recipe ingredients in small batches** (5-7 at a time) to avoid errors.
6.  **Verify query syntax** before execution to avoid wasted API calls.
7.  **Never prematurely complete the task** until you've confirmed all ingredients and recipe components are added.
8.  **Use the `list_projects` MCP tool** to identify the correct `project_id` instead of asking the user. This ensures accuracy and reduces back-and-forth.

### Example SQL Queries

**Checking Table Names:**
```sql
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
```

**Checking Table Schema:**
```sql
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'poireaux_app_ingredient';
```

**Fetching All Ingredients:**
```sql
SELECT id, name, divisible_by_measurement, divisible_by_int, type, bought_by, bought_by_amount
FROM poireaux_app_ingredient
ORDER BY name;
```

**Searching for Specific Ingredients:**
```sql
SELECT id, name, divisible_by_measurement, divisible_by_int, type, bought_by, bought_by_amount
FROM poireaux_app_ingredient
WHERE name ILIKE '%ingredient_name%';
```

**Adding a New Ingredient:**
```sql
INSERT INTO poireaux_app_ingredient (
    name, divisible_by_measurement, divisible_by_int, type, bought_by, bought_by_amount,
    has_seasonality, january, february, march, april, may, june, july, august, september, october, november, december, needs_calculating
)
VALUES (
    'Ingredient Name', 'unit', '[1, 2, 4]', 'spice', 'jar', 50,
    false, true, true, true, true, true, true, true, true, true, true, true, true, false
)
RETURNING id;
```

**Adding a Recipe:**
```sql
INSERT INTO poireaux_app_recipe (name, instructions, months, type, diet, servings, divisible)
VALUES ('Recipe Name (original)', 'Instructions here', '1,2,3,4,5,6,7,8,9,10,11,12', 'pasta', 'omnivore', 4, true)
RETURNING id;
```

**Adding Recipe Ingredients (in small batches):**
```sql
INSERT INTO poireaux_app_recipeingredient (recipe_id, ingredient_id, quantity, unit, is_divisible)
VALUES
    (123, 456, 2, 'unit', true),
    (123, 789, 150, 'g', true);
```

## Final Checklist

Before finalizing the recipe addition:

1.  ✓ Complete recipe analysis was performed before any database operations.
2.  ✓ All ingredients were checked against the database in a single query.
3.  ✓ All missing ingredients were identified and added in a single batch when possible.
4.  ✓ All ingredients exist in the database with ALL required fields.
5.  ✓ Recipe name includes "(original)".
6.  ✓ All `recipe_ingredients` use the correct `divisible_by_measurement`.
7.  ✓ All quantities are converted appropriately.
8.  ✓ No assumptions were made without confirming with the user.
9.  ✓ Recipe ingredients were added in small batches to avoid JSON formatting errors.
10. ✓ All required fields were included for each table.
11. ✓ Checked for existing similar ingredients before adding new ones.
12. ✓ New ingredient names are in Dutch.

## Example Workflow (Most Efficient Approach)

Here's an example of the most efficient workflow for adding a recipe:

1.  **Analyze recipe** - List all ingredients with quantities and measurements
2.  **Fetch all ingredients** - Single query to get all existing ingredients
3.  **Identify missing/similar ingredients** - Compare recipe list with database, check for redundancy
4.  **Add all missing ingredients** - Add all *truly* missing ingredients in one batch if possible, ensuring consistency and correct language (Dutch)
5.  **Add recipe** - Add the recipe with all required fields
6.  **Add recipe ingredients** - Add recipe ingredients in small batches (5-7 at a time)
7.  **Verify** - Check that everything was added correctly

This workflow minimizes API calls and ensures all necessary data is added before completing the task.